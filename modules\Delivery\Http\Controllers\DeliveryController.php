<?php

namespace Modules\Delivery\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Modules\Delivery\Services\DeliveryService;
use Modules\Delivery\Services\DeliveryReviewService;
use Modules\Delivery\Entities\DeliveryAssignment;
use Modules\Delivery\Http\Requests\UpdateDeliveryStatusRequest;
use Modules\Delivery\Http\Requests\CreateDeliveryReviewRequest;
use Modules\Delivery\Http\Resources\DeliveryAssignmentResource;
use Modules\Delivery\Http\Resources\DeliveryAssignmentCollection;
use Modules\Delivery\Http\Resources\DeliveryReviewResource;
use Modules\Delivery\Http\Resources\DeliveryReviewCollection;
use Modules\Delivery\Http\Requests\CreateDeliveryAssignmentRequest;
use Modules\Delivery\Http\Requests\UpdateDeliveryAssignmentRequest;

class DeliveryController extends Controller
{
    public function __construct(
        private DeliveryService $deliveryService,
        private DeliveryReviewService $reviewService
    ) {}

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Get delivery assignments with filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'status', 'personnel_id', 'branch_id', 
                'date_from', 'date_to', 'zone_id'
            ]);
            
            $perPage = $request->get('per_page', 15);
            
            $assignments = $this->deliveryService->getDeliveryAssignments($filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAssignmentCollection($assignments),
                'meta' => [
                    'current_page' => $assignments->currentPage(),
                    'last_page' => $assignments->lastPage(),
                    'per_page' => $assignments->perPage(),
                    'total' => $assignments->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery assignments',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create new delivery assignment
     */
    public function store(CreateDeliveryAssignmentRequest $request): JsonResponse
    {
        try {
            $assignment = $this->deliveryService->createDeliveryAssignment(
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Delivery assignment created successfully',
                'data' => new DeliveryAssignmentResource($assignment),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create delivery assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific delivery assignment
     */
    public function show(int $id): JsonResponse
    {
        try {
            $assignment = $this->deliveryService->getDeliveryAssignments(['id' => $id], 1);
            
            if ($assignment->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery assignment not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new DeliveryAssignmentResource($assignment->first()),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery assignment
     */
    public function update(UpdateDeliveryAssignmentRequest $request, int $id): JsonResponse
    {
        try {
            $assignment = $this->deliveryService->updateDeliveryAssignment($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Delivery assignment updated successfully',
                'data' => new DeliveryAssignmentResource($assignment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update delivery assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery status
     */
    public function updateStatus(UpdateDeliveryStatusRequest $request, int $id): JsonResponse
    {
        try {
            $assignment = $this->deliveryService->updateDeliveryStatus(
                $id,
                $request->validated()['status'],
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Delivery status updated successfully',
                'data' => new DeliveryAssignmentResource($assignment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update delivery status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Auto-assign delivery to available personnel
     */
    public function autoAssign(Request $request): JsonResponse
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
        ]);
        $r= DeliveryAssignment::where('order_id', $request->order_id)->exists();
        if($r){
            return response()->json([
                'success' => false,
                'message' => 'Delivery assignment already exists',
            ], 400);
        }

        try {
            $assignment = $this->deliveryService->createDeliveryAssignment([
                'order_id' => $request->order_id,
                // Personnel will be auto-assigned
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Delivery auto-assigned successfully',
                'data' => new DeliveryAssignmentResource($assignment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to auto-assign delivery',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate delivery fee for coordinates
     */
    public function calculateFee(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $feeData = $this->deliveryService->calculateDeliveryFee(
                $request->latitude,
                $request->longitude,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $feeData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate delivery fee',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'date_from', 'date_to', 'branch_id', 'personnel_id'
            ]);

            $statistics = $this->deliveryService->getDeliveryStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get active deliveries for personnel
     */
    public function activeDeliveries(Request $request): JsonResponse
    {
        $request->validate([
            'personnel_id' => 'required|exists:delivery_personnel,id',
        ]);

        try {
            $deliveries = $this->deliveryService->getActiveDeliveriesForPersonnel(
                $request->personnel_id
            );

            return response()->json([
                'success' => true,
                'data' => DeliveryAssignmentResource::collection($deliveries),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active deliveries',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get customer deliveries
     */
    public function getCustomerDeliveries(Request $request): JsonResponse
    {
        try {
            $customerId = Auth::id();
            $filters = array_merge($request->only(['status', 'date_from', 'date_to']), [
                'customer_id' => $customerId
            ]);
            
            $perPage = $request->get('per_page', 15);
            $assignments = $this->deliveryService->getDeliveryAssignments($filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAssignmentCollection($assignments),
                'meta' => [
                    'current_page' => $assignments->currentPage(),
                    'last_page' => $assignments->lastPage(),
                    'per_page' => $assignments->perPage(),
                    'total' => $assignments->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customer deliveries',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel assignments
     */
    public function getPersonnelAssignments(Request $request): JsonResponse
    {
        try {
            $personnelId = Auth::user()->delivery_personnel_id ?? Auth::id();
            $filters = array_merge($request->only(['status', 'date_from', 'date_to']), [
                'personnel_id' => $personnelId
            ]);
            
            $perPage = $request->get('per_page', 15);
            $assignments = $this->deliveryService->getDeliveryAssignments($filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAssignmentCollection($assignments),
                'meta' => [
                    'current_page' => $assignments->currentPage(),
                    'last_page' => $assignments->lastPage(),
                    'per_page' => $assignments->perPage(),
                    'total' => $assignments->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve personnel assignments',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel performance metrics
     */
    public function getMyPerformance(Request $request): JsonResponse
    {
        try {
            $personnelId = Auth::user()->delivery_personnel_id ?? Auth::id();
            $filters = $request->only(['date_from', 'date_to']);
            
            // This would typically use a dedicated service method
            $performance = [
                'total_deliveries' => 0,
                'completed_deliveries' => 0,
                'average_rating' => 0,
                'total_earnings' => 0,
                'completion_rate' => 0,
            ];

            return response()->json([
                'success' => true,
                'data' => $performance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve performance data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel earnings
     */
    public function getMyEarnings(Request $request): JsonResponse
    {
        try {
            $personnelId = Auth::user()->delivery_personnel_id ?? Auth::id();
            $filters = $request->only(['date_from', 'date_to']);
            
            // This would typically use a dedicated service method
            $earnings = [
                'total_earnings' => 0,
                'delivery_fees' => 0,
                'tips' => 0,
                'bonuses' => 0,
                'period' => $filters,
            ];

            return response()->json([
                'success' => true,
                'data' => $earnings,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve earnings data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get monthly report
     */
    public function getMonthlyReport(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['month', 'year', 'branch_id']);
            
            // This would typically use a dedicated analytics service
            $report = [
                'total_deliveries' => 0,
                'total_revenue' => 0,
                'average_delivery_time' => 0,
                'customer_satisfaction' => 0,
                'top_performers' => [],
            ];

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve monthly report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle payment webhook
     */
    public function handlePaymentWebhook(Request $request): JsonResponse
    {
        try {
            // This would typically validate webhook signature and process payment status
            $paymentData = $request->all();
            
            return response()->json([
                'success' => true,
                'message' => 'Payment webhook processed successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment webhook',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Review-related methods (placeholder implementations)
    
    /**
     * Get delivery reviews
     */
    public function getDeliveryReviews(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['rating', 'verified', 'personnel_id', 'customer_id']);
            $perPage = $request->input('per_page', 15);
            
            $reviews = $this->reviewService->getReviews($filters, $perPage);
            
            return response()->json([
                'success' => true,
                'data' => new DeliveryReviewCollection($reviews),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve reviews',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Get a specific review by ID
     */
    public function getReviewById(Request $request, int $id): JsonResponse
    {
        try {
            $review = $this->reviewService->getReviewById($id);
            
            return response()->json([
                'success' => true,
                'data' => new DeliveryReviewResource($review),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve review',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create delivery review
     */
    public function createDeliveryReview(CreateDeliveryReviewRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();
            $review = $this->reviewService->createReview($validatedData);
            
            return response()->json([
                'success' => true,
                'message' => 'Review created successfully',
                'data' => new DeliveryReviewResource($review),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create review',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Verify review
     */
    public function verifyReview(Request $request, int $id): JsonResponse
    {
        try {
            // Only admin or manager should be able to verify reviews
            // This would typically check permissions
            
            $review = $this->reviewService->verifyReview($id);
            
            return response()->json([
                'success' => true,
                'message' => 'Review verified successfully',
                'data' => new DeliveryReviewResource($review),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to verify review',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel reviews
     */
    public function getPersonnelReviews(Request $request, int $personnelId): JsonResponse
    {
        try {
            $filters = $request->only(['rating', 'verified']);
            $perPage = $request->input('per_page', 15);
            
            $reviews = $this->reviewService->getPersonnelReviews($personnelId, $filters, $perPage);
            
            return response()->json([
                'success' => true,
                'data' => new DeliveryReviewCollection($reviews),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve personnel reviews',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Tip-related methods (placeholder implementations)
    
    /**
     * Get delivery tips
     */
    public function getDeliveryTips(Request $request): JsonResponse
    {
        try {
            // Placeholder implementation
            return response()->json([
                'success' => true,
                'data' => [],
                'message' => 'Tips functionality not yet implemented',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tips',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create delivery tip
     */
    public function createDeliveryTip(Request $request): JsonResponse
    {
        try {
            // Placeholder implementation
            return response()->json([
                'success' => true,
                'message' => 'Tip functionality not yet implemented',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create tip',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process payment for tip
     */
    public function processPayment(Request $request, int $id): JsonResponse
    {
        try {
            // Placeholder implementation
            return response()->json([
                'success' => true,
                'message' => 'Tip payment processing not yet implemented',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process tip payment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel tips
     */
    public function getPersonnelTips(Request $request, int $personnelId): JsonResponse
    {
        try {
            // Placeholder implementation
            return response()->json([
                'success' => true,
                'data' => [],
                'message' => 'Personnel tips functionality not yet implemented',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve personnel tips',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery personnel (the method that was causing the original error)
     */
    public function getDeliveryPersonnel(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'status', 'branch_id', 'vehicle_type', 'is_available'
            ]);
            
            $perPage = $request->get('per_page', 15);
            
            // This would typically use the delivery service to get personnel
            // For now, returning a placeholder response
            return response()->json([
                'success' => true,
                'data' => [],
                'message' => 'Personnel retrieval functionality moved to DeliveryPersonnelController',
                'meta' => [
                    'current_page' => 1,
                    'last_page' => 1,
                    'per_page' => $perPage,
                    'total' => 0,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}