<?php return array (
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'reverb' => 
      array (
        'driver' => 'reverb',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'host' => NULL,
          'port' => 443,
          'scheme' => 'https',
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'cluster' => NULL,
          'host' => 'api-mt1.pusher.com',
          'port' => 443,
          'scheme' => 'https',
          'encrypted' => true,
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'concurrency' => 
  array (
    'default' => 'process',
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => '12',
      'verify' => true,
      'limit' => NULL,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
      'verify' => true,
    ),
    'rehash_on_login' => true,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\resources\\views',
    ),
    'compiled' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\framework\\views',
  ),
  'app' => 
  array (
    'name' => 'Laravel',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://127.0.0.1:8000',
    'frontend_url' => 'http://localhost:3000',
    'asset_url' => NULL,
    'timezone' => 'UTC',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'cipher' => 'AES-256-CBC',
    'key' => 'base64:FC9PmgGJST/iOeAwd2f+/tL5xUPTXomBF2rklf2t/ck=',
    'previous_keys' => 
    array (
    ),
    'maintenance' => 
    array (
      'driver' => 'file',
      'store' => 'database',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
      6 => 'Illuminate\\Cookie\\CookieServiceProvider',
      7 => 'Illuminate\\Database\\DatabaseServiceProvider',
      8 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      9 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      10 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      11 => 'Illuminate\\Hashing\\HashServiceProvider',
      12 => 'Illuminate\\Mail\\MailServiceProvider',
      13 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      14 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      15 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      16 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      17 => 'Illuminate\\Queue\\QueueServiceProvider',
      18 => 'Illuminate\\Redis\\RedisServiceProvider',
      19 => 'Illuminate\\Session\\SessionServiceProvider',
      20 => 'Illuminate\\Translation\\TranslationServiceProvider',
      21 => 'Illuminate\\Validation\\ValidationServiceProvider',
      22 => 'Illuminate\\View\\ViewServiceProvider',
      23 => 'App\\Providers\\AppServiceProvider',
      24 => 'Modules\\Customer\\Providers\\CustomerServiceProvider',
      25 => 'Modules\\Reports\\Providers\\ReportsServiceProvider',
      26 => 'Modules\\Settings\\Providers\\SettingsServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Concurrency' => 'Illuminate\\Support\\Facades\\Concurrency',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Context' => 'Illuminate\\Support\\Facades\\Context',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schedule' => 'Illuminate\\Support\\Facades\\Schedule',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Uri' => 'Illuminate\\Support\\Uri',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'sanctum',
        'provider' => 'users',
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'cache',
        'lock_connection' => NULL,
        'lock_table' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\framework/cache/data',
        'lock_path' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'laravel-cache-',
  ),
  'cores' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
      2 => 'auth/*',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => 'http://localhost:3000',
      1 => 'http://127.0.0.1:3000',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => 'Accept',
      1 => 'Authorization',
      2 => 'Content-Type',
      3 => 'X-Requested-With',
      4 => 'X-CSRF-TOKEN',
      5 => 'X-XSRF-TOKEN',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => true,
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
      2 => 'auth/*',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => 'http://localhost:3000',
      1 => 'http://127.0.0.1:3000',
      2 => 'http://***********:3000',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => 'Accept',
      1 => 'Authorization',
      2 => 'Content-Type',
      3 => 'X-Requested-With',
      4 => 'X-CSRF-TOKEN',
      5 => 'X-XSRF-TOKEN',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => true,
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'returant_pos',
        'prefix' => '',
        'foreign_key_constraints' => true,
        'busy_timeout' => NULL,
        'journal_mode' => NULL,
        'synchronous' => NULL,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'returant_pos',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'mariadb' => 
      array (
        'driver' => 'mariadb',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'returant_pos',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'returant_pos',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'returant_pos',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 
    array (
      'table' => 'migrations',
      'update_date_on_publish' => true,
    ),
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'laravel-database-',
        'persistent' => false,
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'datatables' => 
  array (
    'search' => 
    array (
      'smart' => true,
      'multi_term' => true,
      'case_insensitive' => true,
      'use_wildcards' => false,
      'starts_with' => false,
    ),
    'index_column' => 'DT_RowIndex',
    'engines' => 
    array (
      'eloquent' => 'Yajra\\DataTables\\EloquentDataTable',
      'query' => 'Yajra\\DataTables\\QueryDataTable',
      'collection' => 'Yajra\\DataTables\\CollectionDataTable',
      'resource' => 'Yajra\\DataTables\\ApiResourceDataTable',
    ),
    'builders' => 
    array (
    ),
    'nulls_last_sql' => ':column :direction NULLS LAST',
    'error' => NULL,
    'columns' => 
    array (
      'excess' => 
      array (
        0 => 'rn',
        1 => 'row_num',
      ),
      'escape' => '*',
      'raw' => 
      array (
        0 => 'action',
      ),
      'blacklist' => 
      array (
        0 => 'password',
        1 => 'remember_token',
      ),
      'whitelist' => '*',
    ),
    'json' => 
    array (
      'header' => 
      array (
      ),
      'options' => 0,
    ),
    'callback' => 
    array (
      0 => '$',
      1 => '$.',
      2 => 'function',
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\app/private',
        'serve' => true,
        'throw' => false,
        'report' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\app/public',
        'url' => 'http://127.0.0.1:8000/storage',
        'visibility' => 'public',
        'throw' => false,
        'report' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
        'report' => false,
      ),
    ),
    'links' => 
    array (
      'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\public\\storage' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\app/public',
    ),
  ),
  'logging' => 
  array (
    'default' => 'daily',
    'deprecations' => 
    array (
      'channel' => NULL,
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\logs/laravel.log',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => '14',
        'replace_placeholders' => true,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'handler_with' => 
        array (
          'stream' => 'php://stderr',
        ),
        'formatter' => NULL,
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
        'facility' => 8,
        'replace_placeholders' => true,
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'log',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'scheme' => NULL,
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '2525',
        'username' => NULL,
        'password' => NULL,
        'timeout' => NULL,
        'local_domain' => '127.0.0.1',
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'resend' => 
      array (
        'transport' => 'resend',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
        'retry_after' => 60,
      ),
      'roundrobin' => 
      array (
        'transport' => 'roundrobin',
        'mailers' => 
        array (
          0 => 'ses',
          1 => 'postmark',
        ),
        'retry_after' => 60,
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Laravel',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'queue' => 
  array (
    'default' => 'database',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'batching' => 
    array (
      'database' => 'mysql',
      'table' => 'job_batches',
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => '127.0.0.1:8000',
    ),
    'guard' => 
    array (
      0 => 'web',
    ),
    'expiration' => NULL,
    'token_prefix' => '',
    'middleware' => 
    array (
      'authenticate_session' => 'Laravel\\Sanctum\\Http\\Middleware\\AuthenticateSession',
      'encrypt_cookies' => 'Illuminate\\Cookie\\Middleware\\EncryptCookies',
      'validate_csrf_token' => 'Illuminate\\Foundation\\Http\\Middleware\\ValidateCsrfToken',
    ),
  ),
  'services' => 
  array (
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'resend' => 
    array (
      'key' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'slack' => 
    array (
      'notifications' => 
      array (
        'bot_user_oauth_token' => NULL,
        'channel' => NULL,
      ),
    ),
  ),
  'session' => 
  array (
    'driver' => 'database',
    'lifetime' => 525600,
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'D:\\my dehive work\\resturant-pos - Copy\\epis - Copy\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'laravel_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
    'partitioned' => false,
  ),
  'permission' => 
  array (
    'models' => 
    array (
      'permission' => 'Spatie\\Permission\\Models\\Permission',
      'role' => 'Spatie\\Permission\\Models\\Role',
    ),
    'table_names' => 
    array (
      'roles' => 'roles',
      'permissions' => 'permissions',
      'model_has_permissions' => 'model_has_permissions',
      'model_has_roles' => 'model_has_roles',
      'role_has_permissions' => 'role_has_permissions',
    ),
    'column_names' => 
    array (
      'role_pivot_key' => NULL,
      'permission_pivot_key' => NULL,
      'model_morph_key' => 'model_id',
      'team_foreign_key' => 'team_id',
    ),
    'register_permission_check_method' => true,
    'register_octane_reset_listener' => false,
    'events_enabled' => false,
    'teams' => false,
    'team_resolver' => 'Spatie\\Permission\\DefaultTeamResolver',
    'use_passport_client_credentials' => false,
    'display_permission_in_exception' => false,
    'display_role_in_exception' => false,
    'enable_wildcard_permission' => false,
    'cache' => 
    array (
      'expiration_time' => 
      \DateInterval::__set_state(array(
         'from_string' => true,
         'date_string' => '24 hours',
      )),
      'key' => 'spatie.permission.cache',
      'store' => 'default',
    ),
  ),
  'Inventory' => 
  array (
    'stock_levels' => 
    array (
      'low_stock_threshold_percentage' => 20,
      'critical_stock_threshold_percentage' => 10,
      'auto_reorder_enabled' => true,
      'auto_reorder_threshold_percentage' => 15,
      'default_reorder_quantity_days' => 30,
    ),
    'movement_types' => 
    array (
      'inbound' => 
      array (
        'add' => 'Stock Addition',
        'initial_stock' => 'Initial Stock',
        'return' => 'Customer Return',
        'transfer_in' => 'Transfer In',
        'adjustment_in' => 'Positive Adjustment',
      ),
      'outbound' => 
      array (
        'subtract' => 'Stock Reduction',
        'waste' => 'Waste/Spoilage',
        'transfer_out' => 'Transfer Out',
        'consumption' => 'Recipe Consumption',
        'adjustment_out' => 'Negative Adjustment',
      ),
    ),
    'log_actions' => 
    array (
      'stock_update' => 'Stock Updated',
      'stock_add' => 'Stock Added',
      'stock_subtract' => 'Stock Subtracted',
      'low_stock_alert' => 'Low Stock Alert',
      'reorder_suggestion' => 'Reorder Suggested',
      'inventory_count' => 'Physical Count',
      'waste_recorded' => 'Waste Recorded',
      'transfer' => 'Stock Transfer',
      'adjustment' => 'Stock Adjustment',
    ),
    'purchase_order' => 
    array (
      'statuses' => 
      array (
        'draft' => 'Draft',
        'pending' => 'Pending Approval',
        'approved' => 'Approved',
        'sent' => 'Sent to Supplier',
        'partially_received' => 'Partially Received',
        'received' => 'Fully Received',
        'cancelled' => 'Cancelled',
        'rejected' => 'Rejected',
      ),
      'priorities' => 
      array (
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'urgent' => 'Urgent',
      ),
      'auto_generate_po_number' => true,
      'po_number_prefix' => 'PO',
      'po_number_length' => 8,
      'default_payment_terms' => 30,
      'require_approval_above_amount' => 1000.0,
    ),
    'supplier' => 
    array (
      'categories' => 
      array (
        'food_beverage' => 'Food & Beverage',
        'packaging' => 'Packaging',
        'cleaning' => 'Cleaning Supplies',
        'equipment' => 'Equipment',
        'maintenance' => 'Maintenance',
        'office' => 'Office Supplies',
        'other' => 'Other',
      ),
      'default_payment_terms' => 30,
      'default_credit_limit' => 5000.0,
      'rating_scale' => 
      array (
        1 => 'Poor',
        2 => 'Fair',
        3 => 'Good',
        4 => 'Very Good',
        5 => 'Excellent',
      ),
    ),
    'units' => 
    array (
      'weight' => 
      array (
        'kg' => 'Kilogram',
        'g' => 'Gram',
        'lb' => 'Pound',
        'oz' => 'Ounce',
      ),
      'volume' => 
      array (
        'l' => 'Liter',
        'ml' => 'Milliliter',
        'gal' => 'Gallon',
        'qt' => 'Quart',
        'pt' => 'Pint',
        'cup' => 'Cup',
        'fl_oz' => 'Fluid Ounce',
      ),
      'count' => 
      array (
        'pcs' => 'Pieces',
        'box' => 'Box',
        'case' => 'Case',
        'pack' => 'Pack',
        'dozen' => 'Dozen',
      ),
      'length' => 
      array (
        'm' => 'Meter',
        'cm' => 'Centimeter',
        'ft' => 'Foot',
        'in' => 'Inch',
      ),
    ),
    'categories' => 
    array (
      'ingredients' => 'Ingredients',
      'beverages' => 'Beverages',
      'packaging' => 'Packaging',
      'cleaning' => 'Cleaning Supplies',
      'equipment' => 'Equipment',
      'office' => 'Office Supplies',
      'maintenance' => 'Maintenance',
      'other' => 'Other',
    ),
    'waste_reasons' => 
    array (
      'expired' => 'Expired',
      'damaged' => 'Damaged',
      'spoiled' => 'Spoiled',
      'contaminated' => 'Contaminated',
      'overcooked' => 'Overcooked',
      'dropped' => 'Dropped/Spilled',
      'quality_issue' => 'Quality Issue',
      'customer_return' => 'Customer Return',
      'other' => 'Other',
    ),
    'alerts' => 
    array (
      'low_stock_enabled' => true,
      'expiry_alerts_enabled' => true,
      'expiry_alert_days' => 7,
      'overstock_alerts_enabled' => true,
      'overstock_threshold_percentage' => 150,
    ),
    'analytics' => 
    array (
      'default_period_days' => 30,
      'turnover_calculation_period' => 90,
      'abc_analysis_enabled' => true,
      'seasonal_analysis_enabled' => true,
    ),
    'export' => 
    array (
      'formats' => 
      array (
        0 => 'csv',
        1 => 'excel',
        2 => 'pdf',
      ),
      'default_format' => 'csv',
      'max_export_records' => 10000,
    ),
    'import' => 
    array (
      'allowed_formats' => 
      array (
        0 => 'csv',
        1 => 'xlsx',
      ),
      'max_file_size' => 5120,
      'batch_size' => 100,
      'required_columns' => 
      array (
        0 => 'name',
        1 => 'sku',
        2 => 'category',
        3 => 'unit',
        4 => 'current_stock',
        5 => 'minimum_stock_level',
        6 => 'cost_per_unit',
      ),
    ),
    'permissions' => 
    array (
      'view_inventory' => 'View Inventory',
      'create_inventory' => 'Create Inventory Items',
      'edit_inventory' => 'Edit Inventory Items',
      'delete_inventory' => 'Delete Inventory Items',
      'update_stock' => 'Update Stock Levels',
      'view_suppliers' => 'View Suppliers',
      'manage_suppliers' => 'Manage Suppliers',
      'view_purchase_orders' => 'View Purchase Orders',
      'create_purchase_orders' => 'Create Purchase Orders',
      'approve_purchase_orders' => 'Approve Purchase Orders',
      'receive_purchase_orders' => 'Receive Purchase Orders',
      'view_inventory_reports' => 'View Inventory Reports',
      'export_inventory_data' => 'Export Inventory Data',
      'import_inventory_data' => 'Import Inventory Data',
    ),
    'cache' => 
    array (
      'enabled' => true,
      'ttl' => 3600,
      'keys' => 
      array (
        'low_stock_items' => 'inventory:low_stock',
        'dashboard_stats' => 'inventory:dashboard_stats',
        'supplier_performance' => 'inventory:supplier_performance',
      ),
    ),
  ),
  'tenant' => 
  array (
    'defaults' => 
    array (
      'timezone' => 'UTC',
      'currency' => 'USD',
      'language' => 'en',
      'date_format' => 'Y-m-d',
      'time_format' => 'H:i:s',
      'datetime_format' => 'Y-m-d H:i:s',
    ),
    'code' => 
    array (
      'prefix' => 'TNT',
      'length' => 8,
      'characters' => '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    ),
    'subdomain' => 
    array (
      'enabled' => true,
      'domain' => 'localhost',
      'reserved' => 
      array (
        0 => 'www',
        1 => 'api',
        2 => 'admin',
        3 => 'app',
        4 => 'mail',
        5 => 'ftp',
      ),
      'min_length' => 3,
      'max_length' => 20,
    ),
    'subscription' => 
    array (
      'trial_days' => 14,
      'grace_period_days' => 3,
      'auto_suspend_after_days' => 7,
      'billing_cycles' => 
      array (
        0 => 'monthly',
        1 => 'quarterly',
        2 => 'yearly',
      ),
      'default_billing_cycle' => 'monthly',
    ),
    'usage_limits' => 
    array (
      'cache_ttl' => 3600,
      'warning_threshold' => 0.8,
      'soft_limit_threshold' => 0.9,
    ),
    'billing' => 
    array (
      'tax_rate' => 0.1,
      'currency' => 'USD',
      'payment_methods' => 
      array (
        0 => 'credit_card',
        1 => 'bank_transfer',
        2 => 'paypal',
      ),
      'invoice_prefix' => 'INV',
      'retry_failed_payments' => true,
      'max_payment_retries' => 3,
      'retry_interval_days' => 3,
    ),
    'multi_store' => 
    array (
      'max_branches_per_tenant' => 10,
      'sync_menu_across_branches' => true,
      'centralized_inventory' => false,
      'shared_customer_database' => true,
    ),
    'cache' => 
    array (
      'prefix' => 'tenant',
      'ttl' => 
      array (
        'tenant_data' => 3600,
        'subscription_data' => 1800,
        'usage_stats' => 300,
        'billing_data' => 7200,
      ),
    ),
    'features' => 
    array (
      'multi_store' => true,
      'custom_domains' => false,
      'white_labeling' => false,
      'api_access' => true,
      'advanced_reporting' => true,
      'data_export' => true,
    ),
    'validation' => 
    array (
      'tenant_name_max_length' => 100,
      'tenant_code_max_length' => 20,
      'business_hours_format' => 'H:i',
      'phone_regex' => '/^[+]?[0-9\\s\\-\\(\\)]+$/',
      'social_media_domains' => 
      array (
        0 => 'facebook.com',
        1 => 'twitter.com',
        2 => 'instagram.com',
        3 => 'linkedin.com',
        4 => 'youtube.com',
      ),
    ),
    'notifications' => 
    array (
      'subscription_expiry_warning_days' => 
      array (
        0 => 30,
        1 => 7,
        2 => 1,
      ),
      'usage_limit_warnings' => 
      array (
        0 => 80,
        1 => 90,
        2 => 95,
      ),
      'billing_failure_notifications' => true,
      'new_tenant_notifications' => true,
    ),
    'security' => 
    array (
      'data_isolation' => true,
      'tenant_context_required' => true,
      'cross_tenant_access_prevention' => true,
      'audit_trail' => true,
    ),
  ),
  'delivery' => 
  array (
    'default_delivery_radius' => 10,
    'max_concurrent_deliveries' => 3,
    'auto_assignment_enabled' => true,
    'tracking_interval' => 30,
    'delivery_timeout' => 120,
    'vehicle_types' => 
    array (
      0 => 'motorcycle',
      1 => 'bicycle',
      2 => 'car',
      3 => 'scooter',
      4 => 'walking',
    ),
    'delivery_statuses' => 
    array (
      0 => 'pending',
      1 => 'assigned',
      2 => 'picked_up',
      3 => 'in_transit',
      4 => 'delivered',
      5 => 'failed',
      6 => 'cancelled',
    ),
    'personnel_statuses' => 
    array (
      0 => 'active',
      1 => 'inactive',
      2 => 'busy',
      3 => 'offline',
    ),
    'payment_methods' => 
    array (
      0 => 'cash',
      1 => 'card',
      2 => 'digital_wallet',
      3 => 'bank_transfer',
    ),
    'tip_percentages' => 
    array (
      0 => 5,
      1 => 10,
      2 => 15,
      3 => 20,
    ),
    'analytics' => 
    array (
      'cache_duration' => 3600,
      'metrics_retention_days' => 90,
    ),
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
