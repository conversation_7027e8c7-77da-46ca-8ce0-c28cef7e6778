@extends('layouts.master')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- Material Design Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
<style>
:root {
    --primary: #2563eb;
    --primary-hover: #1d4ed8;
    --success: #059669;
    --success-hover: #047857;
    --warning: #d97706;
    --warning-hover: #b45309;
    --danger: #dc2626;
    --danger-hover: #b91c1c;
    --info: #0891b2;
    --info-hover: #0e7490;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
}

.pos-dashboard {
    background-color: var(--gray-50);
    min-height: 100vh;
    padding: 2rem 1rem;
}

/* Header Section */
.dashboard-header {
    background-color: var(--white);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    border: 1px solid var(--gray-200);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
}

.header-info p {
    color: var(--gray-600);
    margin: 0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-primary-custom {
    background-color: var(--primary);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.btn-primary-custom:hover {
    background-color: var(--primary-hover);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-1px);
}

.btn-outline-custom {
    background-color: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.btn-outline-custom:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
    text-decoration: none;
    border-color: var(--gray-400);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.stats-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--white);
}

.stats-icon.primary { background-color: var(--primary); }
.stats-icon.success { background-color: var(--success); }
.stats-icon.warning { background-color: var(--warning); }
.stats-icon.info { background-color: var(--info); }

.stats-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

/* Main Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 1.5rem 0;
}

/* Recent Orders */
.recent-orders {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.order-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.order-item:hover {
    border-color: var(--gray-300);
    background-color: var(--gray-50);
}

.order-avatar {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background-color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.order-details {
    flex: 1;
    min-width: 0;
}

.order-number {
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
}

.order-meta {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin: 0;
}

.order-status-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.order-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-confirmed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-preparing {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-ready {
    background-color: #e0e7ff;
    color: #5b21b6;
}

.status-served {
    background-color: #f3e8ff;
    color: #6b21a8;
}

.btn-sm {
    padding: 0.5rem;
    border-radius: 6px;
    border: none;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-success {
    background-color: var(--success);
    color: var(--white);
}

.btn-success:hover {
    background-color: var(--success-hover);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: var(--white);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0 0 0.5rem 0;
}

.empty-description {
    color: var(--gray-500);
    margin: 0 0 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pos-dashboard {
        padding: 1rem 0.5rem;
    }
    
    .dashboard-header {
        padding: 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .order-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Transaction Status Badges */
.transaction-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    margin-left: 0.5rem;
}

.transaction-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.transaction-partial {
    background-color: #dbeafe;
    color: #1e40af;
}

.transaction-paid {
    background-color: #d1fae5;
    color: #065f46;
}

.transaction-failed {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Payment Modal */
.payment-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.payment-modal.show {
    display: flex;
}

.payment-modal-content {
    background-color: var(--white);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

.payment-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payment-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.payment-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.payment-modal-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.payment-modal-body {
    padding: 1.5rem;
}

.payment-form-group {
    margin-bottom: 1rem;
}

.payment-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.payment-form-input,
.payment-form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.payment-form-input:focus,
.payment-form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.payment-summary {
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.payment-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.payment-summary-row:last-child {
    margin-bottom: 0;
    padding-top: 0.5rem;
    border-top: 1px solid var(--gray-200);
    font-weight: 600;
}

.payment-modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-payment-cancel {
    background-color: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-payment-cancel:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-400);
}

.btn-payment-submit {
    background-color: var(--primary);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-payment-submit:hover {
    background-color: var(--primary-hover);
}

.btn-payment-submit:disabled {
    background-color: var(--gray-400);
    cursor: not-allowed;
}

/* Loading Animation */
.loading-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
@endpush



@section('content')
<div class="pos-dashboard">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="header-info">
                <h1>Point of Sale Dashboard</h1>
                <p>Manage your restaurant orders and track daily performance</p>
            </div>
            <div class="header-actions">
                <a href="{{ route('pos.create') }}" class="btn-primary-custom">
                    <i class="mdi mdi-plus"></i>
                    New Order
                </a>
                <a href="{{ route('orders.index') }}" class="btn-outline-custom">
                    <i class="mdi mdi-format-list-bulleted"></i>
                    View All Orders
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon primary">
                    <i class="mdi mdi-receipt"></i>
                </div>
                <div class="stats-label">Today's Orders</div>
            </div>
            <div class="stats-value">{{ $recentOrders->where('created_at', '>=', today())->count() }}</div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon warning">
                    <i class="mdi mdi-clock-outline"></i>
                </div>
                <div class="stats-label">Pending Orders</div>
            </div>
            <div class="stats-value">{{ $recentOrders->where('status', 'pending')->count() }}</div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon info">
                    <i class="mdi mdi-chef-hat"></i>
                </div>
                <div class="stats-label">Preparing</div>
            </div>
            <div class="stats-value">{{ $recentOrders->where('status', 'preparing')->count() }}</div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon success">
                    <i class="mdi mdi-currency-usd"></i>
                </div>
                <div class="stats-label">Today's Revenue</div>
            </div>
            <div class="stats-value">${{ number_format($recentOrders->where('created_at', '>=', today())->sum('total_amount'), 0) }}</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content-grid">
        <!-- Recent Orders -->
        <div class="recent-orders">
            <div class="section-header">
                <h3 class="section-title">Recent Orders</h3>
                <a href="{{ route('orders.index') }}" class="btn-outline-custom">
                    <i class="mdi mdi-arrow-right"></i>
                    View All
                </a>
            </div>

            @if($recentOrders->count() > 0)
                <div class="order-list">
                    @foreach($recentOrders as $order)
                    <div class="order-item">
                        <div class="order-avatar">
                            #{{ substr($order->order_number, -3) }}
                        </div>
                        <div class="order-details">
                            <h4 class="order-number">#{{ $order->order_number }}</h4>
                            <p class="order-meta">
                                {{ $order->customer ? $order->customer->first_name . ' ' . $order->customer->last_name : 'Walk-in Customer' }} • 
                                {{ $order->orderItems->count() }} items • 
                                ${{ number_format($order->total_amount, 2) }} • 
                                {{ $order->created_at->format('h:i A') }}
                            </p>
                            <div class="order-status-row">
                                <span class="status-badge status-{{ $order->status }}">
                                    {{ ucfirst($order->status) }}
                                </span>
                                @if($order->transaction)
                                    <span class="transaction-status transaction-{{ $order->transaction->status }}">
                                        @if($order->transaction->status === 'pending')
                                            Unpaid
                                        @elseif($order->transaction->status === 'partial')
                                            Partial: ${{ number_format($order->transaction->paid_amount, 2) }}/${{ number_format($order->transaction->total_amount, 2) }}
                                        @elseif($order->transaction->status === 'completed')
                                            Paid
                                        @else
                                            {{ ucfirst($order->transaction->status) }}
                                        @endif
                                    </span>
                                @else
                                    <span class="transaction-status transaction-pending">
                                        No Transaction
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="order-actions">
                            @if($order->status === 'pending')
                                <button class="btn-sm btn-success confirm-order-btn" data-order-id="{{ $order->id }}" title="Confirm Order">
                                    <i class="mdi mdi-check"></i>
                                </button>
                            @endif
                            
                            @if($order->transaction && $order->transaction->status !== 'completed')
                                <button class="btn-sm btn-primary pay-order-btn" data-order-id="{{ $order->id }}" title="Process Payment">
                                    <i class="mdi mdi-credit-card"></i>
                                </button>
                            @endif
                            
                            <a href="{{ route('orders.show', $order->id) }}" class="btn-sm btn-outline-primary" title="View Details">
                                <i class="mdi mdi-eye"></i>
                            </a>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="mdi mdi-receipt"></i>
                    </div>
                    <h3 class="empty-title">No Orders Yet</h3>
                    <p class="empty-description">Create your first order to get started</p>
                    <a href="{{ route('pos.create') }}" class="btn-primary-custom">
                        <i class="mdi mdi-plus"></i>
                        Create First Order
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div id="payment-modal" class="payment-modal">
    <div class="payment-modal-content">
        <div class="payment-modal-header">
            <h3 class="payment-modal-title">Process Payment</h3>
            <button type="button" class="payment-modal-close" id="close-payment-modal">
                <i class="mdi mdi-close"></i>
            </button>
        </div>
        
        <form id="payment-form">
            <div class="payment-modal-body">
                <div class="payment-summary">
                    <div class="payment-summary-row">
                        <span>Order Number:</span>
                        <span id="payment-order-number">-</span>
                    </div>
                    <div class="payment-summary-row">
                        <span>Total Amount:</span>
                        <span id="payment-total-amount">$0.00</span>
                    </div>
                    <div class="payment-summary-row">
                        <span>Paid Amount:</span>
                        <span id="payment-paid-amount">$0.00</span>
                    </div>
                    <div class="payment-summary-row">
                        <span>Remaining:</span>
                        <span id="payment-remaining-amount">$0.00</span>
                    </div>
                </div>

                <div class="payment-form-group">
                    <label for="payment-method" class="payment-form-label">Payment Method</label>
                    <select id="payment-method" name="payment_method_id" class="payment-form-select" required>
                        <option value="">Select Payment Method</option>
                    </select>
                </div>

                <div class="payment-form-group">
                    <label for="payment-amount" class="payment-form-label">Payment Amount</label>
                    <input type="number" id="payment-amount" name="amount" class="payment-form-input" 
                           step="0.01" min="0.01" placeholder="0.00" required>
                </div>

                <div class="payment-form-group">
                    <label for="payment-reference" class="payment-form-label">Reference Number (Optional)</label>
                    <input type="text" id="payment-reference" name="reference_number" class="payment-form-input" 
                           placeholder="Transaction reference">
                </div>

                <div class="payment-form-group">
                    <label for="payment-notes" class="payment-form-label">Notes (Optional)</label>
                    <input type="text" id="payment-notes" name="notes" class="payment-form-input" 
                           placeholder="Payment notes">
                </div>
            </div>

            <div class="payment-modal-footer">
                <button type="button" class="btn-payment-cancel" id="cancel-payment">Cancel</button>
                <button type="submit" class="btn-payment-submit" id="submit-payment">
                    <span class="payment-btn-text">Process Payment</span>
                    <i class="mdi mdi-loading loading-spin payment-btn-loading" style="display: none;"></i>
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Order confirmation functionality
    $('.confirm-order-btn').on('click', function() {
        const orderId = $(this).data('order-id');
        const button = $(this);
        const originalContent = button.html();
        
        // Show loading state
        button.prop('disabled', true).html('<i class="mdi mdi-loading loading-spin"></i>');
        
        $.ajax({
            url: `/api/orders/${orderId}/confirm`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // Extract order information
                    const orderNumber = response.data.order.order_number || orderId;
                    let kotNumber = null;
                    
                    // Check if KOT was created successfully
                    if (response.data.kot_orders && response.data.kot_orders.length > 0) {
                        kotNumber = response.data.kot_orders[0].kot_number || response.data.kot_orders[0].id;
                    }
                    
                    // Show success notification
                    showOrderConfirmationToast(orderNumber, kotNumber);
                    
                    // Update the order status display
                    const orderItem = button.closest('.order-item');
                    orderItem.find('.status-badge')
                        .removeClass('status-pending')
                        .addClass('status-confirmed')
                        .text('Confirmed');
                    
                    // Remove the confirm button
                    button.fadeOut();
                } else {
                    showOrderErrorToast(response.message || "Failed to confirm order");
                }
                
                // Restore button state
                button.prop('disabled', false).html(originalContent);
            },
            error: function(xhr) {
                console.error('Error confirming order:', xhr);
                let errorMessage = "Failed to confirm order";
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                showOrderErrorToast(errorMessage);
                
                // Restore button
                button.prop('disabled', false).html(originalContent);
            }
        });
    });

    // Payment modal functionality
    let currentOrderId = null;
    let paymentMethods = [];

    // Load payment methods on page load
    loadPaymentMethods();

    // Payment button click handler
    $('.pay-order-btn').on('click', function() {
        const orderId = $(this).data('order-id');
        openPaymentModal(orderId);
    });

    // Close payment modal handlers
    $('#close-payment-modal, #cancel-payment').on('click', function() {
        closePaymentModal();
    });

    // Click outside modal to close
    $('#payment-modal').on('click', function(e) {
        if (e.target === this) {
            closePaymentModal();
        }
    });

    // Payment form submission
    $('#payment-form').on('submit', function(e) {
        e.preventDefault();
        processPayment();
    });

    // Auto-fill remaining amount when payment method changes
    $('#payment-method').on('change', function() {
        const remainingAmount = parseFloat($('#payment-remaining-amount').text().replace('$', ''));
        if (remainingAmount > 0) {
            $('#payment-amount').val(remainingAmount.toFixed(2));
        }
    });

    function loadPaymentMethods() {
        $.ajax({
            url: '/api/payments/payment-methods',
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            },
            success: function(response) {
                paymentMethods = response.data || response;
                populatePaymentMethods();
            },
            error: function() {
                // Fallback payment methods
                paymentMethods = [
                    { id: 1, name: 'Cash', code: 'cash' },
                    { id: 2, name: 'Credit Card', code: 'credit_card' },
                    { id: 3, name: 'Debit Card', code: 'debit_card' }
                ];
                populatePaymentMethods();
            }
        });
    }

    function populatePaymentMethods() {
        const select = $('#payment-method');
        select.find('option:not(:first)').remove();
        
        paymentMethods.forEach(method => {
            if (method.is_active !== false) {
                select.append(`<option value="${method.id}">${method.name}</option>`);
            }
        });
    }

    function openPaymentModal(orderId) {
        currentOrderId = orderId;
        
        // Get order details
        $.ajax({
            url: `/pos/orders/${orderId}/details`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            },
            success: function(response) {
                if (response.success) {
                    const order = response.data;
                    populateOrderDetails(order);
                    $('#payment-modal').addClass('show');
                } else {
                    showErrorToast(response.message || 'Failed to load order details');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Failed to load order details';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showErrorToast(errorMessage);
            }
        });
    }

    function populateOrderDetails(order) {
        $('#payment-order-number').text('#' + order.order_number);
        $('#payment-total-amount').text('$' + parseFloat(order.total_amount).toFixed(2));
        
        const transaction = order.transaction;
        if (transaction) {
            const paidAmount = parseFloat(transaction.paid_amount || 0);
            const totalAmount = parseFloat(transaction.total_amount || order.total_amount);
            const remainingAmount = totalAmount - paidAmount;
            
            $('#payment-paid-amount').text('$' + paidAmount.toFixed(2));
            $('#payment-remaining-amount').text('$' + remainingAmount.toFixed(2));
            $('#payment-amount').val(remainingAmount.toFixed(2));
        } else {
            $('#payment-paid-amount').text('$0.00');
            $('#payment-remaining-amount').text('$' + parseFloat(order.total_amount).toFixed(2));
            $('#payment-amount').val(parseFloat(order.total_amount).toFixed(2));
        }
    }

    function closePaymentModal() {
        $('#payment-modal').removeClass('show');
        $('#payment-form')[0].reset();
        currentOrderId = null;
    }

    function processPayment() {
        if (!currentOrderId) return;

        const submitBtn = $('#submit-payment');
        const btnText = $('.payment-btn-text');
        const btnLoading = $('.payment-btn-loading');
        
        // Show loading state
        submitBtn.prop('disabled', true);
        btnText.hide();
        btnLoading.show();

        const formData = {
            payment_method_id: $('#payment-method').val(),
            amount: $('#payment-amount').val(),
            reference_number: $('#payment-reference').val(),
            notes: $('#payment-notes').val()
        };

        $.ajax({
            url: `/pos/orders/${currentOrderId}/payment`,
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            },
            success: function(response) {
                if (response.success) {
                    showSuccessToast('Payment processed successfully');
                    closePaymentModal();
                    // Refresh the page to update order status
                    location.reload();
                } else {
                    showErrorToast(response.message || 'Payment processing failed');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Payment processing failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showErrorToast(errorMessage);
            },
            complete: function() {
                // Restore button state
                submitBtn.prop('disabled', false);
                btnText.show();
                btnLoading.hide();
            }
        });
    }

    // Toast notification functions
    function showOrderConfirmationToast(orderNumber, kotNumber) {
        Swal.fire({
            icon: 'success',
            title: 'Order Confirmed!',
            text: `Order ${orderNumber} has been confirmed${kotNumber ? ` with KOT ${kotNumber}` : ''}`,
            timer: 3000,
            showConfirmButton: false
        });
    }

    function showOrderErrorToast(message) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message,
            timer: 3000,
            showConfirmButton: false
        });
    }

    function showSuccessToast(message) {
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: message,
            timer: 3000,
            showConfirmButton: false
        });
    }

    function showErrorToast(message) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message,
            timer: 3000,
            showConfirmButton: false
        });
    }
});
</script>
@endpush