<?php

namespace Modules\Customer\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Modules\Customer\Services\LoyaltyService;
use Modules\Customer\Http\Requests\AddLoyaltyPointsRequest;
use Modules\Customer\Http\Requests\RedeemLoyaltyPointsRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\ResponseHelper;

class LoyaltyController extends Controller
{
    protected LoyaltyService $loyaltyService;

    public function __construct(LoyaltyService $loyaltyService)
    {
        $this->loyaltyService = $loyaltyService;
    }

    /**
     * Show customer loyalty details
     */
    public function show(Customer $customer): View
    {
        $stats = $this->loyaltyService->getCustomerLoyaltyStats($customer);
        return view('customers::loyalty.show', compact('customer', 'stats'));
    }

    /**
     * Add loyalty points to customer
     */
    public function addPoints(AddLoyaltyPointsRequest $request, Customer $customer): JsonResponse
    {
        try {
            $transaction = $this->loyaltyService->addPoints(
                $customer,
                $request->points,
                $request->reason ?? 'Points added manually',
                $request->order_id
            );
            
            return ResponseHelper::success(
                'Loyalty points added successfully',
                $transaction
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to add loyalty points: ' . $e->getMessage()
            );
        }
    }

    /**
     * Redeem loyalty points from customer
     */
    public function redeemPoints(RedeemLoyaltyPointsRequest $request, Customer $customer): JsonResponse
    {
        try {
            $transaction = $this->loyaltyService->redeemPoints(
                $customer,
                $request->points,
                $request->reason ?? 'Points redeemed manually',
                $request->order_id
            );
            
            return ResponseHelper::success(
                'Loyalty points redeemed successfully',
                $transaction
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to redeem loyalty points: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get customer loyalty history
     */
    public function history(Customer $customer): JsonResponse
    {
        try {
            $history = $this->loyaltyService->getLoyaltyHistory($customer);
            
            return ResponseHelper::success('Loyalty history retrieved', $history);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get loyalty history: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get loyalty transactions data for DataTables
     */
    public function getTransactionsData(Request $request, Customer $customer): JsonResponse
    {
        try {
            $query = $this->loyaltyService->getTransactionsData($customer, $request->all());

            return DataTables::of($query)
                ->addColumn('type_badge', function ($transaction) {
                    $class = $transaction->type === 'earned' ? 'success' : 'warning';
                    return '<span class="badge bg-' . $class . '">' . ucfirst($transaction->type) . '</span>';
                })
                ->addColumn('points_formatted', function ($transaction) {
                    $color = $transaction->points > 0 ? 'text-success' : 'text-danger';
                    $sign = $transaction->points > 0 ? '+' : '';
                    return '<span class="' . $color . '">' . $sign . number_format($transaction->points, 2) . '</span>';
                })
                ->addColumn('processed_by_name', function ($transaction) {
                    return $transaction->processedBy ? $transaction->processedBy->name : 'System';
                })
                ->addColumn('order_link', function ($transaction) {
                    if ($transaction->order_id) {
                        return '<a href="' . route('orders.show', $transaction->order_id) . '" class="btn btn-sm btn-outline-primary">View Order</a>';
                    }
                    return '-';
                })
                ->addColumn('date_formatted', function ($transaction) {
                    return $transaction->created_at->format('M d, Y H:i');
                })
                ->rawColumns(['type_badge', 'points_formatted', 'order_link'])
                ->make(true);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to load transactions data: ' . $e->getMessage()
            );
        }
    }

    /**
     * Calculate points for order amount
     */
    public function calculatePoints(Request $request): JsonResponse
    {
        try {
            $amount = $request->get('amount', 0);
            $points = $this->loyaltyService->calculatePointsForOrder($amount);
            
            return ResponseHelper::success('Points calculated', ['points' => $points]);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to calculate points: ' . $e->getMessage()
            );
        }
    }

    /**
     * Calculate discount for points
     */
    public function calculateDiscount(Request $request): JsonResponse
    {
        try {
            $points = $request->get('points', 0);
            $discount = $this->loyaltyService->calculateDiscountForPoints($points);
            
            return ResponseHelper::success('Discount calculated', ['discount' => $discount]);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to calculate discount: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get top loyalty customers
     */
    public function topCustomers(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $customers = $this->loyaltyService->getTopLoyaltyCustomers($limit);
            
            return ResponseHelper::success('Top loyalty customers retrieved', $customers);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get top customers: ' . $e->getMessage()
            );
        }
    }

    /**
     * Show loyalty management dashboard
     */
    public function loyaltyIndex(): View
    {
        return view('customer::loyalty.index');
    }

    /**
     * Get loyalty statistics for dashboard
     */
    public function getLoyaltyStatistics(): JsonResponse
    {
        try {
            $stats = $this->loyaltyService->getLoyaltyStatistics();
            
            return ResponseHelper::success('Loyalty statistics retrieved', $stats);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get loyalty statistics: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get loyalty transactions data for DataTables (all customers)
     */
    public function getLoyaltyTransactionsData(Request $request): JsonResponse
    {
        try {
            $query = $this->loyaltyService->getAllTransactionsData($request->all());

            return DataTables::of($query)
                ->addColumn('customer_name', function ($transaction) {
                    return $transaction->customer ? $transaction->customer->full_name : 'Unknown';
                })
                ->addColumn('type_badge', function ($transaction) {
                    $class = $transaction->type === 'earned' ? 'success' : 'warning';
                    return '<span class="badge bg-' . $class . '">' . ucfirst($transaction->type) . '</span>';
                })
                ->addColumn('points_formatted', function ($transaction) {
                    $color = $transaction->points > 0 ? 'text-success' : 'text-danger';
                    $sign = $transaction->points > 0 ? '+' : '';
                    return '<span class="' . $color . '">' . $sign . number_format($transaction->points, 2) . '</span>';
                })
                ->addColumn('processed_by_name', function ($transaction) {
                    return $transaction->processedBy ? $transaction->processedBy->name : 'System';
                })
                ->addColumn('order_link', function ($transaction) {
                    if ($transaction->order_id) {
                        return '<a href="' . route('orders.show', $transaction->order_id) . '" class="btn btn-sm btn-outline-primary">View Order</a>';
                    }
                    return '-';
                })
                ->addColumn('date_formatted', function ($transaction) {
                    return $transaction->created_at->format('M d, Y H:i');
                })
                ->addColumn('actions', function ($transaction) {
                    $actions = '<div class="btn-group" role="group">';
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-info" onclick="viewTransaction(' . $transaction->id . ')" title="View Details">';
                    $actions .= '<i class="fas fa-eye"></i>';
                    $actions .= '</button>';
                    $actions .= '</div>';
                    return $actions;
                })
                ->rawColumns(['type_badge', 'points_formatted', 'order_link', 'actions'])
                ->make(true);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to load loyalty transactions data: ' . $e->getMessage()
            );
        }
    }

    /**
     * Export loyalty data
     */
    public function exportLoyaltyData(Request $request)
    {
        try {
            return $this->loyaltyService->exportLoyaltyData($request->all());
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to export loyalty data: ' . $e->getMessage());
        }
    }

    /**
     * Get loyalty settings
     */
    public function getLoyaltySettings(): JsonResponse
    {
        try {
            $settings = $this->loyaltyService->getLoyaltySettings();
            
            return ResponseHelper::success('Loyalty settings retrieved', $settings);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get loyalty settings: ' . $e->getMessage()
            );
        }
    }

    /**
     * Save loyalty settings
     */
    public function saveLoyaltySettings(Request $request): JsonResponse
    {
        try {
            $settings = $this->loyaltyService->saveLoyaltySettings($request->all());
            
            return ResponseHelper::success('Loyalty settings saved successfully', $settings);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to save loyalty settings: ' . $e->getMessage()
            );
        }
    }

    /**
     * Calculate points for order amount (alias for calculatePoints)
     */
    public function calculatePointsForOrder(Request $request): JsonResponse
    {
        return $this->calculatePoints($request);
    }

    /**
     * Calculate discount for points (alias for calculateDiscount)
     */
    public function calculateDiscountForPoints(Request $request): JsonResponse
    {
        return $this->calculateDiscount($request);
    }

    /**
     * Get top loyalty customers (alias for topCustomers)
     */
    public function getTopLoyaltyCustomers(Request $request): JsonResponse
    {
        return $this->topCustomers($request);
    }
}