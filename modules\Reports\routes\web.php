<?php

use Illuminate\Support\Facades\Route;
use Modules\Reports\Http\Controllers\ReportsWebController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['auth'])->prefix('reports')->name('reports.')->group(function () {
    // Reports dashboard
    Route::get('/', [ReportsWebController::class, 'index'])->name('index');

    // Report generation form
    Route::get('/generate', [ReportsWebController::class, 'create'])->name('create');
    Route::post('/generate', [ReportsWebController::class, 'store'])->name('store');

    // View specific report
    Route::get('/{report}', [ReportsWebController::class, 'show'])->name('show');

    // Download report
    Route::get('/{report}/download', [ReportsWebController::class, 'download'])->name('download');

    // Delete report
    Route::delete('/{report}', [ReportsWebController::class, 'destroy'])->name('destroy');

    // Quick report generation routes
    Route::get('/quick/daily-sales', [ReportsWebController::class, 'quickDailySales'])->name('quick.daily-sales');
    Route::get('/quick/hourly-sales', [ReportsWebController::class, 'quickHourlySales'])->name('quick.hourly-sales');
    Route::get('/quick/tax-report', [ReportsWebController::class, 'quickTaxReport'])->name('quick.tax-report');
    Route::get('/quick/void-cancelled', [ReportsWebController::class, 'quickVoidCancelled'])->name('quick.void-cancelled');

    // Category-specific routes
    Route::prefix('category')->name('category.')->group(function () {
        Route::get('/daily', [ReportsWebController::class, 'dailyReports'])->name('daily');
        Route::get('/periodic', [ReportsWebController::class, 'periodicReports'])->name('periodic');
        Route::get('/advanced', [ReportsWebController::class, 'advancedReports'])->name('advanced');
        Route::get('/restaurant', [ReportsWebController::class, 'restaurantReports'])->name('restaurant');
    });

    // Sales Reports Routes
    Route::prefix('sales')->name('sales.')->group(function () {
        Route::get('/daily', [ReportsWebController::class, 'dailySalesReport'])->name('daily');
        Route::get('/monthly', [ReportsWebController::class, 'monthlySalesReport'])->name('monthly');
        Route::get('/yearly', [ReportsWebController::class, 'yearlySalesReport'])->name('yearly');
        Route::get('/items', [ReportsWebController::class, 'itemSalesReport'])->name('items');
    });

    // Financial Reports Routes
    Route::prefix('financial')->name('financial.')->group(function () {
        Route::get('/profit-loss', [ReportsWebController::class, 'profitLossReport'])->name('profit-loss');
        Route::get('/revenue', [ReportsWebController::class, 'revenueReport'])->name('revenue');
        Route::get('/expenses', [ReportsWebController::class, 'expensesReport'])->name('expenses');
        Route::get('/tax', [ReportsWebController::class, 'taxReport'])->name('tax');
    });

    // Inventory Reports Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/stock-levels', [ReportsWebController::class, 'stockLevelsReport'])->name('stock-levels');
        Route::get('/low-stock', [ReportsWebController::class, 'lowStockReport'])->name('low-stock');
        Route::get('/movements', [ReportsWebController::class, 'stockMovementsReport'])->name('movements');
        Route::get('/waste', [ReportsWebController::class, 'wasteReport'])->name('waste');
    });

    // Staff Reports Routes
    Route::prefix('staff')->name('staff.')->group(function () {
        Route::get('/attendance', [ReportsWebController::class, 'attendanceReport'])->name('attendance');
        Route::get('/performance', [ReportsWebController::class, 'performanceReport'])->name('performance');
        Route::get('/payroll', [ReportsWebController::class, 'payrollReport'])->name('payroll');
        Route::get('/working-hours', [ReportsWebController::class, 'workingHoursReport'])->name('working-hours');
    });

    // Customer Reports Routes
    Route::prefix('customers')->name('customers.')->group(function () {
        Route::get('/analytics', [ReportsWebController::class, 'customerAnalyticsReport'])->name('analytics');
        Route::get('/loyalty', [ReportsWebController::class, 'loyaltyReport'])->name('loyalty');
        Route::get('/feedback', [ReportsWebController::class, 'feedbackReport'])->name('feedback');
    });
});
