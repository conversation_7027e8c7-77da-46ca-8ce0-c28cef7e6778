@extends('layouts.master')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@section('title')
إدارة الحجوزات
@stop

@section('page-header')
<div class="flex justify-between items-center">
    <div class="my-auto">
        <div class="flex items-center">
            <h4 class="text-lg font-semibold text-gray-800 mb-0 my-auto">إدارة الحجوزات</h4>
            <span class="text-gray-500 text-sm mr-2 mb-0">/ الحجوزات</span>
        </div>
    </div>
    <div class="flex my-xl-auto">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200 ml-2 flex items-center gap-2" data-toggle="modal" data-target="#addReservationModal">
                <i class="mdi mdi-plus"></i>
                <span>إضافة حجز</span>
            </button>
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="grid grid-cols-1 gap-6">
    <div class="col-span-full">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-semibold text-gray-800 mb-0">قائمة الحجوزات</h4>
                </div>
                <p class="text-sm text-gray-500 mb-2">إدارة جميع حجوزات المطعم</p>
            </div>
            
            <!-- Filters -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="confirmed">مؤكد</option>
                            <option value="seated">جالس</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                            <option value="no_show">لم يحضر</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                        <input type="date" id="date-from-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                        <input type="date" id="date-to-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">بحث العميل</label>
                        <input type="text" id="customer-search-filter" placeholder="اسم أو رقم هاتف العميل" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="mt-4 flex gap-2">
                    <button type="button" id="apply-filters" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">تطبيق الفلاتر</button>
                    <button type="button" id="clear-filters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">مسح الفلاتر</button>
                </div>
            </div>

            <div class="p-6">
                <div class="overflow-x-auto">
                    <table id="reservations-table" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الحجز</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم العميل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الهاتف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطاولة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنطقة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الحجز</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الأشخاص</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Reservation Modal -->
<div class="fixed inset-0 z-50 hidden overflow-y-auto" id="addReservationModal" aria-labelledby="addReservationModalLabel" role="dialog">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="relative bg-white rounded-lg w-full max-w-4xl">
            <div class="bg-blue-600 px-4 py-3 rounded-t-lg flex justify-between items-center">
                <h5 class="text-lg font-medium text-white" id="addReservationModalLabel">إضافة حجز جديد</h5>
                <button type="button" class="text-white hover:text-gray-200" data-dismiss="modal">
                    <span class="text-2xl">&times;</span>
                </button>
            </div>
            <form id="addReservationForm">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم العميل *</label>
                            <input type="text" name="customer_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- Other form fields remain the same, just updating the container styling -->
                        <!-- ... -->
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">طلبات خاصة</label>
                        <textarea name="special_requests" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                        <textarea name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 rounded-b-lg flex justify-end space-x-2">
                    <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">حفظ الحجز</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Reservation Modal -->
<div class="fixed inset-0 z-50 hidden overflow-y-auto" id="editReservationModal" aria-labelledby="editReservationModalLabel" role="dialog">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="relative bg-white rounded-lg w-full max-w-4xl">
            <div class="bg-yellow-600 px-4 py-3 rounded-t-lg flex justify-between items-center">
                <h5 class="text-lg font-medium text-white" id="editReservationModalLabel">تعديل الحجز</h5>
                <button type="button" class="text-white hover:text-gray-200" data-dismiss="modal">
                    <span class="text-2xl">&times;</span>
                </button>
            </div>
            <form id="editReservationForm">
                <input type="hidden" name="reservation_id" id="edit-reservation-id">
                <!-- Form content remains the same, just updating container styling -->
                <div class="bg-gray-50 px-4 py-3 rounded-b-lg flex justify-end space-x-2">
                    <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">تحديث الحجز</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Reservation Modal -->
<div class="fixed inset-0 z-50 hidden overflow-y-auto" id="viewReservationModal" aria-labelledby="viewReservationModalLabel" role="dialog">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="relative bg-white rounded-lg w-full max-w-4xl">
            <div class="bg-blue-400 px-4 py-3 rounded-t-lg flex justify-between items-center">
                <h5 class="text-lg font-medium text-white" id="viewReservationModalLabel">تفاصيل الحجز</h5>
                <button type="button" class="text-white hover:text-gray-200" data-dismiss="modal">
                    <span class="text-2xl">&times;</span>
                </button>
            </div>
            <div class="p-6">
                <div id="reservation-details">
                    <!-- Reservation details will be loaded here -->
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 rounded-b-lg flex justify-end">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#reservations-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("reservation.reservations.datatable") }}',
            data: function(d) {
                d.status = $('#status-filter').val();
                d.date_from = $('#date-from-filter').val();
                d.date_to = $('#date-to-filter').val();
                d.customer_search = $('#customer-search-filter').val();
            }
        },
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'reservation_number', name: 'reservation_number'},
            {data: 'customer_name', name: 'customer_name'},
            {data: 'customer_phone', name: 'customer_phone'},
            {data: 'table_name', name: 'table_name'},
            {data: 'area_name', name: 'area_name'},
            {data: 'reservation_datetime', name: 'reservation_datetime'},
            {data: 'party_size', name: 'party_size'},
            {data: 'status', name: 'status', orderable: false},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        order: [[6, 'desc']],
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        }
    });

    // Filter functionality
    $('#apply-filters').click(function() {
        table.draw();
    });

    $('#clear-filters').click(function() {
        $('#status-filter').val('');
        $('#date-from-filter').val('');
        $('#date-to-filter').val('');
        $('#customer-search-filter').val('');
        table.draw();
    });

    // Load areas and tables
    loadAreas();

    function loadAreas() {
        $.get('{{ route("reservation.areas.list") }}', function(data) {
            var areaSelects = ['#area-select', '#edit-area-select'];
            areaSelects.forEach(function(selector) {
                $(selector).empty().append('<option value="">اختر المنطقة</option>');
                data.forEach(function(area) {
                    $(selector).append('<option value="' + area.id + '">' + area.name + '</option>');
                });
            });
        });
    }

    // Area change handler
    $(document).on('change', '#area-select, #edit-area-select', function() {
        var areaId = $(this).val();
        var tableSelect = $(this).attr('id') === 'area-select' ? '#table-select' : '#edit-table-select';
        
        $(tableSelect).empty().append('<option value="">اختر الطاولة</option>');
        
        if (areaId) {
            $.get('{{ route("reservation.areas.tables", ":id") }}'.replace(':id', areaId), function(data) {
                data.forEach(function(table) {
                    $(tableSelect).append('<option value="' + table.id + '">' + table.table_name + ' (سعة: ' + table.capacity + ')</option>');
                });
            });
        }
    });

    // Add reservation form
    $('#addReservationForm').submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '{{ route("reservation.reservations.store") }}',
            method: 'POST',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#addReservationModal').modal('hide');
                $('#addReservationForm')[0].reset();
                table.draw();
                Swal.fire('نجح!', 'تم إضافة الحجز بنجاح', 'success');
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة الحجز';
                if (errors) {
                    errorMessage = Object.values(errors).flat().join('\n');
                }
                Swal.fire('خطأ!', errorMessage, 'error');
            }
        });
    });

    // Edit reservation form
    $('#editReservationForm').submit(function(e) {
        e.preventDefault();
        
        var reservationId = $('#edit-reservation-id').val();
        
        $.ajax({
            url: '{{ route("reservation.reservations.update", ":id") }}'.replace(':id', reservationId),
            method: 'PUT',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#editReservationModal').modal('hide');
                table.draw();
                Swal.fire('نجح!', 'تم تحديث الحجز بنجاح', 'success');
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث الحجز';
                if (errors) {
                    errorMessage = Object.values(errors).flat().join('\n');
                }
                Swal.fire('خطأ!', errorMessage, 'error');
            }
        });
    });
});

// Global functions for actions
function showReservation(id) {
    $.get('{{ route("reservation.reservations.show", ":id") }}'.replace(':id', id), function(data) {
        var reservation = data.data;
        var html = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div><strong>رقم الحجز:</strong> ${reservation.reservation_number || '-'}</div>
                <div><strong>اسم العميل:</strong> ${reservation.customer_name || '-'}</div>
                <div><strong>رقم الهاتف:</strong> ${reservation.customer_phone || '-'}</div>
                <div><strong>البريد الإلكتروني:</strong> ${reservation.customer_email || '-'}</div>
                <div><strong>عدد الأشخاص:</strong> ${reservation.party_size}</div>
                <div><strong>تاريخ الحجز:</strong> ${reservation.reservation_datetime}</div>
                <div><strong>المدة:</strong> ${reservation.duration_minutes} دقيقة</div>
                <div><strong>الطاولة:</strong> ${reservation.table ? reservation.table.table_name : '-'}</div>
                <div><strong>المنطقة:</strong> ${reservation.area ? reservation.area.name : '-'}</div>
                <div><strong>الحالة:</strong> ${reservation.reservation_status ? reservation.reservation_status.name : '-'}</div>
            </div>
            ${reservation.special_requests ? '<div class="mt-4"><strong>طلبات خاصة:</strong><br>' + reservation.special_requests + '</div>' : ''}
            ${reservation.notes ? '<div class="mt-4"><strong>ملاحظات:</strong><br>' + reservation.notes + '</div>' : ''}
        `;
        $('#reservation-details').html(html);
        $('#viewReservationModal').modal('show');
    });
}

function editReservation(id) {
    $.get('{{ route("reservation.reservations.edit", ":id") }}'.replace(':id', id), function(data) {
        var reservation = data.data;
        
        $('#edit-reservation-id').val(reservation.id);
        $('#edit-customer-name').val(reservation.customer_name);
        $('#edit-customer-phone').val(reservation.customer_phone);
        $('#edit-customer-email').val(reservation.customer_email);
        $('#edit-party-size').val(reservation.party_size);
        $('#edit-reservation-datetime').val(reservation.reservation_datetime);
        $('#edit-duration-minutes').val(reservation.duration_minutes);
        $('#edit-special-requests').val(reservation.special_requests);
        $('#edit-notes').val(reservation.notes);
        
        if (reservation.area_id) {
            $('#edit-area-select').val(reservation.area_id).trigger('change');
            setTimeout(function() {
                $('#edit-table-select').val(reservation.table_id);
            }, 500);
        }
        
        $('#editReservationModal').modal('show');
    });
}

function confirmReservation(id) {
    Swal.fire({
        title: 'تأكيد الحجز',
        text: 'هل أنت متأكد من تأكيد هذا الحجز؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، تأكيد',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route("reservation.reservations.confirm", ":id") }}'.replace(':id', id),
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#reservations-table').DataTable().draw();
                    Swal.fire('نجح!', 'تم تأكيد الحجز بنجاح', 'success');
                },
                error: function(xhr) {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء تأكيد الحجز', 'error');
                }
            });
        }
    });
}

function deleteReservation(id) {
    Swal.fire({
        title: 'حذف الحجز',
        text: 'هل أنت متأكد من حذف هذا الحجز؟ لا يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، حذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#d33'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route("reservation.reservations.destroy", ":id") }}'.replace(':id', id),
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#reservations-table').DataTable().draw();
                    Swal.fire('تم الحذف!', 'تم حذف الحجز بنجاح', 'success');
                },
                error: function(xhr) {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء حذف الحجز', 'error');
                }
            });
        }
    });
}
</script>
@endpush