<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Modules\Kitchen\Models\Kitchen;
use App\Models\Branch;
use App\Models\User;
use App\Models\MenuItem;

try {
    // Get existing kitchen or create one
    $kitchen = Kitchen::where('code', 'MAIN-001')->first();
    $user = User::first();
    
    if (!$kitchen) {
        // Get first branch and user
        $branch = Branch::first();
        
        if (!$branch) {
            echo "No branch found. Creating a branch first...\n";
            $branch = Branch::create([
                'tenant_id' => 1,
                'name' => 'Main Branch',
                'code' => 'MAIN',
                'address' => '123 Main Street',
                'phone' => '************',
                'email' => '<EMAIL>',
                'is_active' => true
            ]);
        }
        
        if (!$user) {
            echo "No user found. Please create a user first.\n";
            exit;
        }
        
        // Create kitchen
        $kitchen = Kitchen::create([
            'tenant_id' => 1,
            'branch_id' => $branch->id,
            'name' => 'Main Kitchen',
            'code' => 'MAIN-001',
            'description' => 'Primary kitchen for all main course preparations',
            'station_type' => 'main',
            'max_concurrent_orders' => 15,
            'average_prep_time_minutes' => 20,
            'is_active' => true,
            'display_order' => 1,
            'manager_id' => $user->id,
            'equipment_list' => json_encode(['stove', 'oven', 'grill', 'fryer']),
            'operating_hours' => json_encode([
                'monday' => ['start' => '06:00', 'end' => '23:00'],
                'tuesday' => ['start' => '06:00', 'end' => '23:00'],
                'wednesday' => ['start' => '06:00', 'end' => '23:00'],
                'thursday' => ['start' => '06:00', 'end' => '23:00'],
                'friday' => ['start' => '06:00', 'end' => '24:00'],
                'saturday' => ['start' => '06:00', 'end' => '24:00'],
                'sunday' => ['start' => '08:00', 'end' => '22:00']
            ]),
            'created_by' => $user->id
        ]);
        
        echo "Kitchen created with ID: " . $kitchen->id . "\n";
    } else {
        echo "Using existing kitchen with ID: " . $kitchen->id . "\n";
    }
    
    // Get some menu items to assign to kitchen
    $menuItems = MenuItem::take(5)->get();
    
    if ($menuItems->count() > 0) {
        foreach ($menuItems as $menuItem) {
            $kitchen->menuItems()->attach($menuItem->id, [
                'assigned_at' => now(),
                'is_active' => true,
                'prep_time_minutes' => rand(10, 30),
                'assigned_by' => $user->id
            ]);
        }
        echo "Assigned " . $menuItems->count() . " menu items to kitchen\n";
    } else {
        echo "No menu items found to assign\n";
    }
    
    echo "Kitchen data created successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}