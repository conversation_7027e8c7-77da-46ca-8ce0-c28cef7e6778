@extends('layouts.master')

@section('title', 'System Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cogs text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">System Settings</h1>
                        <p class="text-cyan-100">Configure system-wide settings and preferences</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-cyan-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-cyan-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-cyan-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-cyan-200 mx-2"></i>
                                <span class="text-white font-medium">System Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Settings Categories -->
    @if(isset($categories))
    @foreach($categories as $categoryKey => $category)
    <div class="bg-white rounded-lg shadow-lg mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">{{ $category['name'] }}</h3>
            <p class="text-gray-600 mt-1">Configure {{ strtolower($category['name']) }} settings</p>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                @foreach($category['keys'] as $key)
                <div>
                    <label for="{{ $key }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ ucwords(str_replace('_', ' ', $key)) }}
                    </label>
                    @if(in_array($key, ['app_logo']))
                        <input type="file" id="{{ $key }}" name="{{ $key }}" accept="image/*"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-cyan-50 file:text-cyan-700 hover:file:bg-cyan-100">
                    @elseif(in_array($key, ['timezone']))
                        <select id="{{ $key }}" name="{{ $key }}" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                            <option value="UTC">UTC</option>
                            <option value="America/New_York">Eastern Time</option>
                            <option value="America/Chicago">Central Time</option>
                            <option value="America/Denver">Mountain Time</option>
                            <option value="America/Los_Angeles">Pacific Time</option>
                        </select>
                    @elseif(in_array($key, ['language']))
                        <select id="{{ $key }}" name="{{ $key }}" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                            <option value="en">English</option>
                            <option value="ar">العربية</option>
                            <option value="es">Español</option>
                            <option value="fr">Français</option>
                        </select>
                    @elseif(in_array($key, ['currency']))
                        <select id="{{ $key }}" name="{{ $key }}" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                            <option value="USD">USD - US Dollar</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="SAR">SAR - Saudi Riyal</option>
                            <option value="AED">AED - UAE Dirham</option>
                        </select>
                    @elseif(in_array($key, ['date_format']))
                        <select id="{{ $key }}" name="{{ $key }}" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                            <option value="Y-m-d">YYYY-MM-DD</option>
                            <option value="m/d/Y">MM/DD/YYYY</option>
                            <option value="d/m/Y">DD/MM/YYYY</option>
                            <option value="d-m-Y">DD-MM-YYYY</option>
                        </select>
                    @elseif(in_array($key, ['default_tax_rate']))
                        <input type="number" id="{{ $key }}" name="{{ $key }}" step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
                               placeholder="Enter tax rate %" 
                               value="{{ isset($systemSettings[$key]) ? $systemSettings[$key]->value : '' }}">
                    @elseif(in_array($key, ['auto_print_receipt', 'sound_enabled']))
                        <div class="flex items-center">
                            <input type="checkbox" id="{{ $key }}" name="{{ $key }}" value="1"
                                   class="h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-300 rounded"
                                   {{ (isset($systemSettings[$key]) && $systemSettings[$key]->value == '1') ? 'checked' : '' }}>
                            <label for="{{ $key }}" class="ml-2 text-sm text-gray-700">
                                Enable {{ ucwords(str_replace('_', ' ', $key)) }}
                            </label>
                        </div>
                    @else
                        <input type="text" id="{{ $key }}" name="{{ $key }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
                               placeholder="Enter {{ strtolower(str_replace('_', ' ', $key)) }}" 
                               value="{{ isset($systemSettings[$key]) ? $systemSettings[$key]->value : '' }}">
                    @endif
                </div>
                @endforeach
            </div>

            <!-- Save Button for this category -->
            <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="save-category-btn px-4 py-2 text-sm font-medium text-white bg-cyan-600 border border-transparent rounded-lg hover:bg-cyan-700 focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2" data-category="{{ $categoryKey }}">
                    <i class="fas fa-save mr-2"></i>
                    Save {{ $category['name'] }}
                </button>
            </div>
        </div>
    </div>
    @endforeach
    @else
    <!-- Default system settings if no categories provided -->
    <div class="bg-white rounded-lg shadow-lg">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">System Configuration</h3>
            <p class="text-gray-600 mt-1">Configure basic system settings</p>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="app_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Application Name
                    </label>
                    <input type="text" id="app_name" name="app_name" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
                           placeholder="Enter application name" value="Restaurant POS">
                </div>

                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                        Timezone
                    </label>
                    <select id="timezone" name="timezone" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                    </select>
                </div>

                <div>
                    <label for="language" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Language
                    </label>
                    <select id="language" name="language" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                        <option value="en">English</option>
                        <option value="ar">العربية</option>
                        <option value="es">Español</option>
                        <option value="fr">Français</option>
                    </select>
                </div>

                <div>
                    <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Currency
                    </label>
                    <select id="currency" name="currency" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                        <option value="USD">USD - US Dollar</option>
                        <option value="EUR">EUR - Euro</option>
                        <option value="SAR">SAR - Saudi Riyal</option>
                        <option value="AED">AED - UAE Dirham</option>
                    </select>
                </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="save-settings-btn px-4 py-2 text-sm font-medium text-white bg-cyan-600 border border-transparent rounded-lg hover:bg-cyan-700 focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2">
                    <i class="fas fa-save mr-2"></i>
                    Save Settings
                </button>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Save category settings
    $('.save-category-btn').on('click', function() {
        const category = $(this).data('category');
        const button = $(this);
        const originalText = button.html();
        
        // Add loading state
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            button.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });

    // Save general settings
    $('.save-settings-btn').on('click', function() {
        const button = $(this);
        const originalText = button.html();
        
        // Add loading state
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            button.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'System settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });
});
</script>
@endpush
