<div class="flex space-x-1">
    <button type="button" onclick="viewCustomer({{ $customer->id }})" 
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200"
        title="View Details">
        <i class="fas fa-eye"></i>
    </button>
    
    <button type="button" onclick="editCustomer({{ $customer->id }})" 
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-1 transition-colors duration-200"
        title="Edit Customer">
        <i class="fas fa-edit"></i>
    </button>
    
    <button type="button" onclick="manageLoyalty({{ $customer->id }})" 
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-md hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1 transition-colors duration-200"
        title="Manage Loyalty">
        <i class="fas fa-star"></i>
    </button>
    
    <button type="button" onclick="toggleStatus({{ $customer->id }}, {{ $customer->is_active ? 'false' : 'true' }})" 
        class="inline-flex items-center px-2 py-1 text-xs font-medium {{ $customer->is_active ? 'text-red-700 bg-red-100 border-red-300 hover:bg-red-200 focus:ring-red-500' : 'text-green-700 bg-green-100 border-green-300 hover:bg-green-200 focus:ring-green-500' }} border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-1 transition-colors duration-200"
        title="{{ $customer->is_active ? 'Deactivate' : 'Activate' }}">
        <i class="fas fa-{{ $customer->is_active ? 'ban' : 'check' }}"></i>
    </button>
    
    <button type="button" onclick="deleteCustomer({{ $customer->id }})" 
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-colors duration-200"
        title="Delete Customer">
        <i class="fas fa-trash"></i>
    </button>
</div>

<script>
function toggleStatus(customerId, newStatus) {
    if (confirm(newStatus === 'true' ? 'Are you sure you want to activate this customer?' : 'Are you sure you want to deactivate this customer?')) {
        fetch(`/customers/${customerId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                is_active: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Customer status updated successfully!');
                // Reload the DataTable if it exists
                if (typeof table !== 'undefined') {
                    table.ajax.reload();
                }
                // Reload the page as fallback
                window.location.reload();
            } else {
                alert('Failed to update customer status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating customer status');
        });
    }
}
</script>