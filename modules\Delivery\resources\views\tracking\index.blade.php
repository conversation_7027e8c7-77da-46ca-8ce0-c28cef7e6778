@extends('layouts.master')

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<style>
/* Custom DataTables styling for Tailwind */
.dataTables_wrapper {
    font-family: inherit;
}

.dataTables_length select,
.dataTables_filter input {
    @apply px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.dataTables_length label,
.dataTables_filter label {
    @apply text-sm font-medium text-gray-700;
}

.dataTables_info {
    @apply text-sm text-gray-600;
}

.dataTables_paginate .paginate_button {
    @apply px-3 py-2 ml-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700;
}

.dataTables_paginate .paginate_button.current {
    @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
}

.dataTables_paginate .paginate_button.disabled {
    @apply opacity-50 cursor-not-allowed;
}

table.dataTable tbody tr {
    @apply hover:bg-gray-50;
}

table.dataTable tbody td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.dataTables_processing {
    @apply bg-white border border-gray-300 rounded-md shadow-sm;
}
</style>
@endpush

@section('content')
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-map-marker-alt mr-3"></i>
                تتبع التوصيل
            </h1>
            <p class="text-sm text-gray-600 mt-1">تتبع حالة طلبات التوصيل في الوقت الفعلي</p>
        </div>
        <div>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="refresh-btn">
                <i class="fas fa-sync-alt mr-2"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">فلاتر البحث</h3>
    </div>
    <div class="p-6">
        <!-- Filters -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">موظف التوصيل</label>
                <select id="personnel-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع موظفي التوصيل</option>
                    @foreach($deliveryPersonnel as $personnel)
                        <option value="{{ $personnel->id }}">{{ $personnel->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="assigned">مُعيّن</option>
                    <option value="picked_up">تم الاستلام</option>
                    <option value="in_transit">في الطريق</option>
                    <option value="delivered">تم التوصيل</option>
                    <option value="failed">فشل</option>
                    <option value="cancelled">ملغي</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                <input type="date" id="date-from" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                <input type="date" id="date-to" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="flex items-end space-x-2">
                <button type="button" id="filter-btn" class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fas fa-filter mr-2"></i>
                    فلتر
                </button>
                <button type="button" id="reset-btn" class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fas fa-undo mr-2"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>

                    <!-- DataTable -->
                    <div class="overflow-x-auto">
                        <table id="tracking-table" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الطلب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">موظف التوصيل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموقع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">معلومات التوقيت</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- DataTable will populate this -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Tracking Modal -->
<div id="trackingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تتبع الطلب في الوقت الفعلي</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('trackingModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mt-2 px-7 py-3">
                <div id="tracking-info" class="mb-4">
                    <!-- Tracking information will be loaded here -->
                </div>
                <div id="map-container" class="h-96 bg-gray-100 rounded-lg">
                    <!-- Map will be loaded here -->
                </div>
            </div>
            <div class="flex items-center justify-end px-4 py-3 space-x-2">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300" onclick="closeModal('trackingModal')">
                    إغلاق
                </button>
                <button type="button" id="refresh-tracking" class="px-4 py-2 bg-blue-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    <i class="fa fa-refresh mr-2"></i> تحديث الموقع
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Modal helper functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

$(document).ready(function() {
    // Initialize DataTable
    var table = $('#tracking-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("delivery.tracking.data") }}',
            data: function(d) {
                d.delivery_personnel_id = $('#personnel-filter').val();
                d.status = $('#status-filter').val();
                d.date_from = $('#date-from').val();
                d.date_to = $('#date-to').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'order_number', name: 'order.order_number' },
            { data: 'delivery_personnel_name', name: 'deliveryPersonnel.name' },
            { data: 'status_badge', name: 'status' },
            { data: 'location', name: 'location', orderable: false, searchable: false },
            { data: 'timing_info', name: 'timing_info', orderable: false, searchable: false },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        autoWidth: false,
        responsive: true
    });

    // Filter functionality
    $('#filter-btn').click(function() {
        table.draw();
    });

    $('#reset-btn').click(function() {
        $('#personnel-filter').val('');
        $('#status-filter').val('');
        $('#date-from').val('');
        $('#date-to').val('');
        table.draw();
    });

    // Refresh functionality
    $('#refresh-btn').click(function() {
        table.draw(false);
    });

    // Real-time tracking functionality
    window.showRealTimeTracking = function(orderId) {
        $('#trackingModal').modal('show');
        loadTrackingData(orderId);
        
        $('#refresh-tracking').off('click').on('click', function() {
            loadTrackingData(orderId);
        });
    };

    function loadTrackingData(orderId) {
        $.ajax({
            url: '{{ route("delivery.tracking.order", ":id") }}'.replace(':id', orderId),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    var trackingHtml = `
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h6 class="text-lg font-semibold text-gray-900 mb-3">معلومات الطلب</h6>
                                <p class="mb-2"><span class="font-medium">رقم الطلب:</span> #${data.order_number}</p>
                                <p class="mb-2"><span class="font-medium">موظف التوصيل:</span> ${data.delivery_personnel}</p>
                                <p class="mb-2"><span class="font-medium">الحالة:</span> ${getStatusLabel(data.status)}</p>
                            </div>
                            <div>
                                <h6 class="text-lg font-semibold text-gray-900 mb-3">معلومات التوقيت</h6>
                                <p class="mb-2"><span class="font-medium">الوقت المتوقع:</span> ${data.estimated_arrival_time || 'غير محدد'}</p>
                                <p class="mb-2"><span class="font-medium">الوقت الفعلي:</span> ${data.actual_arrival_time || 'لم يصل بعد'}</p>
                                <p class="mb-2"><span class="font-medium">آخر تحديث:</span> ${data.last_updated}</p>
                            </div>
                        </div>
                    `;
                    
                    $('#tracking-info').html(trackingHtml);
                    
                    // Load map if coordinates are available
                    if (data.latitude && data.longitude) {
                        loadMap(data.latitude, data.longitude);
                    } else {
                        $('#map-container').html('<div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md">لا توجد بيانات موقع متاحة</div>');
                    }
                } else {
                    $('#tracking-info').html('<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">' + response.message + '</div>');
                }
            },
            error: function() {
                $('#tracking-info').html('<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">حدث خطأ أثناء تحميل بيانات التتبع</div>');
            }
        });
    }

    function getStatusLabel(status) {
        var labels = {
            'assigned': 'مُعيّن',
            'picked_up': 'تم الاستلام',
            'in_transit': 'في الطريق',
            'delivered': 'تم التوصيل',
            'failed': 'فشل',
            'cancelled': 'ملغي'
        };
        return labels[status] || status;
    }

    function loadMap(latitude, longitude) {
        var mapHtml = `
            <div class="text-center">
                <p class="font-medium mb-2">الموقع الحالي:</p>
                <p class="mb-1">خط العرض: ${latitude}</p>
                <p class="mb-4">خط الطول: ${longitude}</p>
                <a href="https://maps.google.com/?q=${latitude},${longitude}" target="_blank" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fa fa-map-marker mr-2"></i> عرض على خرائط جوجل
                </a>
            </div>
        `;
        $('#map-container').html(mapHtml);
    }

    // Auto-refresh every 30 seconds for active tracking
    setInterval(function() {
        if ($('#trackingModal').hasClass('show')) {
            var orderId = $('#refresh-tracking').data('order-id');
            if (orderId) {
                loadTrackingData(orderId);
            }
        }
    }, 30000);
});
</script>
@endpush