<?php

namespace Modules\Reservation\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Area;
use Modules\Reservation\Services\AreaService;

class AreaController extends Controller
{
    protected AreaService $areaService;

    public function __construct(AreaService $areaService)
    {
        $this->areaService = $areaService;
    }

    /**
     * Display the areas index page.
     */
    public function index()
    {
        return view('Reservation::areas');
    }

    /**
     * Get areas data for DataTables.
     */
    public function dataTable(Request $request)
    {
        if ($request->ajax()) {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'error' => 'User is not assigned to any branch'
                ], 400);
            }

            $query = Area::where('branch_id', $user->branch_id);

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($area) {
                    $actions = '<div class="btn-group" role="group">';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-info" onclick="showArea(' . $area->id . ')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="editArea(' . $area->id . ')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteArea(' . $area->id . ')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>';
                    
                    $actions .= '</div>';
                    return $actions;
                })
                ->editColumn('is_active', function ($area) {
                    return $area->is_active ? 
                        '<span class="badge badge-success">نشط</span>' : 
                        '<span class="badge badge-secondary">غير نشط</span>';
                })
                ->rawColumns(['action', 'is_active'])
                ->make(true);
        }

        return view('Reservation::areas');
    }

    /**
     * Get areas data for DataTables (alias for dataTable).
     */
 

    /**
     * Store a new area.
     */
    public function store(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id || !$user->tenant_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch or tenant'
                ], 400);
            }

            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $data = $request->all();
            $data['tenant_id'] = $user->tenant_id;
            $data['branch_id'] = $user->branch_id;
            
            $area = $this->areaService->createArea($data);

            return response()->json([
                'success' => true,
                'data' => $area,
                'message' => 'Area created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create area',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Show a specific area.
     */
    public function show($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $area = $this->areaService->getAreaByIdForBranch($id, $user->branch_id);

            if (!$area) {
                return response()->json([
                    'success' => false,
                    'message' => 'Area not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $area,
                'message' => 'Area retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve area',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a specific area.
     */
    public function update(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $area = $this->areaService->updateAreaForBranch($id, $request->all(), $user->branch_id);

            if (!$area) {
                return response()->json([
                    'success' => false,
                    'message' => 'Area not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $area,
                'message' => 'Area updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update area',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Delete a specific area.
     */
    public function destroy($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $deleted = $this->areaService->deleteAreaForBranch($id, $user->branch_id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Area not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Area deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete area',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get areas dropdown data.
     */
    public function dropdown()
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $areas = Area::where('branch_id', $user->branch_id)
                ->where('is_active', true)
                ->select('id', 'name')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $areas
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve areas',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export areas.
     */
    public function export(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // For now, just return a simple response
            return response()->json([
                'success' => true,
                'message' => 'Export functionality will be implemented soon'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export areas',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get area reports.
     */
    public function areaReports(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // For now, just return basic statistics
            return response()->json([
                'success' => true,
                'message' => 'Area reports functionality will be implemented soon'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate area reports',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}