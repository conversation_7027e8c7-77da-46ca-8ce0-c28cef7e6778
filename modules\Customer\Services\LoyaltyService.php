<?php

namespace Modules\Customer\Services;

use App\Models\Customer;
use App\Models\LoyaltyTransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;

class LoyaltyService
{
    /**
     * Add loyalty points to customer
     */
    public function addPoints(Customer $customer, float $points, string $reason = 'Points added', ?int $orderId = null): LoyaltyTransaction
    {
        return DB::transaction(function () use ($customer, $points, $reason, $orderId) {
            // Update customer loyalty points
            $customer->increment('loyalty_points', $points);

            // Create loyalty transaction record
            return LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'order_id' => $orderId,
                'points' => $points,
                'type' => 'earned',
                'description' => $reason,
                'processed_by' => Auth::id(),
                'processed_at' => now(),
            ]);
        });
    }

    /**
     * Redeem loyalty points from customer
     */
    public function redeemPoints(Customer $customer, float $points, string $reason = 'Points redeemed', ?int $orderId = null): LoyaltyTransaction
    {
        if ($customer->loyalty_points < $points) {
            throw new \Exception('Insufficient loyalty points');
        }

        return DB::transaction(function () use ($customer, $points, $reason, $orderId) {
            // Update customer loyalty points
            $customer->decrement('loyalty_points', $points);

            // Create loyalty transaction record
            return LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'order_id' => $orderId,
                'points' => -$points,
                'type' => 'redeemed',
                'description' => $reason,
                'processed_by' => Auth::id(),
                'processed_at' => now(),
            ]);
        });
    }

    /**
     * Get customer loyalty history
     */
    public function getLoyaltyHistory(Customer $customer): Collection
    {
        return LoyaltyTransaction::where('customer_id', $customer->id)
            ->with(['processedBy', 'order'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get customer current points
     */
    public function getCurrentPoints(Customer $customer): float
    {
        return $customer->loyalty_points;
    }

    /**
     * Calculate points for order amount
     */
    public function calculatePointsForOrder(float $orderAmount): float
    {
        // 1 point for every $10 spent (configurable)
        $pointsPerDollar = 0.1;
        return floor($orderAmount * $pointsPerDollar);
    }

    /**
     * Calculate discount amount for points
     */
    public function calculateDiscountForPoints(float $points): float
    {
        // 1 point = $0.01 discount (configurable)
        $dollarPerPoint = 0.01;
        return $points * $dollarPerPoint;
    }

    /**
     * Get loyalty transactions for DataTables
     */
    public function getTransactionsData(Customer $customer, array $filters = [])
    {
        $query = LoyaltyTransaction::where('customer_id', $customer->id)
            ->with(['processedBy', 'order']);

        // Apply filters
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Get loyalty statistics for customer
     */
    public function getCustomerLoyaltyStats(Customer $customer): array
    {
        $totalEarned = LoyaltyTransaction::where('customer_id', $customer->id)
            ->where('type', 'earned')
            ->sum('points');

        $totalRedeemed = LoyaltyTransaction::where('customer_id', $customer->id)
            ->where('type', 'redeemed')
            ->sum('points');

        $transactionCount = LoyaltyTransaction::where('customer_id', $customer->id)->count();

        return [
            'current_points' => $customer->loyalty_points,
            'total_earned' => $totalEarned,
            'total_redeemed' => abs($totalRedeemed),
            'transaction_count' => $transactionCount,
        ];
    }

    /**
     * Get top loyalty customers
     */
    public function getTopLoyaltyCustomers(int $limit = 10): Collection
    {
        return Customer::where('loyalty_points', '>', 0)
            ->orderBy('loyalty_points', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get overall loyalty statistics
     */
    public function getLoyaltyStatistics(): array
    {
        $totalPointsIssued = LoyaltyTransaction::where('type', 'earned')->sum('points');
        $totalPointsRedeemed = abs(LoyaltyTransaction::where('type', 'redeemed')->sum('points'));
        $activeMembers = Customer::where('loyalty_points', '>', 0)->count();
        $totalMembers = Customer::count();
        $averagePointsPerCustomer = $totalMembers > 0 ? Customer::avg('loyalty_points') : 0;

        // Recent activity (last 30 days)
        $recentEarned = LoyaltyTransaction::where('type', 'earned')
            ->where('created_at', '>=', now()->subDays(30))
            ->sum('points');
        
        $recentRedeemed = abs(LoyaltyTransaction::where('type', 'redeemed')
            ->where('created_at', '>=', now()->subDays(30))
            ->sum('points'));

        // Monthly data for charts (last 12 months)
        $monthlyData = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthlyData[] = [
                'month' => $date->format('M Y'),
                'earned' => LoyaltyTransaction::where('type', 'earned')
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->sum('points'),
                'redeemed' => abs(LoyaltyTransaction::where('type', 'redeemed')
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->sum('points'))
            ];
        }

        return [
            'total_points_issued' => $totalPointsIssued,
            'total_points_redeemed' => $totalPointsRedeemed,
            'points_outstanding' => $totalPointsIssued - $totalPointsRedeemed,
            'active_members' => $activeMembers,
            'total_members' => $totalMembers,
            'average_points_per_customer' => round($averagePointsPerCustomer, 2),
            'recent_earned' => $recentEarned,
            'recent_redeemed' => $recentRedeemed,
            'monthly_data' => $monthlyData,
        ];
    }

    /**
     * Get all loyalty transactions for DataTables
     */
    public function getAllTransactionsData(array $filters = [])
    {
        $query = LoyaltyTransaction::with(['customer', 'processedBy', 'order']);

        // Apply filters
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (!empty($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Export loyalty data
     */
    public function exportLoyaltyData(array $filters = [])
    {
        $transactions = $this->getAllTransactionsData($filters)->get();
        
        $csvData = [];
        $csvData[] = [
            'Date',
            'Customer',
            'Type',
            'Points',
            'Description',
            'Order ID',
            'Processed By'
        ];

        foreach ($transactions as $transaction) {
            $csvData[] = [
                $transaction->created_at->format('Y-m-d H:i:s'),
                $transaction->customer ? $transaction->customer->full_name : 'Unknown',
                ucfirst($transaction->type),
                $transaction->points,
                $transaction->description,
                $transaction->order_id ?: '-',
                $transaction->processedBy ? $transaction->processedBy->name : 'System'
            ];
        }

        $filename = 'loyalty_transactions_' . now()->format('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get loyalty settings
     */
    public function getLoyaltySettings(): array
    {
        // In a real application, these would be stored in a settings table or config
        // For now, returning default values
        return [
            'loyalty_enabled' => true,
            'points_per_dollar' => 1.0,
            'minimum_order_amount' => 0,
            'signup_bonus' => 100,
            'birthday_bonus' => 50,
            'points_value' => 5.00, // $5 per 100 points
            'minimum_redemption' => 100,
            'maximum_redemption_percent' => 50,
            'points_expiry_months' => 0, // 0 = never expire
            'enable_tiers' => false,
            'silver_threshold' => 500,
            'gold_threshold' => 1000,
            'platinum_threshold' => 2500,
            'silver_multiplier' => 1.25,
            'gold_multiplier' => 1.5,
            'platinum_multiplier' => 2.0,
            'notify_points_earned' => true,
            'notify_points_redeemed' => true,
            'notify_tier_upgrade' => true,
            'notify_birthday' => true,
        ];
    }

    /**
     * Save loyalty settings
     */
    public function saveLoyaltySettings(array $settings): array
    {
        // In a real application, these would be saved to a settings table
        // For now, just validate and return the settings
        
        $validatedSettings = [
            'loyalty_enabled' => $settings['loyalty_enabled'] ?? true,
            'points_per_dollar' => max(0, floatval($settings['points_per_dollar'] ?? 1.0)),
            'minimum_order_amount' => max(0, floatval($settings['minimum_order_amount'] ?? 0)),
            'signup_bonus' => max(0, intval($settings['signup_bonus'] ?? 100)),
            'birthday_bonus' => max(0, intval($settings['birthday_bonus'] ?? 50)),
            'points_value' => max(0, floatval($settings['points_value'] ?? 5.00)),
            'minimum_redemption' => max(1, intval($settings['minimum_redemption'] ?? 100)),
            'maximum_redemption_percent' => max(1, min(100, intval($settings['maximum_redemption_percent'] ?? 50))),
            'points_expiry_months' => max(0, intval($settings['points_expiry_months'] ?? 0)),
            'enable_tiers' => $settings['enable_tiers'] ?? false,
            'silver_threshold' => max(0, floatval($settings['silver_threshold'] ?? 500)),
            'gold_threshold' => max(0, floatval($settings['gold_threshold'] ?? 1000)),
            'platinum_threshold' => max(0, floatval($settings['platinum_threshold'] ?? 2500)),
            'silver_multiplier' => max(1, floatval($settings['silver_multiplier'] ?? 1.25)),
            'gold_multiplier' => max(1, floatval($settings['gold_multiplier'] ?? 1.5)),
            'platinum_multiplier' => max(1, floatval($settings['platinum_multiplier'] ?? 2.0)),
            'notify_points_earned' => $settings['notify_points_earned'] ?? true,
            'notify_points_redeemed' => $settings['notify_points_redeemed'] ?? true,
            'notify_tier_upgrade' => $settings['notify_tier_upgrade'] ?? true,
            'notify_birthday' => $settings['notify_birthday'] ?? true,
        ];

        // Here you would typically save to database
        // Setting::updateOrCreate(['key' => 'loyalty_settings'], ['value' => json_encode($validatedSettings)]);

        return $validatedSettings;
    }
}