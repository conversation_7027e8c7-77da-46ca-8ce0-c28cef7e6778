<?php

namespace Modules\Transaction\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Modules\Transaction\Models\Payment;
use Modules\Transaction\Models\PaymentMethod;
use Modules\Transaction\Services\PaymentService;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Services\PaymentMethodService;
use Modules\Transaction\Http\Requests\StorePaymentRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Yajra\DataTables\Facades\DataTables;

class PaymentWebController extends Controller
{
    protected PaymentService $paymentService;
    protected TransactionService $transactionService;
    protected PaymentMethodService $paymentMethodService;

    public function __construct(PaymentService $paymentService, TransactionService $transactionService, PaymentMethodService $paymentMethodService)
    {
        $this->paymentService = $paymentService;
        $this->transactionService = $transactionService;
        $this->paymentMethodService = $paymentMethodService;
    }

    /**
     * Display a listing of payments.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->getDataTablesData($request);
        }

        $paymentMethods = $this->paymentService->getPaymentMethods();
        $dueTransactions = $this->transactionService->getDueTransactions(['limit' => 50]);

        return view('transaction::payments.index', compact('paymentMethods', 'dueTransactions'));
    }

    /**
     * Get DataTables data for payments.
     */
    private function getDataTablesData(Request $request)
    {
        $query = Payment::with(['transaction.order', 'paymentMethod', 'processedBy']);

        return DataTables::of($query)
            ->addColumn('id', function ($payment) {
                return $payment->id;
            })
            ->addColumn('payment_number', function ($payment) {
                return $payment->payment_number;
            })
            ->addColumn('transaction_number', function ($payment) {
                return $payment->transaction ? $payment->transaction->transaction_number : 'N/A';
            })
            ->addColumn('order_number', function ($payment) {
                return $payment->transaction && $payment->transaction->order 
                    ? $payment->transaction->order->order_number 
                    : 'N/A';
            })
            ->addColumn('payment_method', function ($payment) {
                return $payment->paymentMethod ? $payment->paymentMethod->name : 'Unknown';
            })
            ->addColumn('payment_method_badge', function ($payment) {
                $method = $payment->paymentMethod;
                $methodName = $method ? $method->name : 'Unknown';
                $methodCode = $method ? $method->code : 'unknown';
                
                return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' 
                    . $methodName . '</span>';
            })
            ->addColumn('status', function ($payment) {
                return $payment->status;
            })
            ->addColumn('status_badge', function ($payment) {
                $statusColors = [
                    'completed' => 'bg-green-100 text-green-800',
                    'pending' => 'bg-yellow-100 text-yellow-800',
                    'failed' => 'bg-red-100 text-red-800',
                    'cancelled' => 'bg-gray-100 text-gray-800',
                    'refunded' => 'bg-purple-100 text-purple-800',
                ];
                
                $colorClass = $statusColors[$payment->status] ?? 'bg-gray-100 text-gray-800';
                
                return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' 
                    . $colorClass . '">' . ucfirst(str_replace('_', ' ', $payment->status)) . '</span>';
            })
            ->addColumn('amount', function ($payment) {
                return $payment->amount;
            })
            ->addColumn('formatted_amount', function ($payment) {
                return '$' . number_format($payment->amount, 2);
            })
            ->addColumn('reference_number', function ($payment) {
                return $payment->reference_number ?: '-';
            })
            ->addColumn('formatted_date', function ($payment) {
                return $payment->payment_date ? $payment->payment_date->format('M d, Y H:i') : '-';
            })
            ->addColumn('processed_by', function ($payment) {
                return $payment->processedBy ? $payment->processedBy->name : 'System';
            })
            ->addColumn('action', function ($payment) {
                $actions = '<div class="flex space-x-2">';
                
                // View button
                $actions .= '<button type="button" class="view-payment-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" data-id="' . $payment->id . '">';
                $actions .= '<i class="fas fa-eye mr-1"></i> View';
                $actions .= '</button>';
                
                // Cancel button (if can be cancelled)
                if ($payment->canBeCancelled()) {
                    $actions .= '<button type="button" class="cancel-payment-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 ml-2" data-id="' . $payment->id . '">';
                    $actions .= '<i class="fas fa-ban mr-1"></i> Cancel';
                    $actions .= '</button>';
                }
                
                // Refund button (if can be refunded)
                if ($payment->canBeRefunded()) {
                    $actions .= '<button type="button" class="refund-payment-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ml-2" data-id="' . $payment->id . '">';
                    $actions .= '<i class="fas fa-undo mr-1"></i> Refund';
                    $actions .= '</button>';
                }
                
                $actions .= '</div>';
                
                return $actions;
            })
            ->filter(function ($query) use ($request) {
                // Status filter
                if ($request->has('status') && $request->status != '') {
                    $query->where('status', $request->status);
                }
                
                // Payment method filter
                if ($request->has('payment_method') && $request->payment_method != '') {
                    $query->where('payment_method_id', $request->payment_method);
                }
                
                // Date range filter
                if ($request->has('date_from') && $request->date_from != '') {
                    $query->whereDate('payment_date', '>=', $request->date_from);
                }
                
                if ($request->has('date_to') && $request->date_to != '') {
                    $query->whereDate('payment_date', '<=', $request->date_to);
                }
                
                // Amount range filter
                if ($request->has('amount_from') && $request->amount_from != '') {
                    $query->where('amount', '>=', $request->amount_from);
                }
                
                if ($request->has('amount_to') && $request->amount_to != '') {
                    $query->where('amount', '<=', $request->amount_to);
                }
            })
            ->rawColumns(['payment_method_badge', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new payment.
     */
    public function create(Request $request): View
    {
        $transactionId = $request->get('transaction_id');
        $transaction = null;

        if ($transactionId) {
            $transaction = $this->transactionService->getTransactionById($transactionId);
        }

        $paymentMethods = $this->paymentService->getPaymentMethods();
        $dueTransactions = $this->transactionService->getDueTransactions(['limit' => 50]);

        return view('transaction::payments.create', compact('transaction', 'paymentMethods', 'dueTransactions'));
    }

    /**
     * Store a newly created payment.
     */
    public function store(StorePaymentRequest $request): RedirectResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($request->transaction_id);

            if (!$transaction) {
                return redirect()
                    ->back()
                    ->withInput()
                    ->with('error', 'Transaction not found.');
            }

            $payment = $this->paymentService->processPayment($transaction, $request->validated());

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment processed successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to process payment: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(int $id): View
    {
        $payment = $this->paymentService->getPaymentById($id);

        if (!$payment) {
            abort(404, 'Payment not found');
        }

        return view('transaction::payments.show', compact('payment'));
    }

    /**
     * Cancel the specified payment.
     */
    public function cancel(int $id, Request $request): RedirectResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return redirect()
                    ->route('payments.index')
                    ->with('error', 'Payment not found.');
            }

            $reason = $request->input('reason', 'Payment cancelled by user');
            $this->paymentService->cancelPayment($payment, $reason);

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment cancelled successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to cancel payment: ' . $e->getMessage());
        }
    }

    /**
     * Refund the specified payment.
     */
    public function refund(int $id, Request $request): RedirectResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return redirect()
                    ->route('payments.index')
                    ->with('error', 'Payment not found.');
            }

            $refundAmount = $request->input('refund_amount');
            $reason = $request->input('reason', 'Payment refunded by user');
            
            $this->paymentService->refundPayment($payment, $refundAmount, $reason);

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment refunded successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to refund payment: ' . $e->getMessage());
        }
    }

    /**
     * Show payment statistics page.
     */
    public function statistics(): View
    {
        return view('transaction::payments.statistics');
    }

    /**
     * Show payment methods page.
     */
    public function methods(Request $request)
    {
        if ($request->ajax()) {
            if ($request->has('stats')) {
                return response()->json([
                    'statistics' => $this->paymentMethodService->getPaymentMethodStatistics()
                ]);
            }
            
            return $this->getPaymentMethodsDataTablesData($request);
        }

        return view('transaction::payments.methods');
    }

    /**
     * Get DataTables data for payment methods - Global Implementation.
     */
    private function getPaymentMethodsDataTablesData(Request $request)
    {
        // Base query without tenant filtering - payment methods are global
        $query = PaymentMethod::orderBy('sort_order', 'asc')
                             ->orderBy('name', 'asc');

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('status_badge', function ($method) {
                $statusClass = $method->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                $statusText = $method->is_active ? 'Active' : 'Inactive';
                
                return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' 
                    . $statusClass . '">' . $statusText . '</span>';
            })
            ->addColumn('action', function ($method) {
                $actions = '<div class="flex items-center space-x-1">';
                
                // Edit button
                $actions .= '<button type="button" class="edit-payment-method-btn inline-flex items-center px-2 py-1 text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-1 focus:ring-blue-500" data-id="' . $method->id . '" title="Edit">';
                $actions .= '<i class="fas fa-edit"></i>';
                $actions .= '</button>';
                
                // Toggle status button
                $toggleText = $method->is_active ? 'Deactivate' : 'Activate';
                $toggleClass = $method->is_active ? 'text-red-700 bg-red-100 hover:bg-red-200' : 'text-green-700 bg-green-100 hover:bg-green-200';
                $toggleIcon = $method->is_active ? 'fa-ban' : 'fa-check';
                
                $actions .= '<button type="button" class="toggle-status-btn inline-flex items-center px-2 py-1 text-xs font-medium rounded ' . $toggleClass . ' focus:outline-none focus:ring-1 focus:ring-blue-500" data-id="' . $method->id . '" title="' . $toggleText . '">';
                $actions .= '<i class="fas ' . $toggleIcon . '"></i>';
                $actions .= '</button>';
                
                // Delete button
                $actions .= '<button type="button" class="delete-payment-method-btn inline-flex items-center px-2 py-1 text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-1 focus:ring-red-500" data-id="' . $method->id . '" title="Delete">';
                $actions .= '<i class="fas fa-trash"></i>';
                $actions .= '</button>';
                
                $actions .= '</div>';
                
                return $actions;
            })
            ->filter(function ($query) use ($request) {
                // Search filter
                if ($request->filled('search_filter')) {
                    $search = $request->search_filter;
                    $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('code', 'like', "%{$search}%")
                          ->orWhere('description', 'like', "%{$search}%");
                    });
                }
                
                // Status filter
                if ($request->filled('status_filter') && $request->status_filter !== '') {
                    $query->where('is_active', $request->status_filter == '1');
                }
            })
            ->rawColumns(['status_badge', 'action'])
            ->make(true);
    }

    /**
     * Store a new payment method.
     */
    public function storePaymentMethod(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:100',
                'code' => 'required|string|max:50|regex:/^[a-zA-Z0-9_]+$/',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            // Check for validation errors from service
            $errors = $this->paymentMethodService->validatePaymentMethodData($validatedData);
            if (!empty($errors)) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $errors
                ], 422);
            }

            $paymentMethod = $this->paymentMethodService->createPaymentMethod($validatedData);

            return response()->json([
                'message' => 'Payment method created successfully',
                'data' => $paymentMethod
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show a specific payment method.
     */
    public function showPaymentMethod(int $id): JsonResponse
    {
        try {
            $paymentMethod = $this->paymentMethodService->getPaymentMethodById($id);

            if (!$paymentMethod) {
                return response()->json([
                    'message' => 'Payment method not found'
                ], 404);
            }

            return response()->json([
                'data' => $paymentMethod
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a payment method.
     */
    public function updatePaymentMethod(Request $request, int $id): JsonResponse
    {
        try {
            $paymentMethod = $this->paymentMethodService->getPaymentMethodById($id);

            if (!$paymentMethod) {
                return response()->json([
                    'message' => 'Payment method not found'
                ], 404);
            }

            $validatedData = $request->validate([
                'name' => 'required|string|max:100',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            // Check for validation errors from service (for update, we don't validate code)
            $errors = $this->paymentMethodService->validatePaymentMethodData($validatedData, $paymentMethod);
            if (!empty($errors)) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $errors
                ], 422);
            }

            $updatedPaymentMethod = $this->paymentMethodService->updatePaymentMethod($paymentMethod, $validatedData);

            return response()->json([
                'message' => 'Payment method updated successfully',
                'data' => $updatedPaymentMethod
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle payment method status.
     */
    public function togglePaymentMethodStatus(int $id): JsonResponse
    {
        try {
            $paymentMethod = $this->paymentMethodService->getPaymentMethodById($id);

            if (!$paymentMethod) {
                return response()->json([
                    'message' => 'Payment method not found'
                ], 404);
            }

            $updatedPaymentMethod = $this->paymentMethodService->toggleStatus($paymentMethod);

            return response()->json([
                'message' => 'Payment method status updated successfully',
                'data' => $updatedPaymentMethod
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update payment method status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a payment method.
     */
    public function destroyPaymentMethod(int $id): JsonResponse
    {
        try {
            $paymentMethod = $this->paymentMethodService->getPaymentMethodById($id);

            if (!$paymentMethod) {
                return response()->json([
                    'message' => 'Payment method not found'
                ], 404);
            }



            $this->paymentMethodService->deletePaymentMethod($paymentMethod);

            return response()->json([
                'message' => 'Payment method deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show refunds page.
     */
    public function refunds(): View
    {
        return view('transaction::payments.refunds');
    }

    /**
     * Show payments for a specific transaction.
     */
    public function byTransaction(int $transactionId): View
    {
        $transaction = $this->transactionService->getTransactionById($transactionId);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        return view('transaction::payments.by-transaction', compact('transaction'));
    }
}