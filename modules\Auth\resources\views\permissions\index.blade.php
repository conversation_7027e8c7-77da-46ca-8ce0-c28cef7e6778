@extends('layouts.master')

@section('title', 'إدارة الصلاحيات')

@section('content')
<div class="container-fluid px-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <i class="fas fa-key text-blue-600"></i>
                        إدارة الصلاحيات
                    </h1>
                    <div class="flex space-x-2">
                        <button onclick="openBulkCreateModal()" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus-circle mr-2"></i>
                            إنشاء مجموعة
                        </button>
                        <button onclick="openCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            إضافة صلاحية
                        </button>
                    </div>
                </div>
            </div>
        </div>



        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                        <input type="text" id="search-input" placeholder="البحث في الصلاحيات..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المجموعة</label>
                        <select id="group-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع المجموعات</option>
                            @foreach($groups as $group)
                                <option value="{{ $group }}">{{ $group }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحارس</label>
                        <select id="guard-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع الأنواع</option>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="resetFilters()" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-undo mr-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">قائمة الصلاحيات</h2>
                    <div class="flex space-x-2">
                        <button onclick="bulkDelete()" class="inline-flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            حذف المحدد
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table id="permissions-table" class="w-full text-sm text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th class="px-6 py-3">
                                    <input type="checkbox" id="select-all" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                </th>
                                <th class="px-6 py-3">#</th>
                                <th class="px-6 py-3">اسم الصلاحية</th>
                                <th class="px-6 py-3">المجموعة</th>
                                <th class="px-6 py-3">نوع الحارس</th>
                                <th class="px-6 py-3">عدد الأدوار</th>
                                <th class="px-6 py-3">تاريخ الإنشاء</th>
                                <th class="px-6 py-3">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Permission Modal -->
<div id="permission-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 id="modal-title" class="text-lg font-semibold text-gray-900">إضافة صلاحية جديدة</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="permission-form">
                @csrf
                <input type="hidden" id="permission-id" name="permission_id">
                <input type="hidden" id="form-method" name="_method" value="POST">
                
                <div class="px-6 py-4 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية *</label>
                        <input type="text" id="permission-name" name="name" required placeholder="مثال: users.create" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div id="name-error" class="text-red-500 text-sm mt-1 hidden"></div>
                        <p class="text-xs text-gray-500 mt-1">استخدم النمط: group.action (مثال: users.create, posts.edit)</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحارس *</label>
                        <select id="permission-guard" name="guard_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر نوع الحارس</option>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                        <div id="guard-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" id="submit-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Create Modal -->
<div id="bulk-create-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">إنشاء مجموعة صلاحيات</h3>
                    <button onclick="closeBulkCreateModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="bulk-create-form">
                @csrf
                
                <div class="px-6 py-4 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المجموعة *</label>
                        <input type="text" id="group-name" placeholder="مثال: users, posts, orders" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحارس *</label>
                        <select id="bulk-guard" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الإجراءات</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <label class="flex items-center">
                                <input type="checkbox" value="index" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">عرض القائمة (index)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="show" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">عرض التفاصيل (show)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="create" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">إنشاء (create)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="store" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">حفظ (store)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="edit" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">تعديل (edit)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="update" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">تحديث (update)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="destroy" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">حذف (destroy)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="manage" class="action-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-2 text-sm">إدارة (manage)</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">معاينة الصلاحيات</label>
                        <div id="permissions-preview" class="bg-gray-50 rounded-lg p-4 min-h-20">
                            <p class="text-gray-500 text-sm">اختر اسم المجموعة والإجراءات لمعاينة الصلاحيات</p>
                        </div>
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeBulkCreateModal()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus-circle mr-2"></i>
                        إنشاء الصلاحيات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<script>
let permissionsTable;
let isEditMode = false;

$(document).ready(function() {
    // Initialize DataTable
    permissionsTable = $('#permissions-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("permissions.data") }}',
            data: function(d) {
                d.group = $('#group-filter').val();
                d.guard_name = $('#guard-filter').val();
                d.search.value = $('#search-input').val();
            }
        },
        columns: [
            { 
                data: 'id', 
                name: 'id', 
                orderable: false, 
                searchable: false,
                render: function(data) {
                    return `<input type="checkbox" class="row-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" value="${data}">`;
                }
            },
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'group', name: 'group', orderable: false },
            { data: 'guard_badge', name: 'guard_name', orderable: false },
            { data: 'roles_count', name: 'roles_count', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        pageLength: 25,
        dom: 'rtip'
    });

    // Search functionality
    $('#search-input').on('keyup', function() {
        permissionsTable.draw();
    });

    // Filter functionality
    $('#group-filter, #guard-filter').on('change', function() {
        permissionsTable.draw();
    });

    // Select all checkbox
    $('#select-all').on('change', function() {
        $('.row-checkbox').prop('checked', this.checked);
    });

    // Form submissions
    $('#permission-form').on('submit', function(e) {
        e.preventDefault();
        submitForm();
    });

    $('#bulk-create-form').on('submit', function(e) {
        e.preventDefault();
        submitBulkCreate();
    });

    // Bulk create preview
    $('#group-name, .action-checkbox').on('change', function() {
        updatePermissionsPreview();
    });
});

function openCreateModal() {
    isEditMode = false;
    $('#modal-title').text('إضافة صلاحية جديدة');
    $('#permission-form')[0].reset();
    $('#permission-id').val('');
    $('#form-method').val('POST');
    clearErrors();
    $('#permission-modal').removeClass('hidden');
}

function editPermission(id) {
    isEditMode = true;
    $('#modal-title').text('تعديل الصلاحية');
    $('#form-method').val('PUT');
    clearErrors();
    
    // Fetch permission data
    $.get(`/admin/permissions/${id}`, function(response) {
        $('#permission-id').val(response.id);
        $('#permission-name').val(response.name);
        $('#permission-guard').val(response.guard_name);
        $('#permission-modal').removeClass('hidden');
    }).fail(function() {
        Swal.fire('خطأ!', 'حدث خطأ أثناء جلب بيانات الصلاحية', 'error');
    });
}

function deletePermission(id) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/permissions/${id}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    Swal.fire('تم الحذف!', 'تم حذف الصلاحية بنجاح', 'success');
                    permissionsTable.ajax.reload();
                },
                error: function(xhr) {
                    let message = 'حدث خطأ أثناء حذف الصلاحية';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Swal.fire('خطأ!', message, 'error');
                }
            });
        }
    });
}

function openBulkCreateModal() {
    $('#bulk-create-form')[0].reset();
    $('#permissions-preview').html('<p class="text-gray-500 text-sm">اختر اسم المجموعة والإجراءات لمعاينة الصلاحيات</p>');
    $('#bulk-create-modal').removeClass('hidden');
}

function closeBulkCreateModal() {
    $('#bulk-create-modal').addClass('hidden');
}

function bulkDelete() {
    const selectedIds = $('.row-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selectedIds.length === 0) {
        Swal.fire('تنبيه!', 'يرجى تحديد صلاحية واحدة على الأقل للحذف', 'warning');
        return;
    }

    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: `سيتم حذف ${selectedIds.length} صلاحية. لن تتمكن من التراجع عن هذا الإجراء!`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '/admin/permissions/bulk-delete',
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    permission_ids: selectedIds
                },
                success: function(response) {
                    Swal.fire('تم الحذف!', 'تم حذف الصلاحيات المحددة بنجاح', 'success');
                    permissionsTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                },
                error: function(xhr) {
                    let message = 'حدث خطأ أثناء حذف الصلاحيات';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Swal.fire('خطأ!', message, 'error');
                }
            });
        }
    });
}

function submitForm() {
    const formData = new FormData($('#permission-form')[0]);
    const url = isEditMode ? `/admin/permissions/${$('#permission-id').val()}` : '/admin/permissions';
    
    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            Swal.fire('نجح!', isEditMode ? 'تم تحديث الصلاحية بنجاح' : 'تم إنشاء الصلاحية بنجاح', 'success');
            closeModal();
            permissionsTable.ajax.reload();
        },
        error: function(xhr) {
            if (xhr.status === 422) {
                displayErrors(xhr.responseJSON.errors);
            } else {
                Swal.fire('خطأ!', 'حدث خطأ أثناء حفظ البيانات', 'error');
            }
        }
    });
}

function submitBulkCreate() {
    const groupName = $('#group-name').val().trim();
    const guardName = $('#bulk-guard').val();
    const selectedActions = $('.action-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (!groupName) {
        Swal.fire('خطأ!', 'يرجى إدخال اسم المجموعة', 'error');
        return;
    }

    if (selectedActions.length === 0) {
        Swal.fire('خطأ!', 'يرجى تحديد إجراء واحد على الأقل', 'error');
        return;
    }

    const permissions = selectedActions.map(action => ({
        name: `${groupName}.${action}`,
        guard_name: guardName
    }));

    $.ajax({
        url: '/admin/permissions/bulk-create',
        type: 'POST',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            permissions: permissions
        },
        success: function(response) {
            Swal.fire('نجح!', 'تم إنشاء الصلاحيات بنجاح', 'success');
            closeBulkCreateModal();
            permissionsTable.ajax.reload();
        },
        error: function(xhr) {
            let message = 'حدث خطأ أثناء إنشاء الصلاحيات';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            Swal.fire('خطأ!', message, 'error');
        }
    });
}

function updatePermissionsPreview() {
    const groupName = $('#group-name').val().trim();
    const selectedActions = $('.action-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (!groupName || selectedActions.length === 0) {
        $('#permissions-preview').html('<p class="text-gray-500 text-sm">اختر اسم المجموعة والإجراءات لمعاينة الصلاحيات</p>');
        return;
    }

    const permissions = selectedActions.map(action => `${groupName}.${action}`);
    const html = permissions.map(permission => 
        `<span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-2 mb-2">${permission}</span>`
    ).join('');

    $('#permissions-preview').html(html);
}

function closeModal() {
    $('#permission-modal').addClass('hidden');
    clearErrors();
}

function resetFilters() {
    $('#search-input').val('');
    $('#group-filter').val('');
    $('#guard-filter').val('');
    permissionsTable.draw();
}

function clearErrors() {
    $('.text-red-500').addClass('hidden');
    $('.border-red-500').removeClass('border-red-500');
}

function displayErrors(errors) {
    clearErrors();
    
    for (let field in errors) {
        const errorElement = $(`#${field}-error`);
        const inputElement = $(`#permission-${field}`);
        
        if (errorElement.length) {
            errorElement.text(errors[field][0]).removeClass('hidden');
            inputElement.addClass('border-red-500');
        }
    }
}
</script>
@endpush