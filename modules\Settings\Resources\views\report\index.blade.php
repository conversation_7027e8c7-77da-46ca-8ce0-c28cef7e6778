@extends('layouts.master')

@section('title', 'Report Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Report Settings</h1>
                        <p class="text-emerald-100">Configure report generation and export settings</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-emerald-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-emerald-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-emerald-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-emerald-200 mx-2"></i>
                                <span class="text-white font-medium">Report Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Report Configuration -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Report Configuration</h3>
                <p class="text-gray-600 mt-1">Configure default report settings and formats</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <!-- Default Report Format -->
                <div>
                    <label for="default_report_format" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Report Format
                    </label>
                    <select id="default_report_format" name="default_report_format" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="pdf" {{ (isset($reportSettings['default_report_format']) && $reportSettings['default_report_format']->value == 'pdf') ? 'selected' : '' }}>PDF</option>
                        <option value="excel" {{ (isset($reportSettings['default_report_format']) && $reportSettings['default_report_format']->value == 'excel') ? 'selected' : '' }}>Excel</option>
                        <option value="csv" {{ (isset($reportSettings['default_report_format']) && $reportSettings['default_report_format']->value == 'csv') ? 'selected' : '' }}>CSV</option>
                        <option value="html" {{ (isset($reportSettings['default_report_format']) && $reportSettings['default_report_format']->value == 'html') ? 'selected' : '' }}>HTML</option>
                    </select>
                </div>

                <!-- Report Logo -->
                <div>
                    <label for="report_logo" class="block text-sm font-medium text-gray-700 mb-2">
                        Report Logo
                    </label>
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <img class="h-16 w-16 rounded-lg object-cover border border-gray-300" 
                                 src="https://via.placeholder.com/64x64?text=Logo" alt="Current logo">
                        </div>
                        <div class="flex-1">
                            <input type="file" id="report_logo" name="report_logo" accept="image/*" 
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100">
                            <p class="text-sm text-gray-500 mt-1">PNG, JPG up to 1MB (recommended: 200x100px)</p>
                        </div>
                    </div>
                </div>

                <!-- Report Footer -->
                <div>
                    <label for="report_footer" class="block text-sm font-medium text-gray-700 mb-2">
                        Report Footer Text
                    </label>
                    <textarea id="report_footer" name="report_footer" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                              placeholder="Enter footer text for reports">{{ isset($reportSettings['report_footer']) ? $reportSettings['report_footer']->value : 'Generated by Restaurant POS System' }}</textarea>
                </div>

                <!-- Report Retention -->
                <div>
                    <label for="report_retention_days" class="block text-sm font-medium text-gray-700 mb-2">
                        Report Retention Period (days)
                    </label>
                    <input type="number" id="report_retention_days" name="report_retention_days" 
                           value="{{ isset($reportSettings['report_retention_days']) ? $reportSettings['report_retention_days']->value : '365' }}" 
                           min="30" max="3650"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    <p class="text-sm text-gray-500 mt-1">How long to keep generated reports before automatic deletion</p>
                </div>

                <!-- Auto Email Reports -->
                <div class="flex items-center">
                    <input type="checkbox" id="auto_email_reports" name="auto_email_reports" value="1" 
                           {{ (isset($reportSettings['auto_email_reports']) && $reportSettings['auto_email_reports']->value == '1') ? 'checked' : '' }}
                           class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                    <label for="auto_email_reports" class="ml-2 text-sm text-gray-700">
                        Automatically email daily reports to management
                    </label>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        Reset to Defaults
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-lg hover:bg-emerald-700 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Scheduled Reports -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Scheduled Reports</h3>
                        <p class="text-gray-600 mt-1">Configure automatic report generation</p>
                    </div>
                    <button class="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add Schedule
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="space-y-4">
                    <!-- Daily Sales Report -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">Daily Sales Report</h4>
                                <p class="text-sm text-gray-600">Sent every day at 11:00 PM</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                                <button class="text-emerald-600 hover:text-emerald-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p><strong>Recipients:</strong> <EMAIL>, <EMAIL></p>
                            <p><strong>Format:</strong> PDF</p>
                            <p><strong>Next Run:</strong> Today at 11:00 PM</p>
                        </div>
                    </div>

                    <!-- Weekly Inventory Report -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">Weekly Inventory Report</h4>
                                <p class="text-sm text-gray-600">Sent every Monday at 9:00 AM</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                                <button class="text-emerald-600 hover:text-emerald-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p><strong>Recipients:</strong> <EMAIL></p>
                            <p><strong>Format:</strong> Excel</p>
                            <p><strong>Next Run:</strong> Monday at 9:00 AM</p>
                        </div>
                    </div>

                    <!-- Monthly Financial Report -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">Monthly Financial Report</h4>
                                <p class="text-sm text-gray-600">Sent on the 1st of each month at 8:00 AM</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Paused</span>
                                <button class="text-emerald-600 hover:text-emerald-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p><strong>Recipients:</strong> <EMAIL>, <EMAIL></p>
                            <p><strong>Format:</strong> PDF</p>
                            <p><strong>Next Run:</strong> Next month on 1st at 8:00 AM</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Templates -->
    <div class="bg-white rounded-lg shadow-lg mt-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Report Templates</h3>
            <p class="text-gray-600 mt-1">Customize report templates and layouts</p>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Sales Report Template -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-lg font-medium text-gray-900">Sales Report</h4>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Default</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Daily, weekly, and monthly sales analytics</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Last modified: 2 days ago</span>
                        <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">
                            <i class="fas fa-edit mr-1"></i>
                            Customize
                        </button>
                    </div>
                </div>

                <!-- Inventory Report Template -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-lg font-medium text-gray-900">Inventory Report</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Stock levels, low inventory alerts, and usage</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Last modified: 1 week ago</span>
                        <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">
                            <i class="fas fa-edit mr-1"></i>
                            Customize
                        </button>
                    </div>
                </div>

                <!-- Financial Report Template -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-lg font-medium text-gray-900">Financial Report</h4>
                        <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Custom</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Revenue, expenses, profit/loss statements</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Last modified: 3 days ago</span>
                        <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">
                            <i class="fas fa-edit mr-1"></i>
                            Customize
                        </button>
                    </div>
                </div>

                <!-- Customer Report Template -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-lg font-medium text-gray-900">Customer Report</h4>
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">Draft</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Customer analytics, loyalty, and preferences</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Last modified: 5 days ago</span>
                        <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">
                            <i class="fas fa-edit mr-1"></i>
                            Customize
                        </button>
                    </div>
                </div>

                <!-- Staff Performance Template -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-lg font-medium text-gray-900">Staff Performance</h4>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Beta</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Employee productivity and performance metrics</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Last modified: 1 day ago</span>
                        <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">
                            <i class="fas fa-edit mr-1"></i>
                            Customize
                        </button>
                    </div>
                </div>

                <!-- Add New Template Card -->
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center hover:border-gray-400 transition-colors duration-200 cursor-pointer">
                    <div class="text-center">
                        <i class="fas fa-plus text-gray-400 text-2xl mb-2"></i>
                        <p class="text-gray-500 text-sm">Create New Template</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handler
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Add loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Report settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });

    // Reset to defaults handler
    $('button:contains("Reset to Defaults")').on('click', function() {
        Swal.fire({
            title: 'Reset to Defaults?',
            text: 'This will reset all report settings to their default values.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, reset!'
        }).then((result) => {
            if (result.isConfirmed) {
                // Reset form values
                $('#default_report_format').val('pdf');
                $('#report_footer').val('Generated by Restaurant POS System');
                $('#report_retention_days').val('365');
                $('#auto_email_reports').prop('checked', false);
                
                Swal.fire({
                    icon: 'success',
                    title: 'Reset Complete!',
                    text: 'Settings have been reset to defaults.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Template customization handlers
    $('.text-emerald-600:contains("Customize")').on('click', function() {
        const templateName = $(this).closest('.border').find('h4').text();
        
        Swal.fire({
            title: `Customize ${templateName}`,
            text: 'Template customization feature coming soon!',
            icon: 'info',
            confirmButtonColor: '#10b981'
        });
    });

    // Add schedule handler
    $('button:contains("Add Schedule")').on('click', function() {
        Swal.fire({
            title: 'Add New Schedule',
            text: 'Schedule creation feature coming soon!',
            icon: 'info',
            confirmButtonColor: '#10b981'
        });
    });
});
</script>
@endpush
