<?php

namespace Modules\Delivery\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Branch;
use Modules\Delivery\Entities\DeliveryZone;

class DeliveryZoneFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = DeliveryZone::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'branch_id' => Branch::factory(),
            'name' => $this->faker->randomElement([
                'Central Business District',
                'Residential Area North',
                'Residential Area South',
                'Industrial Zone',
                'Shopping District',
                'University Area',
                'Airport Zone',
                'Suburban Area',
                'Downtown Core',
                'Waterfront District'
            ]) . ' ' . $this->faker->numberBetween(1, 10),
            'description' => $this->faker->sentence(10),
            'polygon_coordinates' => $this->generatePolygonCoordinates(),
            'delivery_fee' => $this->faker->randomFloat(2, 5.00, 25.00),
            'minimum_order_amount' => $this->faker->randomFloat(2, 20.00, 100.00),
            'estimated_delivery_time_minutes' => $this->faker->numberBetween(15, 60),
            'priority' => $this->faker->numberBetween(1, 5),
            'is_active' => $this->faker->boolean(85), // 85% chance of being active
        ];
    }

    /**
     * Generate polygon coordinates for a delivery zone.
     */
    private function generatePolygonCoordinates(): array
    {
        // Generate a rectangular polygon around Jakarta area
        $centerLat = $this->faker->latitude(-6.3, -6.1);
        $centerLng = $this->faker->longitude(106.7, 106.9);
        
        $size = $this->faker->randomFloat(3, 0.01, 0.05); // Size of the zone
        
        return [
            [$centerLat - $size, $centerLng - $size], // Bottom-left
            [$centerLat - $size, $centerLng + $size], // Bottom-right
            [$centerLat + $size, $centerLng + $size], // Top-right
            [$centerLat + $size, $centerLng - $size], // Top-left
        ];
    }

    /**
     * Generate a more complex polygon (hexagon).
     */
    private function generateHexagonCoordinates(float $centerLat, float $centerLng, float $radius): array
    {
        $coordinates = [];
        
        for ($i = 0; $i < 6; $i++) {
            $angle = ($i * 60) * (M_PI / 180); // Convert to radians
            $lat = $centerLat + ($radius * cos($angle));
            $lng = $centerLng + ($radius * sin($angle));
            $coordinates[] = [$lat, $lng];
        }
        
        return $coordinates;
    }

    /**
     * Indicate that the zone is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the zone is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Set high priority for the zone.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(4, 5),
        ]);
    }

    /**
     * Set low priority for the zone.
     */
    public function lowPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(1, 2),
        ]);
    }

    /**
     * Set premium pricing for the zone.
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'delivery_fee' => $this->faker->randomFloat(2, 20.00, 35.00),
            'minimum_order_amount' => $this->faker->randomFloat(2, 75.00, 150.00),
            'estimated_delivery_time_minutes' => $this->faker->numberBetween(45, 75),
        ]);
    }

    /**
     * Set budget pricing for the zone.
     */
    public function budget(): static
    {
        return $this->state(fn (array $attributes) => [
            'delivery_fee' => $this->faker->randomFloat(2, 3.00, 10.00),
            'minimum_order_amount' => $this->faker->randomFloat(2, 15.00, 40.00),
            'estimated_delivery_time_minutes' => $this->faker->numberBetween(15, 30),
        ]);
    }

    /**
     * Set fast delivery for the zone.
     */
    public function fastDelivery(): static
    {
        return $this->state(fn (array $attributes) => [
            'estimated_delivery_time_minutes' => $this->faker->numberBetween(15, 25),
            'delivery_fee' => $attributes['delivery_fee'] * 1.2, // 20% premium for fast delivery
        ]);
    }

    /**
     * Set the zone for a specific branch.
     */
    public function forBranch(int $branchId): static
    {
        return $this->state(fn (array $attributes) => [
            'branch_id' => $branchId,
        ]);
    }

    /**
     * Create a zone with specific coordinates.
     */
    public function withCoordinates(array $coordinates): static
    {
        return $this->state(fn (array $attributes) => [
            'polygon_coordinates' => $coordinates,
        ]);
    }

    /**
     * Create a circular zone (approximated with octagon).
     */
    public function circular(float $centerLat, float $centerLng, float $radiusKm = 2.0): static
    {
        $coordinates = [];
        $earthRadius = 6371; // Earth's radius in km
        
        // Convert radius to degrees (approximate)
        $radiusDeg = $radiusKm / $earthRadius * (180 / M_PI);
        
        // Create octagon
        for ($i = 0; $i < 8; $i++) {
            $angle = ($i * 45) * (M_PI / 180); // Convert to radians
            $lat = $centerLat + ($radiusDeg * cos($angle));
            $lng = $centerLng + ($radiusDeg * sin($angle) / cos($centerLat * M_PI / 180));
            $coordinates[] = [$lat, $lng];
        }
        
        return $this->state(fn (array $attributes) => [
            'polygon_coordinates' => $coordinates,
        ]);
    }

    /**
     * Create a zone covering central Jakarta.
     */
    public function centralJakarta(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Central Jakarta Zone',
            'description' => 'Central business district with high-rise buildings and shopping centers',
            'polygon_coordinates' => [
                [-6.1744, 106.8227],
                [-6.1744, 106.8427],
                [-6.1944, 106.8427],
                [-6.1944, 106.8227],
            ],
            'delivery_fee' => 15.00,
            'minimum_order_amount' => 50.00,
            'estimated_delivery_time_minutes' => 30,
            'priority' => 1,
        ]);
    }

    /**
     * Create a zone covering south Jakarta.
     */
    public function southJakarta(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'South Jakarta Zone',
            'description' => 'Upscale residential area with shopping malls',
            'polygon_coordinates' => [
                [-6.2244, 106.7927],
                [-6.2244, 106.8327],
                [-6.2644, 106.8327],
                [-6.2644, 106.7927],
            ],
            'delivery_fee' => 12.00,
            'minimum_order_amount' => 40.00,
            'estimated_delivery_time_minutes' => 35,
            'priority' => 2,
        ]);
    }

    /**
     * Create a zone with express delivery.
     */
    public function express(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $attributes['name'] . ' (Express)',
            'estimated_delivery_time_minutes' => $this->faker->numberBetween(10, 20),
            'delivery_fee' => $attributes['delivery_fee'] * 1.5, // 50% premium
            'priority' => 5,
        ]);
    }
}