{"info": {"name": "Delivery Module API Collection", "description": "Comprehensive API collection for the Delivery module including personnel management, zones, assignments, tracking, and analytics.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Delivery Personnel", "item": [{"name": "List Personnel", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel?status=active&per_page=20&page=1", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel"], "query": [{"key": "status", "value": "active", "description": "Filter by status (active, inactive, busy, offline, on_break, suspended)"}, {"key": "branch_id", "value": "", "disabled": true, "description": "Filter by branch ID"}, {"key": "available_only", "value": "", "disabled": true, "description": "Show only available personnel"}, {"key": "search", "value": "", "disabled": true, "description": "Search by name or phone"}, {"key": "per_page", "value": "20", "description": "Items per page"}, {"key": "page", "value": "1", "description": "Page number"}]}}}, {"name": "Create Personnel", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": 10,\n  \"branch_id\": 1,\n  \"vehicle_type\": \"motorcycle\",\n  \"vehicle_model\": \"Honda CBR\",\n  \"vehicle_plate_number\": \"ABC-123\",\n  \"license_number\": \"DL123456789\",\n  \"phone_number\": \"+**********\",\n  \"emergency_contact\": \"<PERSON>\",\n  \"emergency_phone\": \"+**********\",\n  \"max_concurrent_deliveries\": 3,\n  \"delivery_radius_km\": 15,\n  \"hourly_rate\": 15.00,\n  \"commission_rate\": 10.0,\n  \"working_hours\": {\n    \"monday\": {\"enabled\": true, \"start\": \"09:00\", \"end\": \"18:00\"},\n    \"tuesday\": {\"enabled\": true, \"start\": \"09:00\", \"end\": \"18:00\"},\n    \"wednesday\": {\"enabled\": true, \"start\": \"09:00\", \"end\": \"18:00\"},\n    \"thursday\": {\"enabled\": true, \"start\": \"09:00\", \"end\": \"18:00\"},\n    \"friday\": {\"enabled\": true, \"start\": \"09:00\", \"end\": \"18:00\"},\n    \"saturday\": {\"enabled\": false, \"start\": null, \"end\": null},\n    \"sunday\": {\"enabled\": false, \"start\": null, \"end\": null}\n  }\n}"}, "url": {"raw": "{{base_url}}/api/delivery/personnel", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel"]}}}, {"name": "Get Personnel by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel", "1"]}}}, {"name": "Update Personnel", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"branch_id\": 1,\n  \"vehicle_type\": \"motorcycle\",\n  \"vehicle_model\": \"Honda CBR 150\",\n  \"vehicle_plate_number\": \"ABC-123\",\n  \"license_number\": \"DL123456789\",\n  \"phone_number\": \"+**********\",\n  \"emergency_contact\": \"<PERSON>\",\n  \"emergency_phone\": \"+**********\",\n  \"max_concurrent_deliveries\": 4,\n  \"delivery_radius_km\": 20,\n  \"hourly_rate\": 16.00,\n  \"commission_rate\": 12.0,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/personnel/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel", "1"]}}}, {"name": "Delete Personnel", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel", "1"]}}}, {"name": "Verify Personnel", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"verification_status\": \"verified\",\n  \"notes\": \"All documents verified successfully\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/personnel/1/verify", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel", "1", "verify"]}}}, {"name": "Get Personnel Performance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel/1/performance?period=month", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel", "1", "performance"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year"}]}}}, {"name": "Get Available Personnel", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel/available/list", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel", "available", "list"]}}}]}, {"name": "Delivery Zones", "item": [{"name": "List Zones", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/zones?branch_id=1&active_only=true", "host": ["{{base_url}}"], "path": ["api", "delivery", "zones"], "query": [{"key": "branch_id", "value": "1", "description": "Filter by branch ID"}, {"key": "active_only", "value": "true", "description": "Show only active zones"}, {"key": "search", "value": "", "disabled": true, "description": "Search by name"}]}}}, {"name": "Create Zone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Downtown Area\",\n  \"branch_id\": 1,\n  \"coordinates\": [\n    {\"latitude\": 40.7128, \"longitude\": -74.0060},\n    {\"latitude\": 40.7589, \"longitude\": -73.9851},\n    {\"latitude\": 40.7505, \"longitude\": -73.9934},\n    {\"latitude\": 40.7128, \"longitude\": -74.0060}\n  ],\n  \"delivery_fee\": 5.99,\n  \"minimum_order_amount\": 15.00,\n  \"estimated_delivery_time\": 30,\n  \"is_active\": true,\n  \"priority\": 1,\n  \"description\": \"Downtown delivery zone covering main business district\",\n  \"color\": \"#FF5733\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/zones", "host": ["{{base_url}}"], "path": ["api", "delivery", "zones"]}}}, {"name": "Get Zone by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/zones/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "zones", "1"]}}}, {"name": "Update Zone", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Downtown Area - Updated\",\n  \"delivery_fee\": 6.99,\n  \"minimum_order_amount\": 20.00,\n  \"estimated_delivery_time\": 25,\n  \"is_active\": true,\n  \"description\": \"Updated downtown delivery zone\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/zones/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "zones", "1"]}}}, {"name": "Check Delivery Zone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"branch_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/delivery/zones/check", "host": ["{{base_url}}"], "path": ["api", "delivery", "zones", "check"]}}}]}, {"name": "Delivery Assignments", "item": [{"name": "List Assignments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/assignments?status=in_transit&per_page=20&page=1", "host": ["{{base_url}}"], "path": ["api", "delivery", "assignments"], "query": [{"key": "status", "value": "in_transit", "description": "pending, assigned, picked_up, in_transit, delivered, cancelled, failed"}, {"key": "personnel_id", "value": "", "disabled": true, "description": "Filter by delivery personnel ID"}, {"key": "zone_id", "value": "", "disabled": true, "description": "Filter by delivery zone ID"}, {"key": "date_from", "value": "", "disabled": true, "description": "Filter from date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "disabled": true, "description": "Filter to date (YYYY-MM-DD)"}, {"key": "priority", "value": "", "disabled": true, "description": "low, normal, high, urgent"}, {"key": "search", "value": "", "disabled": true, "description": "Search in address or tracking code"}, {"key": "per_page", "value": "20", "description": "Items per page"}, {"key": "page", "value": "1", "description": "Page number"}]}}}, {"name": "Get Assignment by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/assignments/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "assignments", "1"]}}}, {"name": "Assign Delivery", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": 123,\n  \"delivery_personnel_id\": 5,\n  \"delivery_zone_id\": 2,\n  \"pickup_time\": \"2024-01-15 14:30:00\",\n  \"delivery_time\": \"2024-01-15 15:00:00\",\n  \"delivery_address\": \"123 Main St, City, State 12345\",\n  \"delivery_latitude\": 40.7128,\n  \"delivery_longitude\": -74.0060,\n  \"delivery_fee\": 5.99,\n  \"estimated_delivery_time\": 30,\n  \"special_instructions\": \"Ring doorbell twice, leave at door if no answer\",\n  \"priority\": \"normal\",\n  \"auto_assign\": false\n}"}, "url": {"raw": "{{base_url}}/api/delivery/assignments/assign", "host": ["{{base_url}}"], "path": ["api", "delivery", "assignments", "assign"]}}}, {"name": "Auto-Assign Delivery", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": 123,\n  \"criteria\": \"distance\",\n  \"max_distance\": 10,\n  \"priority_weight\": 0.3,\n  \"distance_weight\": 0.4,\n  \"load_weight\": 0.3\n}"}, "url": {"raw": "{{base_url}}/api/delivery/assignments/auto-assign", "host": ["{{base_url}}"], "path": ["api", "delivery", "assignments", "auto-assign"]}}}, {"name": "Update Assignment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"delivery_personnel_id\": 6,\n  \"pickup_time\": \"2024-01-15 14:45:00\",\n  \"delivery_time\": \"2024-01-15 15:15:00\",\n  \"special_instructions\": \"Updated instructions - call before delivery\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/assignments/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "assignments", "1"]}}}, {"name": "Update Delivery Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"delivered\",\n  \"notes\": \"Delivered successfully to customer\",\n  \"delivery_time\": \"2024-01-15 15:15:00\",\n  \"customer_signature\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==\",\n  \"delivery_photo\": \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=\",\n  \"customer_rating\": 5,\n  \"customer_feedback\": \"Excellent service, very professional driver!\",\n  \"tip_amount\": 3.50\n}"}, "url": {"raw": "{{base_url}}/api/delivery/assignments/1/status", "host": ["{{base_url}}"], "path": ["api", "delivery", "assignments", "1", "status"]}}}]}, {"name": "Location Tracking", "item": [{"name": "Update Location", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignment_id\": 1,\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"accuracy\": 5.0,\n  \"speed\": 25.5,\n  \"heading\": 180.0,\n  \"altitude\": 10.0,\n  \"battery_level\": 85,\n  \"is_manual\": false,\n  \"notes\": \"Approaching destination\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/tracking/1/location", "host": ["{{base_url}}"], "path": ["api", "delivery", "tracking", "1", "location"]}}}, {"name": "Get Tracking Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/tracking/1/tracking?limit=50", "host": ["{{base_url}}"], "path": ["api", "delivery", "tracking", "1", "tracking"], "query": [{"key": "limit", "value": "50", "description": "Number of tracking records"}, {"key": "from", "value": "", "disabled": true, "description": "Start datetime (YYYY-MM-DD HH:MM:SS)"}, {"key": "to", "value": "", "disabled": true, "description": "End datetime (YYYY-MM-DD HH:MM:SS)"}]}}}, {"name": "Get Current Location", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/tracking/1/current-location", "host": ["{{base_url}}"], "path": ["api", "delivery", "tracking", "1", "current-location"]}}}]}, {"name": "Delivery Reviews", "item": [{"name": "List Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/reviews?per_page=20&page=1", "host": ["{{base_url}}"], "path": ["api", "delivery", "reviews"], "query": [{"key": "per_page", "value": "20", "description": "Items per page"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "rating", "value": "", "disabled": true, "description": "Filter by rating (1-5)"}, {"key": "personnel_id", "value": "", "disabled": true, "description": "Filter by personnel ID"}]}}}, {"name": "Create Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignment_id\": 1,\n  \"rating\": 5,\n  \"comment\": \"Excellent delivery service! Very professional and on time.\",\n  \"delivery_time_rating\": 5,\n  \"personnel_rating\": 5,\n  \"food_condition_rating\": 5\n}"}, "url": {"raw": "{{base_url}}/api/delivery/reviews", "host": ["{{base_url}}"], "path": ["api", "delivery", "reviews"]}}}, {"name": "Get Review by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/reviews/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "reviews", "1"]}}}, {"name": "Verify Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"is_verified\": true,\n  \"admin_notes\": \"Review verified as legitimate\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/reviews/1/verify", "host": ["{{base_url}}"], "path": ["api", "delivery", "reviews", "1", "verify"]}}}, {"name": "Get Personnel Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/reviews/personnel/1?per_page=20", "host": ["{{base_url}}"], "path": ["api", "delivery", "reviews", "personnel", "1"], "query": [{"key": "per_page", "value": "20", "description": "Items per page"}]}}}]}, {"name": "Delivery Tips", "item": [{"name": "List Tips", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/tips?per_page=20&page=1", "host": ["{{base_url}}"], "path": ["api", "delivery", "tips"], "query": [{"key": "per_page", "value": "20", "description": "Items per page"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "personnel_id", "value": "", "disabled": true, "description": "Filter by personnel ID"}, {"key": "date_from", "value": "", "disabled": true, "description": "Filter from date"}, {"key": "date_to", "value": "", "disabled": true, "description": "Filter to date"}]}}}, {"name": "Create Tip", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignment_id\": 1,\n  \"amount\": 5.00,\n  \"payment_method\": \"cash\",\n  \"notes\": \"Great service, thank you!\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/tips", "host": ["{{base_url}}"], "path": ["api", "delivery", "tips"]}}}, {"name": "Get Tip by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/tips/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "tips", "1"]}}}, {"name": "Process Tip Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"payment_method\": \"credit_card\",\n  \"payment_reference\": \"txn_**********\",\n  \"processed_at\": \"2024-01-15 15:30:00\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/tips/1/process-payment", "host": ["{{base_url}}"], "path": ["api", "delivery", "tips", "1", "process-payment"]}}}, {"name": "Get Personnel Tips", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/tips/personnel/1?period=month", "host": ["{{base_url}}"], "path": ["api", "delivery", "tips", "personnel", "1"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year"}]}}}]}, {"name": "Analytics & Reports", "item": [{"name": "Get Delivery Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/analytics/stats?period=month", "host": ["{{base_url}}"], "path": ["api", "delivery", "analytics", "stats"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year"}, {"key": "personnel_id", "value": "", "disabled": true, "description": "Filter by personnel"}, {"key": "zone_id", "value": "", "disabled": true, "description": "Filter by zone"}]}}}, {"name": "Get Personnel Performance Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/analytics/personnel/1/performance?period=month", "host": ["{{base_url}}"], "path": ["api", "delivery", "analytics", "personnel", "1", "performance"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year"}]}}}]}, {"name": "Utility Functions", "item": [{"name": "Calculate Delivery Fee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"order_amount\": 25.99,\n  \"branch_id\": 1,\n  \"delivery_time\": \"2024-01-15 15:00:00\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/utils/calculate-fee", "host": ["{{base_url}}"], "path": ["api", "delivery", "utils", "calculate-fee"]}}}, {"name": "Get Available Personnel (Utility)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/utils/available-personnel?latitude=40.7128&longitude=-74.0060&max_distance=10", "host": ["{{base_url}}"], "path": ["api", "delivery", "utils", "available-personnel"], "query": [{"key": "latitude", "value": "40.7128", "description": "Delivery latitude"}, {"key": "longitude", "value": "-74.0060", "description": "Delivery longitude"}, {"key": "max_distance", "value": "10", "description": "Maximum distance in km"}]}}}]}, {"name": "Public Endpoints", "item": [{"name": "Check Zone (Public)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"branch_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/delivery/public/check-zone", "host": ["{{base_url}}"], "path": ["api", "delivery", "public", "check-zone"]}}}, {"name": "<PERSON><PERSON> (Public)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"order_amount\": 25.99,\n  \"branch_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/delivery/public/calculate-fee", "host": ["{{base_url}}"], "path": ["api", "delivery", "public", "calculate-fee"]}}}]}, {"name": "Customer App", "item": [{"name": "My Deliveries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/customer/my-deliveries?status=active&per_page=20", "host": ["{{base_url}}"], "path": ["api", "delivery", "customer", "my-deliveries"], "query": [{"key": "status", "value": "active", "description": "Filter by status"}, {"key": "per_page", "value": "20", "description": "Items per page"}]}}}, {"name": "Track Delivery", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/customer/tracking/1", "host": ["{{base_url}}"], "path": ["api", "delivery", "customer", "tracking", "1"]}}}, {"name": "Create Review (Customer)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignment_id\": 1,\n  \"rating\": 5,\n  \"comment\": \"Excellent delivery service!\",\n  \"delivery_time_rating\": 5,\n  \"personnel_rating\": 5,\n  \"food_condition_rating\": 5\n}"}, "url": {"raw": "{{base_url}}/api/delivery/customer/reviews", "host": ["{{base_url}}"], "path": ["api", "delivery", "customer", "reviews"]}}}, {"name": "Create Tip (Customer)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignment_id\": 1,\n  \"amount\": 5.00,\n  \"payment_method\": \"credit_card\",\n  \"notes\": \"Thank you for the great service!\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/customer/tips", "host": ["{{base_url}}"], "path": ["api", "delivery", "customer", "tips"]}}}]}, {"name": "Personnel App", "item": [{"name": "My Assignments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel-app/my-assignments?status=active", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel-app", "my-assignments"], "query": [{"key": "status", "value": "active", "description": "Filter by status"}]}}}, {"name": "Update Assignment Status (Personnel)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"picked_up\",\n  \"notes\": \"Order picked up from restaurant\",\n  \"pickup_time\": \"2024-01-15 14:45:00\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/personnel-app/assignments/1/status", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel-app", "assignments", "1", "status"]}}}, {"name": "Update Location (Personnel)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"accuracy\": 5.0,\n  \"speed\": 25.5,\n  \"heading\": 180.0,\n  \"battery_level\": 85\n}"}, "url": {"raw": "{{base_url}}/api/delivery/personnel-app/assignments/1/location", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel-app", "assignments", "1", "location"]}}}, {"name": "My Performance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel-app/my-performance?period=month", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel-app", "my-performance"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year"}]}}}, {"name": "My Earnings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/personnel-app/my-earnings?period=month", "host": ["{{base_url}}"], "path": ["api", "delivery", "personnel-app", "my-earnings"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year"}]}}}]}, {"name": "Admin Dashboard", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/admin/dashboard", "host": ["{{base_url}}"], "path": ["api", "delivery", "admin", "dashboard"]}}}, {"name": "Daily Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/admin/reports/daily?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "delivery", "admin", "reports", "daily"], "query": [{"key": "date", "value": "2024-01-15", "description": "Report date (YYYY-MM-DD)"}]}}}, {"name": "Weekly Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/admin/reports/weekly?week=2024-W03", "host": ["{{base_url}}"], "path": ["api", "delivery", "admin", "reports", "weekly"], "query": [{"key": "week", "value": "2024-W03", "description": "Week in format YYYY-WXX"}]}}}, {"name": "Monthly Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/delivery/admin/reports/monthly?month=2024-01", "host": ["{{base_url}}"], "path": ["api", "delivery", "admin", "reports", "monthly"], "query": [{"key": "month", "value": "2024-01", "description": "Month in format YYYY-MM"}]}}}, {"name": "Suspend Personnel", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Policy violation\",\n  \"suspension_until\": \"2024-02-15\",\n  \"notes\": \"Suspended for repeated late deliveries\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/admin/personnel/1/suspend", "host": ["{{base_url}}"], "path": ["api", "delivery", "admin", "personnel", "1", "suspend"]}}}, {"name": "Activate Personnel", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notes\": \"Suspension period completed, reactivating personnel\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/admin/personnel/1/activate", "host": ["{{base_url}}"], "path": ["api", "delivery", "admin", "personnel", "1", "activate"]}}}]}, {"name": "Webhooks", "item": [{"name": "Payment Status Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event_type\": \"payment.completed\",\n  \"assignment_id\": 1,\n  \"payment_id\": \"pay_**********\",\n  \"amount\": 5.99,\n  \"status\": \"completed\",\n  \"timestamp\": \"2024-01-15T15:30:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/webhooks/payment-status", "host": ["{{base_url}}"], "path": ["api", "delivery", "webhooks", "payment-status"]}}}, {"name": "Location Update Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event_type\": \"location.updated\",\n  \"personnel_id\": 1,\n  \"assignment_id\": 1,\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"accuracy\": 5.0,\n  \"timestamp\": \"2024-01-15T15:30:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/delivery/webhooks/location-update", "host": ["{{base_url}}"], "path": ["api", "delivery", "webhooks", "location-update"]}}}]}]}