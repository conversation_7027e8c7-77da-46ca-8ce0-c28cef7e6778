@extends('layouts.master')

@section('title', 'Receipt Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-green-600 to-teal-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-receipt text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Receipt Settings</h1>
                        <p class="text-green-100">Configure receipt layout and printing options</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-green-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-green-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-green-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-green-200 mx-2"></i>
                                <span class="text-white font-medium">Receipt Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Receipt Configuration -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Receipt Configuration</h3>
                <p class="text-gray-600 mt-1">Configure receipt header, footer, and layout settings</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <!-- Receipt Header -->
                <div>
                    <label for="receipt_header" class="block text-sm font-medium text-gray-700 mb-2">
                        Receipt Header
                    </label>
                    <textarea id="receipt_header" name="receipt_header" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="Enter receipt header text">{{ isset($receiptSettings['receipt_header']) ? $receiptSettings['receipt_header']->value : 'Welcome to Our Restaurant' }}</textarea>
                </div>

                <!-- Receipt Footer -->
                <div>
                    <label for="receipt_footer" class="block text-sm font-medium text-gray-700 mb-2">
                        Receipt Footer
                    </label>
                    <textarea id="receipt_footer" name="receipt_footer" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="Enter receipt footer text">{{ isset($receiptSettings['receipt_footer']) ? $receiptSettings['receipt_footer']->value : 'Thank you for your visit!' }}</textarea>
                </div>

                <!-- Receipt Width -->
                <div>
                    <label for="receipt_width" class="block text-sm font-medium text-gray-700 mb-2">
                        Receipt Width (mm)
                    </label>
                    <select id="receipt_width" name="receipt_width" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="58">58mm (Small)</option>
                        <option value="80" selected>80mm (Standard)</option>
                        <option value="110">110mm (Large)</option>
                    </select>
                </div>

                <!-- Receipt Logo -->
                <div>
                    <label for="receipt_logo" class="block text-sm font-medium text-gray-700 mb-2">
                        Receipt Logo
                    </label>
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <img class="h-16 w-16 rounded-lg object-cover border border-gray-300" 
                                 src="https://via.placeholder.com/64x64?text=Logo" alt="Current logo">
                        </div>
                        <div class="flex-1">
                            <input type="file" id="receipt_logo" name="receipt_logo" accept="image/*" 
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-green-50 file:text-green-700 hover:file:bg-green-100">
                            <p class="text-sm text-gray-500 mt-1">PNG, JPG up to 1MB (recommended: 200x100px)</p>
                        </div>
                    </div>
                </div>

                <!-- Print Options -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Print Options</h4>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="auto_print" name="auto_print" value="1" checked
                                   class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                            <label for="auto_print" class="ml-2 text-sm text-gray-700">
                                Auto-print receipts after order completion
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="print_logo" name="print_logo" value="1" checked
                                   class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                            <label for="print_logo" class="ml-2 text-sm text-gray-700">
                                Include logo on receipts
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="print_qr_code" name="print_qr_code" value="1"
                                   class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                            <label for="print_qr_code" class="ml-2 text-sm text-gray-700">
                                Include QR code for digital receipt
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        Test Print
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Receipt Preview -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Receipt Preview</h3>
                <p class="text-gray-600 mt-1">Preview how your receipt will look</p>
            </div>

            <div class="p-6">
                <div class="bg-gray-50 rounded-lg p-4 max-w-xs mx-auto" style="width: 300px;">
                    <!-- Receipt Preview Content -->
                    <div class="text-center border-b border-gray-300 pb-4 mb-4">
                        <img src="https://via.placeholder.com/100x50?text=Logo" alt="Logo" class="mx-auto mb-2 rounded">
                        <div class="text-sm font-medium" id="preview-header">Welcome to Our Restaurant</div>
                        <div class="text-xs text-gray-600 mt-1">123 Main Street, City</div>
                        <div class="text-xs text-gray-600">Phone: (*************</div>
                    </div>

                    <div class="text-xs space-y-1 mb-4">
                        <div class="flex justify-between">
                            <span>Order #: 12345</span>
                            <span>Table: 5</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Date: {{ date('Y-m-d') }}</span>
                            <span>Time: {{ date('H:i') }}</span>
                        </div>
                        <div>Cashier: John Doe</div>
                    </div>

                    <div class="border-t border-gray-300 pt-2 mb-4">
                        <div class="text-xs space-y-1">
                            <div class="flex justify-between">
                                <span>2x Burger</span>
                                <span>$20.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span>1x Fries</span>
                                <span>$5.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span>2x Drink</span>
                                <span>$6.00</span>
                            </div>
                        </div>
                    </div>

                    <div class="border-t border-gray-300 pt-2 mb-4">
                        <div class="text-xs space-y-1">
                            <div class="flex justify-between">
                                <span>Subtotal:</span>
                                <span>$31.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Tax (8%):</span>
                                <span>$2.48</span>
                            </div>
                            <div class="flex justify-between font-bold">
                                <span>Total:</span>
                                <span>$33.48</span>
                            </div>
                        </div>
                    </div>

                    <div class="text-center border-t border-gray-300 pt-4">
                        <div class="text-xs" id="preview-footer">Thank you for your visit!</div>
                        <div class="text-xs text-gray-600 mt-1">Visit us again soon!</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Update preview when header/footer changes
    $('#receipt_header').on('input', function() {
        $('#preview-header').text($(this).val() || 'Welcome to Our Restaurant');
    });

    $('#receipt_footer').on('input', function() {
        $('#preview-footer').text($(this).val() || 'Thank you for your visit!');
    });

    // Form submission handler
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        // Add loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Receipt settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });

    // Test print handler
    $('button:contains("Test Print")').on('click', function() {
        Swal.fire({
            icon: 'info',
            title: 'Test Print',
            text: 'Sending test receipt to default printer...',
            timer: 2000,
            showConfirmButton: false
        });
    });
});
</script>
@endpush
