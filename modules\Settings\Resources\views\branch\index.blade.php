@extends('layouts.master')

@section('title', 'Branch Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-store text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Branch Settings</h1>
                        <p class="text-yellow-100">Configure settings for individual branches</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-yellow-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-yellow-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-yellow-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-yellow-200 mx-2"></i>
                                <span class="text-white font-medium">Branch Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Branch Selector -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Select Branch</h3>
                    <p class="text-gray-600 mt-1">Choose a branch to configure</p>
                </div>

                <div class="p-6">
                    @if($branches && $branches->count() > 0)
                        <div class="space-y-3">
                            @foreach($branches as $branch)
                                <div class="branch-item p-3 rounded-lg border {{ $branchId == $branch->id ? 'border-yellow-500 bg-yellow-50' : 'border-gray-200 hover:border-gray-300' }} cursor-pointer transition-colors duration-200" 
                                     data-branch-id="{{ $branch->id }}">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-store text-yellow-600"></i>
                                            </div>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <h4 class="text-sm font-medium text-gray-900">{{ $branch->name }}</h4>
                                            <p class="text-xs text-gray-500">{{ $branch->address ?? 'No address set' }}</p>
                                        </div>
                                        @if($branchId == $branch->id)
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-check text-yellow-600"></i>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-store text-gray-400 text-3xl mb-4"></i>
                            <p class="text-gray-500">No branches found</p>
                            <p class="text-sm text-gray-400 mt-1">Create a branch first to configure settings</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Settings Configuration -->
        <div class="lg:col-span-3">
            @if($branchId && isset($categories))
                @foreach($categories as $categoryKey => $category)
                <div class="bg-white rounded-lg shadow-lg mb-6">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">{{ $category['name'] }}</h3>
                        <p class="text-gray-600 mt-1">{{ $category['description'] ?? 'Configure ' . strtolower($category['name']) . ' settings' }}</p>
                    </div>

                    <form class="p-6 space-y-6" data-category="{{ $categoryKey }}">
                        @csrf
                        <input type="hidden" name="branch_id" value="{{ $branchId }}">
                        <input type="hidden" name="category" value="{{ $categoryKey }}">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($category['settings'] as $settingKey => $settingConfig)
                                <div>
                                    <label for="{{ $categoryKey }}_{{ $settingKey }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ $settingConfig['label'] }}
                                        @if($settingConfig['required'] ?? false)
                                            <span class="text-red-500">*</span>
                                        @endif
                                    </label>
                                    
                                    @php
                                        $currentValue = isset($settings[$categoryKey]) ? 
                                            $settings[$categoryKey]->where('key', $settingKey)->first()?->value : 
                                            ($settingConfig['default'] ?? '');
                                    @endphp

                                    @if($settingConfig['type'] === 'select')
                                        <select id="{{ $categoryKey }}_{{ $settingKey }}" name="{{ $settingKey }}" 
                                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                                                {{ ($settingConfig['required'] ?? false) ? 'required' : '' }}>
                                            @foreach($settingConfig['options'] as $optionValue => $optionLabel)
                                                <option value="{{ $optionValue }}" {{ $currentValue == $optionValue ? 'selected' : '' }}>
                                                    {{ $optionLabel }}
                                                </option>
                                            @endforeach
                                        </select>
                                    @elseif($settingConfig['type'] === 'checkbox')
                                        <div class="flex items-center">
                                            <input type="checkbox" id="{{ $categoryKey }}_{{ $settingKey }}" name="{{ $settingKey }}" value="1"
                                                   class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                                                   {{ $currentValue == '1' ? 'checked' : '' }}>
                                            <label for="{{ $categoryKey }}_{{ $settingKey }}" class="ml-2 text-sm text-gray-700">
                                                {{ $settingConfig['description'] ?? 'Enable ' . $settingConfig['label'] }}
                                            </label>
                                        </div>
                                    @elseif($settingConfig['type'] === 'number')
                                        <input type="number" id="{{ $categoryKey }}_{{ $settingKey }}" name="{{ $settingKey }}" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                                               placeholder="{{ $settingConfig['placeholder'] ?? '' }}" 
                                               value="{{ $currentValue }}"
                                               min="{{ $settingConfig['min'] ?? '' }}"
                                               max="{{ $settingConfig['max'] ?? '' }}"
                                               step="{{ $settingConfig['step'] ?? '1' }}"
                                               {{ ($settingConfig['required'] ?? false) ? 'required' : '' }}>
                                    @elseif($settingConfig['type'] === 'textarea')
                                        <textarea id="{{ $categoryKey }}_{{ $settingKey }}" name="{{ $settingKey }}" rows="3"
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                                                  placeholder="{{ $settingConfig['placeholder'] ?? '' }}"
                                                  {{ ($settingConfig['required'] ?? false) ? 'required' : '' }}>{{ $currentValue }}</textarea>
                                    @else
                                        <input type="text" id="{{ $categoryKey }}_{{ $settingKey }}" name="{{ $settingKey }}" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                                               placeholder="{{ $settingConfig['placeholder'] ?? '' }}" 
                                               value="{{ $currentValue }}"
                                               {{ ($settingConfig['required'] ?? false) ? 'required' : '' }}>
                                    @endif
                                    
                                    @if(isset($settingConfig['help']))
                                        <p class="text-sm text-gray-500 mt-1">{{ $settingConfig['help'] }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>

                        <!-- Save Button for this category -->
                        <div class="flex justify-end pt-4 border-t border-gray-200">
                            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-yellow-600 border border-transparent rounded-lg hover:bg-yellow-700 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                                <i class="fas fa-save mr-2"></i>
                                Save {{ $category['name'] }}
                            </button>
                        </div>
                    </form>
                </div>
                @endforeach
            @else
                <div class="bg-white rounded-lg shadow-lg">
                    <div class="p-6 text-center">
                        <i class="fas fa-cog text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Select a Branch</h3>
                        <p class="text-gray-600">Choose a branch from the sidebar to configure its settings</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Branch selection handler
    $('.branch-item').on('click', function() {
        const branchId = $(this).data('branch-id');
        window.location.href = `{{ route('settings.branch.index') }}/${branchId}`;
    });

    // Form submission handler
    $('form[data-category]').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const category = form.data('category');
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Add loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: `${category} settings saved successfully.`,
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });
});
</script>
@endpush
