<?php $__env->startSection('title', 'إدارة الأدوار'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <i class="fas fa-user-shield text-blue-600"></i>
                        إدارة الأدوار
                    </h1>
                    <button onclick="openCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة دور جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                        <input type="text" id="search-input" placeholder="البحث في الأدوار..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحارس</label>
                        <select id="guard-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع الأنواع</option>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="resetFilters()" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-undo mr-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">قائمة الأدوار</h2>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table id="roles-table" class="w-full text-sm text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th class="px-6 py-3">#</th>
                                <th class="px-6 py-3">اسم الدور</th>
                                <th class="px-6 py-3">نوع الحارس</th>
                                <th class="px-6 py-3">عدد الصلاحيات</th>
                                <th class="px-6 py-3">عدد المستخدمين</th>
                                <th class="px-6 py-3">تاريخ الإنشاء</th>
                                <th class="px-6 py-3">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Role Modal -->
<div id="role-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 id="modal-title" class="text-lg font-semibold text-gray-900">إضافة دور جديد</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="role-form">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="role-id" name="role_id">
                <input type="hidden" id="form-method" name="_method" value="POST">
                
                <div class="px-6 py-4 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الدور *</label>
                        <input type="text" id="role-name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div id="name-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحارس *</label>
                        <select id="role-guard" name="guard_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر نوع الحارس</option>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                        <div id="guard-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" id="submit-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Permissions Modal -->
<div id="permissions-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 id="permissions-modal-title" class="text-lg font-semibold text-gray-900">إدارة صلاحيات الدور</h3>
                    <button onclick="closePermissionsModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="permissions-form">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="permissions-role-id" name="role_id">
                
                <div class="px-6 py-4">
                    <div id="permissions-content">
                        <!-- Permissions will be loaded here -->
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closePermissionsModal()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        حفظ الصلاحيات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<script>
let rolesTable;
let isEditMode = false;

$(document).ready(function() {
    // Initialize DataTable
    rolesTable = $('#roles-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '<?php echo e(route("roles.data")); ?>',
            data: function(d) {
                d.guard_name = $('#guard-filter').val();
                d.search.value = $('#search-input').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'guard_badge', name: 'guard_name', orderable: false },
            { data: 'permissions_count', name: 'permissions_count', orderable: false },
            { data: 'users_count', name: 'users_count', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        pageLength: 25,
        dom: 'rtip'
    });

    // Search functionality
    $('#search-input').on('keyup', function() {
        rolesTable.draw();
    });

    // Filter functionality
    $('#guard-filter').on('change', function() {
        rolesTable.draw();
    });

    // Form submission
    $('#role-form').on('submit', function(e) {
        e.preventDefault();
        submitForm();
    });

    // Permissions form submission
    $('#permissions-form').on('submit', function(e) {
        e.preventDefault();
        submitPermissions();
    });
});

function openCreateModal() {
    isEditMode = false;
    $('#modal-title').text('إضافة دور جديد');
    $('#role-form')[0].reset();
    $('#role-id').val('');
    $('#form-method').val('POST');
    clearErrors();
    $('#role-modal').removeClass('hidden');
}

function editRole(id) {
    isEditMode = true;
    $('#modal-title').text('تعديل الدور');
    $('#form-method').val('PUT');
    clearErrors();
    
    // Fetch role data
    $.get(`/admin/roles/${id}`, function(response) {
        $('#role-id').val(response.id);
        $('#role-name').val(response.name);
        $('#role-guard').val(response.guard_name);
        $('#role-modal').removeClass('hidden');
    }).fail(function() {
        Swal.fire('خطأ!', 'حدث خطأ أثناء جلب بيانات الدور', 'error');
    });
}

function assignPermissions(id) {
    $('#permissions-role-id').val(id);
    
    // Fetch role and permissions data
    $.get(`/admin/roles/${id}/permissions`, function(response) {
        $('#permissions-modal-title').text(`إدارة صلاحيات الدور: ${response.role.name}`);
        $('#permissions-content').html(response.html);
        $('#permissions-modal').removeClass('hidden');
    }).fail(function() {
        Swal.fire('خطأ!', 'حدث خطأ أثناء جلب الصلاحيات', 'error');
    });
}

function deleteRole(id) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/roles/${id}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    Swal.fire('تم الحذف!', 'تم حذف الدور بنجاح', 'success');
                    rolesTable.ajax.reload();
                },
                error: function(xhr) {
                    let message = 'حدث خطأ أثناء حذف الدور';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Swal.fire('خطأ!', message, 'error');
                }
            });
        }
    });
}

function submitForm() {
    const formData = new FormData($('#role-form')[0]);
    const url = isEditMode ? `/admin/roles/${$('#role-id').val()}` : '/admin/roles';
    
    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            Swal.fire('نجح!', isEditMode ? 'تم تحديث الدور بنجاح' : 'تم إنشاء الدور بنجاح', 'success');
            closeModal();
            rolesTable.ajax.reload();
        },
        error: function(xhr) {
            if (xhr.status === 422) {
                displayErrors(xhr.responseJSON.errors);
            } else {
                Swal.fire('خطأ!', 'حدث خطأ أثناء حفظ البيانات', 'error');
            }
        }
    });
}

function submitPermissions() {
    const formData = new FormData($('#permissions-form')[0]);
    const roleId = $('#permissions-role-id').val();
    
    $.ajax({
        url: `/admin/roles/${roleId}/permissions`,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            Swal.fire('نجح!', 'تم تحديث صلاحيات الدور بنجاح', 'success');
            closePermissionsModal();
            rolesTable.ajax.reload();
        },
        error: function(xhr) {
            Swal.fire('خطأ!', 'حدث خطأ أثناء حفظ الصلاحيات', 'error');
        }
    });
}

function closeModal() {
    $('#role-modal').addClass('hidden');
    clearErrors();
}

function closePermissionsModal() {
    $('#permissions-modal').addClass('hidden');
}

function resetFilters() {
    $('#search-input').val('');
    $('#guard-filter').val('');
    rolesTable.draw();
}

function clearErrors() {
    $('.text-red-500').addClass('hidden');
    $('.border-red-500').removeClass('border-red-500');
}

function displayErrors(errors) {
    clearErrors();
    
    for (let field in errors) {
        const errorElement = $(`#${field}-error`);
        const inputElement = $(`#role-${field}`);
        
        if (errorElement.length) {
            errorElement.text(errors[field][0]).removeClass('hidden');
            inputElement.addClass('border-red-500');
        }
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/roles/index.blade.php ENDPATH**/ ?>