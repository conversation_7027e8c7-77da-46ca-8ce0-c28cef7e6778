<?php

use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\Web\AreaController;
use Modules\Reservation\Http\Controllers\Web\ReservationController;
use Modules\Reservation\Http\Controllers\Web\TableController;
use Modules\Reservation\Http\Controllers\Web\QRTestController;
use Modules\Reservation\Http\Controllers\Api\QRCodeController;
use Modules\Reservation\Http\Controllers\Api\WaiterRequestController;

/*
|--------------------------------------------------------------------------
| Web Routes for Reservation Module
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Reservation module.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Public QR Code Routes (no authentication required)
Route::prefix('restaurant')->name('restaurant.')->group(function () {
    Route::get('table/{qrCode}', [QRCodeController::class, 'showTable'])->name('table.show');
    Route::get('table/{qrCode}/menu', [QRCodeController::class, 'showMenu'])->name('table.menu');
    Route::post('table/{qrCode}/request', [WaiterRequestController::class, 'createFromQR'])->name('table.request');
});

// Authenticated Routes
Route::middleware(['auth'])->prefix('reservation')->name('reservation.')->group(function () {
    
    // Dashboard
    Route::get('/', [ReservationController::class, 'dashboard'])->name('dashboard');
    Route::get('/dashboard', [ReservationController::class, 'dashboard'])->name('dashboard.index');
    
    // Reservation Management Routes
    Route::prefix('reservations')->name('reservations.')->group(function () {
        // Main reservation page
        Route::get('/', [ReservationController::class, 'index'])->name('index');
        
        // DataTable endpoint
        Route::get('/datatable', [ReservationController::class, 'datatable'])->name('datatable');
        
        // CRUD operations
        Route::post('/', [ReservationController::class, 'store'])->name('store');
        Route::get('/{id}', [ReservationController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [ReservationController::class, 'edit'])->name('edit');
        Route::put('/{id}', [ReservationController::class, 'update'])->name('update');
        Route::delete('/{id}', [ReservationController::class, 'destroy'])->name('destroy');
        
        // Status management
        Route::post('/{id}/confirm', [ReservationController::class, 'confirm'])->name('confirm');
        Route::post('/{id}/seat', [ReservationController::class, 'seat'])->name('seat');
        Route::post('/{id}/complete', [ReservationController::class, 'complete'])->name('complete');
        Route::post('/{id}/no-show', [ReservationController::class, 'noShow'])->name('no-show');
        Route::post('/{id}/cancel', [ReservationController::class, 'cancel'])->name('cancel');
        
        // Utility routes
        Route::post('/check-availability', [ReservationController::class, 'checkAvailability'])->name('check-availability');
        Route::get('/export', [ReservationController::class, 'export'])->name('export');
        Route::get('/statistics', [ReservationController::class, 'statistics'])->name('statistics');
    });

    // Waiter Request Management Routes
    Route::prefix('waiter-requests')->name('waiter-requests.')->group(function () {
        // Main waiter requests page
        Route::get('/', [ReservationController::class, 'waiterRequestsIndex'])->name('index');
        
        // Cards endpoint for AJAX loading
        Route::get('/cards', [ReservationController::class, 'waiterRequestsCards'])->name('cards');
        
        // CRUD operations
        Route::post('/', [ReservationController::class, 'waiterRequestsStore'])->name('store');
        Route::get('/{id}', [ReservationController::class, 'waiterRequestsShow'])->name('show');
        Route::get('/{id}/edit', [ReservationController::class, 'waiterRequestsEdit'])->name('edit');
        Route::put('/{id}', [ReservationController::class, 'waiterRequestsUpdate'])->name('update');
        Route::delete('/{id}', [ReservationController::class, 'waiterRequestsDestroy'])->name('destroy');
        
        // Status management
        Route::post('/{id}/complete', [ReservationController::class, 'waiterRequestsComplete'])->name('complete');
        Route::post('/{id}/cancel', [ReservationController::class, 'waiterRequestsCancel'])->name('cancel');
        Route::post('/{id}/assign', [ReservationController::class, 'waiterRequestsAssign'])->name('assign');
        
        // Utility routes
        Route::get('/export', [ReservationController::class, 'waiterRequestsExport'])->name('export');
        Route::get('/statistics', [ReservationController::class, 'waiterRequestsStatistics'])->name('statistics');
    });

    // Area Management Routes
    Route::prefix('areas')->name('areas.')->group(function () {
        // Main areas page
        Route::get('/', [AreaController::class, 'index'])->name('index');
        
        // DataTable endpoint
        Route::get('/datatable', [AreaController::class, 'datatable'])->name('datatable');
        Route::get('/data', [AreaController::class, 'datatable'])->name('data');
        
        // CRUD operations
        Route::post('/', [AreaController::class, 'store'])->name('store');
        Route::get('/{id}', [AreaController::class, 'show'])->name('show');
        Route::put('/{id}', [AreaController::class, 'update'])->name('update');
        Route::delete('/{id}', [AreaController::class, 'destroy'])->name('destroy');
        
        // Utility routes
        Route::get('/dropdown', [AreaController::class, 'dropdown'])->name('dropdown');
        Route::get('/export', [AreaController::class, 'export'])->name('export');
    });

    // Table Management Routes
    Route::prefix('tables')->name('tables.')->group(function () {
        // Main tables page
        Route::get('/', [TableController::class, 'index'])->name('index');
        
        // DataTable endpoint
        Route::get('/datatable', [TableController::class, 'datatable'])->name('datatable');
        
        // CRUD operations
        Route::post('/', [TableController::class, 'store'])->name('store');
        Route::get('/{id}', [TableController::class, 'show'])->name('show');
        Route::put('/{id}', [TableController::class, 'update'])->name('update');
        Route::delete('/{id}', [TableController::class, 'destroy'])->name('destroy');
        
        // Status and utility routes
        Route::post('/{id}/status', [TableController::class, 'updateStatus'])->name('update-status');
        Route::get('/area/{areaId}', [TableController::class, 'getTablesByArea'])->name('by-area');
        Route::get('/dropdown', [TableController::class, 'dropdown'])->name('dropdown');
        Route::get('/export', [TableController::class, 'export'])->name('export');
    });

    // QR Code Management Routes
    Route::prefix('qr-codes')->name('qr-codes.')->group(function () {
        // Main QR codes page
        Route::get('/', [QRCodeController::class, 'index'])->name('index');
        
        // QR code generation and management
        Route::post('/generate/{tableId}', [QRCodeController::class, 'generate'])->name('generate');
        Route::post('/batch-generate', [QRCodeController::class, 'batchGenerate'])->name('batch-generate');
        Route::get('/download/{tableId}', [QRCodeController::class, 'download'])->name('download');
        Route::post('/print/{tableId}', [QRCodeController::class, 'print'])->name('print');
        
        // QR code testing and validation
        Route::get('/test', [QRCodeController::class, 'test'])->name('test');
        Route::post('/validate', [QRCodeController::class, 'validate'])->name('validate');
    });

    // Dropdown/AJAX utility routes
    Route::prefix('dropdowns')->name('dropdowns.')->group(function () {
        Route::get('/areas', [AreaController::class, 'dropdown'])->name('areas');
        Route::get('/tables', [TableController::class, 'dropdown'])->name('tables');
        Route::get('/tables/area/{areaId}', [TableController::class, 'getTablesByArea'])->name('tables.by-area');
        Route::get('/waiters', [ReservationController::class, 'waitersDropdown'])->name('waiters');
    });

    // Direct utility routes for backward compatibility
    Route::get('/waiters', [ReservationController::class, 'waitersDropdown'])->name('waiters');

    // Additional AJAX routes for frontend compatibility
    Route::get('/areas/list', [AreaController::class, 'dropdown'])->name('areas.list');
    Route::get('/areas/{id}/tables', [TableController::class, 'getTablesByArea'])->name('areas.tables');
    
    // Tables index route for AJAX requests (used by waiter-requests view)
    Route::get('/tables', [TableController::class, 'index'])->name('tables.index');

    // Statistics and Reports
    Route::get('/reports', function() {
        return view('Reservation::reports');
    })->name('reports');
    
    Route::prefix('reports')->name('reports.')->group(function () {
        // Main reports page
        Route::get('/', function() {
            return view('Reservation::reports');
        })->name('index');
        
        Route::get('/reservations', [ReservationController::class, 'reservationReports'])->name('reservations');
        Route::get('/waiter-requests', [WaiterRequestController::class, 'waiterRequestReports'])->name('waiter-requests');
        Route::get('/tables', [TableController::class, 'tableReports'])->name('tables');
        Route::get('/areas', [AreaController::class, 'areaReports'])->name('areas');
    });
    
    // API Documentation and Testing
    Route::get('/api-docs', function() {
        return view('Reservation::api-documentation');
    })->name('api.docs');
    
    Route::get('/api-tester', function() {
        return view('Reservation::api-tester');
    })->name('api.tester');
    
    // QR Code Testing
    Route::get('/qr-test', function() {
        return view('Reservation::qr-test');
    })->name('qr.test');
});

// Route aliases for backward compatibility
Route::middleware(['auth'])->group(function () {
    // Areas routes
    Route::get('/areas/data', [AreaController::class, 'datatable'])->name('areas.data');
    
    // Tables routes
    Route::post('/tables', [TableController::class, 'store'])->name('tables.store');
});