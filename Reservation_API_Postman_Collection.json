{"info": {"_postman_id": "reservation-api-collection", "name": "Restaurant POS - Reservation API", "description": "Complete API collection for testing Reservation module endpoints including Areas, Tables, Reservations, Waiter Requests, and QR Codes", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "tenant_id", "value": "1", "type": "string"}, {"key": "branch_id", "value": "1", "type": "string"}, {"key": "area_id", "value": "", "type": "string"}, {"key": "table_id", "value": "", "type": "string"}, {"key": "reservation_id", "value": "", "type": "string"}, {"key": "waiter_request_id", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('auth_token', response.token);", "        console.log('Auth token saved:', response.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}}}]}, {"name": "Areas Management", "item": [{"name": "Get All Areas", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/areas", "host": ["{{base_url}}"], "path": ["areas"]}}}, {"name": "Create Area", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('area_id', response.data.id);", "        console.log('Area ID saved:', response.data.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"name\": \"Main Dining Area\",\n    \"description\": \"Primary dining area with 20 tables\"\n}"}, "url": {"raw": "{{base_url}}/areas", "host": ["{{base_url}}"], "path": ["areas"]}}}, {"name": "Get Area by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/areas/{{area_id}}", "host": ["{{base_url}}"], "path": ["areas", "{{area_id}}"]}}}, {"name": "Update Area", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"name\": \"Updated Main Dining Area\",\n    \"description\": \"Updated primary dining area with 25 tables\"\n}"}, "url": {"raw": "{{base_url}}/areas/{{area_id}}", "host": ["{{base_url}}"], "path": ["areas", "{{area_id}}"]}}}, {"name": "Delete Area", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/areas/{{area_id}}", "host": ["{{base_url}}"], "path": ["areas", "{{area_id}}"]}}}]}, {"name": "Tables Management", "item": [{"name": "Get All Tables", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tables", "host": ["{{base_url}}"], "path": ["tables"]}}}, {"name": "Create Table", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('table_id', response.data.id);", "        console.log('Table ID saved:', response.data.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"area_id\": {{area_id}},\n    \"table_number\": \"T001\",\n    \"table_name\": \"Table 1\",\n    \"seating_capacity\": 4,\n    \"section\": \"Window Side\",\n    \"status\": \"available\",\n    \"notes\": \"Near the window with a nice view\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/tables", "host": ["{{base_url}}"], "path": ["tables"]}}}, {"name": "Get Table by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tables/{{table_id}}", "host": ["{{base_url}}"], "path": ["tables", "{{table_id}}"]}}}, {"name": "Update Table", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"area_id\": {{area_id}},\n    \"table_number\": \"T001\",\n    \"table_name\": \"Updated Table 1\",\n    \"seating_capacity\": 6,\n    \"section\": \"Window Side\",\n    \"status\": \"available\",\n    \"notes\": \"Updated table with increased capacity\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/tables/{{table_id}}", "host": ["{{base_url}}"], "path": ["tables", "{{table_id}}"]}}}, {"name": "Update Table Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"occupied\"\n}"}, "url": {"raw": "{{base_url}}/tables/{{table_id}}/status", "host": ["{{base_url}}"], "path": ["tables", "{{table_id}}", "status"]}}}, {"name": "Get Tables by Area", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tables/area/{{area_id}}", "host": ["{{base_url}}"], "path": ["tables", "area", "{{area_id}}"]}}}, {"name": "Get Available Tables", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tables/available/2024-12-20 19:00:00", "host": ["{{base_url}}"], "path": ["tables", "available", "2024-12-20 19:00:00"]}}}, {"name": "Delete Table", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tables/{{table_id}}", "host": ["{{base_url}}"], "path": ["tables", "{{table_id}}"]}}}]}, {"name": "Reservations Management", "item": [{"name": "Get All Reservations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations?per_page=15&status=confirmed", "host": ["{{base_url}}"], "path": ["api", "reservations"], "query": [{"key": "per_page", "value": "15"}, {"key": "status", "value": "confirmed"}]}}}, {"name": "Create Reservation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('reservation_id', response.data.id);", "        console.log('Reservation ID saved:', response.data.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"table_id\": {{table_id}},\n    \"customer_name\": \"<PERSON>\",\n    \"customer_phone\": \"+1234567890\",\n    \"email\": \"<EMAIL>\",\n    \"party_size\": 4,\n    \"reservation_datetime\": \"2024-12-25 19:00:00\",\n    \"duration_minutes\": 120,\n    \"special_requests\": \"Window table preferred\",\n    \"notes\": \"Anniversary dinner\",\n    \"area_id\": {{area_id}}\n}"}, "url": {"raw": "{{base_url}}/api/reservations", "host": ["{{base_url}}"], "path": ["api", "reservations"]}}}, {"name": "Get Reservation by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations/{{reservation_id}}", "host": ["{{base_url}}"], "path": ["api", "reservations", "{{reservation_id}}"]}}}, {"name": "Update Reservation", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"table_id\": {{table_id}},\n    \"customer_name\": \"<PERSON> Updated\",\n    \"customer_phone\": \"+1234567890\",\n    \"email\": \"<EMAIL>\",\n    \"party_size\": 6,\n    \"reservation_datetime\": \"2024-12-25 20:00:00\",\n    \"duration_minutes\": 150,\n    \"special_requests\": \"Window table required\",\n    \"notes\": \"Updated anniversary dinner reservation\",\n    \"area_id\": {{area_id}}\n}"}, "url": {"raw": "{{base_url}}/api/reservations/{{reservation_id}}", "host": ["{{base_url}}"], "path": ["api", "reservations", "{{reservation_id}}"]}}}, {"name": "Confirm Reservation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations/{{reservation_id}}/confirm", "host": ["{{base_url}}"], "path": ["api", "reservations", "{{reservation_id}}", "confirm"]}}}, {"name": "Seat Reservation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations/{{reservation_id}}/seat", "host": ["{{base_url}}"], "path": ["api", "reservations", "{{reservation_id}}", "seat"]}}}, {"name": "Complete Reservation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations/{{reservation_id}}/complete", "host": ["{{base_url}}"], "path": ["api", "reservations", "{{reservation_id}}", "complete"]}}}, {"name": "<PERSON> as No <PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations/{{reservation_id}}/no-show", "host": ["{{base_url}}"], "path": ["api", "reservations", "{{reservation_id}}", "no-show"]}}}, {"name": "Check Availability", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"reservation_datetime\": \"2024-12-25 19:00:00\",\n    \"party_size\": 4,\n    \"duration_minutes\": 120\n}"}, "url": {"raw": "{{base_url}}/api/reservations/check-availability", "host": ["{{base_url}}"], "path": ["api", "reservations", "check-availability"]}}}, {"name": "Get Reservation Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations/statistics/overview", "host": ["{{base_url}}"], "path": ["api", "reservations", "statistics", "overview"]}}}, {"name": "Cancel Reservation", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/reservations/{{reservation_id}}", "host": ["{{base_url}}"], "path": ["api", "reservations", "{{reservation_id}}"]}}}]}, {"name": "Waiter Requests", "item": [{"name": "Get All Waiter Requests", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/waiter-requests", "host": ["{{base_url}}"], "path": ["waiter-requests"]}}}, {"name": "Create Waiter Request", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('waiter_request_id', response.data.id);", "        console.log('Waiter Request ID saved:', response.data.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"table_id\": {{table_id}},\n    \"branch_id\": {{branch_id}},\n    \"request_type\": \"service\",\n    \"notes\": \"Customer needs assistance with menu\",\n    \"status\": \"pending\"\n}"}, "url": {"raw": "{{base_url}}/waiter-requests", "host": ["{{base_url}}"], "path": ["waiter-requests"]}}}, {"name": "Get Waiter Request by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/waiter-requests/{{waiter_request_id}}", "host": ["{{base_url}}"], "path": ["waiter-requests", "{{waiter_request_id}}"]}}}, {"name": "Update Waiter Request", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"table_id\": {{table_id}},\n    \"branch_id\": {{branch_id}},\n    \"request_type\": \"bill\",\n    \"notes\": \"Customer requesting the bill\",\n    \"status\": \"in_progress\"\n}"}, "url": {"raw": "{{base_url}}/waiter-requests/{{waiter_request_id}}", "host": ["{{base_url}}"], "path": ["waiter-requests", "{{waiter_request_id}}"]}}}, {"name": "Complete Waiter Request", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/waiter-requests/{{waiter_request_id}}/complete", "host": ["{{base_url}}"], "path": ["waiter-requests", "{{waiter_request_id}}", "complete"]}}}, {"name": "Cancel Waiter Request", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/waiter-requests/{{waiter_request_id}}/cancel", "host": ["{{base_url}}"], "path": ["waiter-requests", "{{waiter_request_id}}", "cancel"]}}}, {"name": "Get Requests by Table", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/waiter-requests/table/{{table_id}}", "host": ["{{base_url}}"], "path": ["waiter-requests", "table", "{{table_id}}"]}}}, {"name": "Get Requests by Waiter", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/waiter-requests/waiter/1", "host": ["{{base_url}}"], "path": ["waiter-requests", "waiter", "1"]}}}, {"name": "Delete Waiter Request", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/waiter-requests/{{waiter_request_id}}", "host": ["{{base_url}}"], "path": ["waiter-requests", "{{waiter_request_id}}"]}}}]}, {"name": "QR Code Management", "item": [{"name": "Validate QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"qr_code\": \"table_qr_code_here\",\n    \"table_id\": {{table_id}}\n}"}, "url": {"raw": "{{base_url}}/qr-codes/validate", "host": ["{{base_url}}"], "path": ["qr-codes", "validate"]}}}, {"name": "Generate Table QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"size\": 200,\n    \"format\": \"png\"\n}"}, "url": {"raw": "{{base_url}}/qr-codes/table/{{table_id}}/generate", "host": ["{{base_url}}"], "path": ["qr-codes", "table", "{{table_id}}", "generate"]}}}, {"name": "Generate Menu QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"size\": 200,\n    \"format\": \"png\"\n}"}, "url": {"raw": "{{base_url}}/qr-codes/table/{{table_id}}/menu", "host": ["{{base_url}}"], "path": ["qr-codes", "table", "{{table_id}}", "menu"]}}}, {"name": "Get QR Content for Table", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/qr-codes/table/{{table_id}}/content", "host": ["{{base_url}}"], "path": ["qr-codes", "table", "{{table_id}}", "content"]}}}, {"name": "Batch Generate QR Codes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"table_ids\": [{{table_id}}],\n    \"size\": 200,\n    \"format\": \"png\"\n}"}, "url": {"raw": "{{base_url}}/qr-codes/batch-generate", "host": ["{{base_url}}"], "path": ["qr-codes", "batch-generate"]}}}]}, {"name": "Public API (No Auth)", "item": [{"name": "Public QR Validation", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"qr_code\": \"table_qr_code_here\",\n    \"table_id\": {{table_id}}\n}"}, "url": {"raw": "{{base_url}}/public/qr/validate", "host": ["{{base_url}}"], "path": ["public", "qr", "validate"]}}}, {"name": "Public Waiter Request", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"table_id\": {{table_id}},\n    \"branch_id\": {{branch_id}},\n    \"request_type\": \"service\",\n    \"notes\": \"Customer needs assistance\",\n    \"status\": \"pending\"\n}"}, "url": {"raw": "{{base_url}}/public/waiter-requests", "host": ["{{base_url}}"], "path": ["public", "waiter-requests"]}}}, {"name": "Public Check Availability", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"branch_id\": {{branch_id}},\n    \"reservation_datetime\": \"2024-12-25 19:00:00\",\n    \"party_size\": 4,\n    \"duration_minutes\": 120\n}"}, "url": {"raw": "{{base_url}}/public/reservations/check-availability", "host": ["{{base_url}}"], "path": ["public", "reservations", "check-availability"]}}}]}]}