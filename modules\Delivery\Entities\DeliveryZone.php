<?php

namespace Modules\Delivery\Entities;

use App\Models\Branch;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryZone extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'description',
        'coordinates',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'delivery_fee',
        'minimum_order_amount',
        'estimated_delivery_time_minutes',
        'is_active',
        'priority',
    ];

    protected $casts = [
        'coordinates' => 'array',
        'delivery_fee' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant this zone belongs to.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the branch this zone belongs to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get delivery assignments in this zone.
     */
    public function deliveryAssignments(): HasMany
    {
        return $this->hasMany(DeliveryAssignment::class);
    }

    /**
     * Check if a point is within this delivery zone.
     */
    public function containsPoint(float $latitude, float $longitude): bool
    {
        if (!$this->coordinates || !is_array($this->coordinates)) {
            return false;
        }

        $polygon = $this->coordinates;
        $x = $longitude;
        $y = $latitude;
        $inside = false;

        $j = count($polygon) - 1;
        for ($i = 0; $i < count($polygon); $i++) {
            $xi = $polygon[$i]['lng'] ?? $polygon[$i][0];
            $yi = $polygon[$i]['lat'] ?? $polygon[$i][1];
            $xj = $polygon[$j]['lng'] ?? $polygon[$j][0];
            $yj = $polygon[$j]['lat'] ?? $polygon[$j][1];

            if ((($yi > $y) !== ($yj > $y)) && ($x < ($xj - $xi) * ($y - $yi) / ($yj - $yi) + $xi)) {
                $inside = !$inside;
            }
            $j = $i;
        }

        return $inside;
    }

    /**
     * Get the center point of the zone.
     */
    public function getCenterPoint(): array
    {
        if (!$this->coordinates || !is_array($this->coordinates)) {
            return ['lat' => 0, 'lng' => 0];
        }

        $latSum = 0;
        $lngSum = 0;
        $count = count($this->coordinates);

        foreach ($this->coordinates as $point) {
            $latSum += $point['lat'] ?? $point[1];
            $lngSum += $point['lng'] ?? $point[0];
        }

        return [
            'lat' => $latSum / $count,
            'lng' => $lngSum / $count,
        ];
    }

    /**
     * Calculate approximate area of the zone in square kilometers.
     */
    public function getApproximateArea(): float
    {
        if (!$this->coordinates || count($this->coordinates) < 3) {
            return 0;
        }

        $earthRadius = 6371; // km
        $area = 0;
        $coordinates = $this->coordinates;
        $n = count($coordinates);

        for ($i = 0; $i < $n; $i++) {
            $j = ($i + 1) % $n;
            $lat1 = deg2rad($coordinates[$i]['lat'] ?? $coordinates[$i][1]);
            $lng1 = deg2rad($coordinates[$i]['lng'] ?? $coordinates[$i][0]);
            $lat2 = deg2rad($coordinates[$j]['lat'] ?? $coordinates[$j][1]);
            $lng2 = deg2rad($coordinates[$j]['lng'] ?? $coordinates[$j][0]);
            
            $area += ($lng2 - $lng1) * (2 + sin($lat1) + sin($lat2));
        }

        $area = abs($area) * $earthRadius * $earthRadius / 2;
        return $area;
    }

    /**
     * Scope for active zones.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for zones ordered by priority.
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Find the best matching zone for a location.
     */
    public static function findZoneForLocation(int $branchId, float $latitude, float $longitude): ?self
    {
        return static::where('branch_id', $branchId)
            ->active()
            ->byPriority()
            ->get()
            ->first(function ($zone) use ($latitude, $longitude) {
                return $zone->containsPoint($latitude, $longitude);
            });
    }
}