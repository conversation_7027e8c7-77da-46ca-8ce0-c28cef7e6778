<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\KotOrderItem;
use App\Models\User;
use App\Models\MenuItem;
use App\Models\Order;
use App\Models\Branch;

try {
    // Get the kitchen
    $kitchen = Kitchen::where('code', 'MAIN-001')->first();
    $user = User::first();
    $menuItems = MenuItem::take(3)->get();
    
    if (!$kitchen) {
        echo "Kitchen not found!\n";
        exit;
    }
    
    if (!$user) {
        echo "User not found!\n";
        exit;
    }
    
    if ($menuItems->count() == 0) {
        echo "No menu items found!\n";
        exit;
    }
    
    // Create 3 sample orders first
    $branch = Branch::first();
    
    for ($i = 1; $i <= 3; $i++) {
        // Create order
        $order = Order::create([
            'branch_id' => $branch->id,
            'order_number' => 'ORD-' . str_pad($i, 4, '0', STR_PAD_LEFT),
            'order_type' => 'dine_in',
            'status' => 'confirmed',
            'pax' => rand(1, 4),
            'customer_info' => json_encode([
                'name' => 'Customer ' . $i,
                'phone' => '***********' . $i
            ]),
            'subtotal' => rand(50, 200),
            'tax_amount' => rand(5, 20),
            'total_amount' => rand(55, 220),
            'waiter_id' => $user->id,
        ]);
        
        // Create KOT order
        $kotOrder = KotOrder::create([
            'tenant_id' => 1,
            'kitchen_id' => $kitchen->id,
            'kot_number' => 'KOT-' . str_pad($i, 4, '0', STR_PAD_LEFT),
            'order_id' => $order->id,
            'status' => ['pending', 'preparing', 'ready'][array_rand(['pending', 'preparing', 'ready'])],
            'priority' => ['low', 'medium', 'high'][array_rand(['low', 'medium', 'high'])],
            'estimated_prep_time_minutes' => rand(15, 45),
            'assigned_to' => $user->id,
            'created_by' => $user->id,
            'notes' => 'Sample KOT order #' . $i,
            'started_at' => now()->subMinutes(rand(5, 30)),
        ]);
        
        // Add items to the KOT order
        foreach ($menuItems->take(rand(1, 3)) as $menuItem) {
            KotOrderItem::create([
                'kot_order_id' => $kotOrder->id,
                'menu_item_id' => $menuItem->id,
                'quantity' => rand(1, 3),
                'status' => ['pending', 'preparing', 'ready', 'completed'][array_rand(['pending', 'preparing', 'ready', 'completed'])],
                'special_instructions' => 'No special instructions',
                'estimated_prep_time_minutes' => rand(10, 25),
            ]);
        }
        
        echo "Created KOT order: " . $kotOrder->kot_number . "\n";
    }
    
    echo "KOT orders created successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}