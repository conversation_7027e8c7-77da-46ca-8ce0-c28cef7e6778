<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports & Analytics - Reservation Module</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4f46e5',
                        secondary: '#6366f1'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <i class="fas fa-chart-bar text-blue-500 text-2xl ml-3"></i>
                        <h1 class="text-2xl font-bold text-gray-900">التقارير والإحصائيات</h1>
                    </div>
                    <a href="{{ route('reservation.dashboard') }}" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            
            <!-- Filters -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-filter text-blue-500 ml-2"></i>
                    فلاتر التقارير
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                        <input type="date" id="startDate" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                        <input type="date" id="endDate" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المنطقة</label>
                        <select id="areaFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">جميع المناطق</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="loadReports()" class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            <i class="fas fa-search ml-2"></i>
                            تحديث التقارير
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <i class="fas fa-calendar-check text-blue-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجمالي الحجوزات</p>
                            <p id="totalReservations" class="text-2xl font-bold text-gray-900">-</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">الحجوزات المؤكدة</p>
                            <p id="confirmedReservations" class="text-2xl font-bold text-gray-900">-</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100">
                            <i class="fas fa-clock text-yellow-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">في الانتظار</p>
                            <p id="pendingReservations" class="text-2xl font-bold text-gray-900">-</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100">
                            <i class="fas fa-times-circle text-red-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">الحجوزات الملغية</p>
                            <p id="cancelledReservations" class="text-2xl font-bold text-gray-900">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row 1 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Reservations Trend -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-line-chart text-blue-500 ml-2"></i>
                        اتجاه الحجوزات
                    </h3>
                    <canvas id="reservationsTrendChart" height="300"></canvas>
                </div>
                
                <!-- Status Distribution -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-pie-chart text-green-500 ml-2"></i>
                        توزيع حالات الحجز
                    </h3>
                    <canvas id="statusDistributionChart" height="300"></canvas>
                </div>
            </div>

            <!-- Charts Row 2 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Peak Hours -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-clock text-purple-500 ml-2"></i>
                        ساعات الذروة
                    </h3>
                    <canvas id="peakHoursChart" height="300"></canvas>
                </div>
                
                <!-- Area Performance -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-map-marker-alt text-orange-500 ml-2"></i>
                        أداء المناطق
                    </h3>
                    <canvas id="areaPerformanceChart" height="300"></canvas>
                </div>
            </div>

            <!-- Tables Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Top Tables -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-trophy text-yellow-500 ml-2"></i>
                        أكثر الطاولات حجزاً
                    </h3>
                    <div id="topTablesTable" class="overflow-x-auto">
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري تحميل البيانات...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Customer Analytics -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-users text-indigo-500 ml-2"></i>
                        إحصائيات العملاء
                    </h3>
                    <div id="customerAnalytics" class="space-y-4">
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري تحميل البيانات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Waiter Requests Analytics -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">
                    <i class="fas fa-bell text-red-500 ml-2"></i>
                    إحصائيات طلبات النادل
                </h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Waiter Requests Trend -->
                    <div>
                        <h4 class="text-md font-medium text-gray-700 mb-4">اتجاه طلبات النادل</h4>
                        <canvas id="waiterRequestsTrendChart" height="250"></canvas>
                    </div>
                    
                    <!-- Request Types -->
                    <div>
                        <h4 class="text-md font-medium text-gray-700 mb-4">أنواع الطلبات</h4>
                        <canvas id="requestTypesChart" height="250"></canvas>
                    </div>
                </div>
                
                <!-- Waiter Performance Table -->
                <div class="mt-8">
                    <h4 class="text-md font-medium text-gray-700 mb-4">أداء النادلين</h4>
                    <div id="waiterPerformanceTable" class="overflow-x-auto">
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري تحميل البيانات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export Section -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-download text-green-500 ml-2"></i>
                    تصدير التقارير
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="exportReport('pdf')" class="bg-red-500 text-white py-3 rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-file-pdf ml-2"></i>
                        تصدير PDF
                    </button>
                    <button onclick="exportReport('excel')" class="bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-file-excel ml-2"></i>
                        تصدير Excel
                    </button>
                    <button onclick="exportReport('csv')" class="bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-file-csv ml-2"></i>
                        تصدير CSV
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set up axios defaults
        axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        let charts = {};

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set default dates (last 30 days)
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            
            // Load initial data
            loadAreas();
            loadReports();
        });

        function loadAreas() {
            axios.get('/api/reservation/areas')
                .then(response => {
                    const select = document.getElementById('areaFilter');
                    select.innerHTML = '<option value="">جميع المناطق</option>';
                    
                    response.data.data.forEach(area => {
                        const option = document.createElement('option');
                        option.value = area.id;
                        option.textContent = area.name;
                        select.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading areas:', error);
                });
        }

        function loadReports() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const areaId = document.getElementById('areaFilter').value;
            
            const params = {
                start_date: startDate,
                end_date: endDate
            };
            
            if (areaId) {
                params.area_id = areaId;
            }
            
            // Load statistics
            loadStatistics(params);
            
            // Load charts
            loadReservationsTrend(params);
            loadStatusDistribution(params);
            loadPeakHours(params);
            loadAreaPerformance(params);
            loadWaiterRequestsAnalytics(params);
            
            // Load tables
            loadTopTables(params);
            loadCustomerAnalytics(params);
            loadWaiterPerformance(params);
        }

        function loadStatistics(params) {
            axios.get('/api/reservation/statistics', { params })
                .then(response => {
                    const data = response.data.data;
                    document.getElementById('totalReservations').textContent = data.total_reservations || 0;
                    document.getElementById('confirmedReservations').textContent = data.confirmed_reservations || 0;
                    document.getElementById('pendingReservations').textContent = data.pending_reservations || 0;
                    document.getElementById('cancelledReservations').textContent = data.cancelled_reservations || 0;
                })
                .catch(error => {
                    console.error('Error loading statistics:', error);
                    // Set default values on error
                    document.getElementById('totalReservations').textContent = '0';
                    document.getElementById('confirmedReservations').textContent = '0';
                    document.getElementById('pendingReservations').textContent = '0';
                    document.getElementById('cancelledReservations').textContent = '0';
                });
        }

        function loadReservationsTrend(params) {
            // Destroy existing chart
            if (charts.reservationsTrend) {
                charts.reservationsTrend.destroy();
            }
            
            const ctx = document.getElementById('reservationsTrendChart').getContext('2d');
            
            // Sample data - replace with actual API call
            const sampleData = {
                labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                datasets: [{
                    label: 'الحجوزات',
                    data: [65, 78, 90, 81],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            };
            
            charts.reservationsTrend = new Chart(ctx, {
                type: 'line',
                data: sampleData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function loadStatusDistribution(params) {
            // Destroy existing chart
            if (charts.statusDistribution) {
                charts.statusDistribution.destroy();
            }
            
            const ctx = document.getElementById('statusDistributionChart').getContext('2d');
            
            // Sample data - replace with actual API call
            const sampleData = {
                labels: ['مؤكد', 'في الانتظار', 'ملغي', 'مكتمل'],
                datasets: [{
                    data: [45, 25, 15, 15],
                    backgroundColor: [
                        'rgb(34, 197, 94)',
                        'rgb(251, 191, 36)',
                        'rgb(239, 68, 68)',
                        'rgb(59, 130, 246)'
                    ]
                }]
            };
            
            charts.statusDistribution = new Chart(ctx, {
                type: 'doughnut',
                data: sampleData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function loadPeakHours(params) {
            // Destroy existing chart
            if (charts.peakHours) {
                charts.peakHours.destroy();
            }
            
            const ctx = document.getElementById('peakHoursChart').getContext('2d');
            
            // Sample data - replace with actual API call
            const sampleData = {
                labels: ['12 ص', '6 ص', '12 م', '6 م'],
                datasets: [{
                    label: 'عدد الحجوزات',
                    data: [5, 15, 45, 35],
                    backgroundColor: 'rgba(147, 51, 234, 0.8)'
                }]
            };
            
            charts.peakHours = new Chart(ctx, {
                type: 'bar',
                data: sampleData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function loadAreaPerformance(params) {
            // Destroy existing chart
            if (charts.areaPerformance) {
                charts.areaPerformance.destroy();
            }
            
            const ctx = document.getElementById('areaPerformanceChart').getContext('2d');
            
            // Sample data - replace with actual API call
            const sampleData = {
                labels: ['المنطقة الرئيسية', 'التراس', 'VIP', 'الحديقة'],
                datasets: [{
                    label: 'عدد الحجوزات',
                    data: [120, 85, 45, 30],
                    backgroundColor: 'rgba(249, 115, 22, 0.8)'
                }]
            };
            
            charts.areaPerformance = new Chart(ctx, {
                type: 'bar',
                data: sampleData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function loadWaiterRequestsAnalytics(params) {
            // Waiter Requests Trend
            if (charts.waiterRequestsTrend) {
                charts.waiterRequestsTrend.destroy();
            }
            
            const trendCtx = document.getElementById('waiterRequestsTrendChart').getContext('2d');
            const trendData = {
                labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                datasets: [{
                    label: 'طلبات النادل',
                    data: [25, 32, 28, 35],
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4
                }]
            };
            
            charts.waiterRequestsTrend = new Chart(trendCtx, {
                type: 'line',
                data: trendData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Request Types
            if (charts.requestTypes) {
                charts.requestTypes.destroy();
            }
            
            const typesCtx = document.getElementById('requestTypesChart').getContext('2d');
            const typesData = {
                labels: ['طلب الفاتورة', 'طلب الطعام', 'مساعدة', 'أخرى'],
                datasets: [{
                    data: [40, 30, 20, 10],
                    backgroundColor: [
                        'rgb(34, 197, 94)',
                        'rgb(59, 130, 246)',
                        'rgb(251, 191, 36)',
                        'rgb(156, 163, 175)'
                    ]
                }]
            };
            
            charts.requestTypes = new Chart(typesCtx, {
                type: 'pie',
                data: typesData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function loadTopTables(params) {
            // Sample data - replace with actual API call
            const sampleData = [
                { table_number: 'طاولة 1', reservations: 45, area: 'المنطقة الرئيسية' },
                { table_number: 'طاولة 5', reservations: 38, area: 'التراس' },
                { table_number: 'طاولة 3', reservations: 32, area: 'المنطقة الرئيسية' },
                { table_number: 'طاولة 8', reservations: 28, area: 'VIP' },
                { table_number: 'طاولة 2', reservations: 25, area: 'الحديقة' }
            ];
            
            const tableHtml = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطاولة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنطقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الحجوزات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${sampleData.map(item => `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.table_number}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.area}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.reservations}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            document.getElementById('topTablesTable').innerHTML = tableHtml;
        }

        function loadCustomerAnalytics(params) {
            // Sample data - replace with actual API call
            const analyticsHtml = `
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-4 bg-blue-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">متوسط حجم المجموعة</span>
                        <span class="text-lg font-bold text-blue-600">4.2 أشخاص</span>
                    </div>
                    <div class="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">العملاء المتكررون</span>
                        <span class="text-lg font-bold text-green-600">68%</span>
                    </div>
                    <div class="flex justify-between items-center p-4 bg-yellow-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">متوسط مدة الحجز</span>
                        <span class="text-lg font-bold text-yellow-600">1.5 ساعة</span>
                    </div>
                    <div class="flex justify-between items-center p-4 bg-purple-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">معدل الحضور</span>
                        <span class="text-lg font-bold text-purple-600">92%</span>
                    </div>
                </div>
            `;
            
            document.getElementById('customerAnalytics').innerHTML = analyticsHtml;
        }

        function loadWaiterPerformance(params) {
            // Sample data - replace with actual API call
            const sampleData = [
                { waiter: 'أحمد محمد', requests: 45, avg_response: '3.2 دقيقة', rating: 4.8 },
                { waiter: 'سارة أحمد', requests: 38, avg_response: '2.8 دقيقة', rating: 4.9 },
                { waiter: 'محمد علي', requests: 32, avg_response: '4.1 دقيقة', rating: 4.6 },
                { waiter: 'فاطمة حسن', requests: 28, avg_response: '3.5 دقيقة', rating: 4.7 }
            ];
            
            const tableHtml = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النادل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الطلبات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط الاستجابة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التقييم</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${sampleData.map(item => `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.waiter}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.requests}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.avg_response}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        ${item.rating} ⭐
                                    </span>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            document.getElementById('waiterPerformanceTable').innerHTML = tableHtml;
        }

        function exportReport(format) {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const areaId = document.getElementById('areaFilter').value;
            
            const params = new URLSearchParams({
                format: format,
                start_date: startDate,
                end_date: endDate
            });
            
            if (areaId) {
                params.append('area_id', areaId);
            }
            
            // Create download link
            const url = `/api/reservation/reports/export?${params.toString()}`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `reservation-report-${startDate}-to-${endDate}.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>