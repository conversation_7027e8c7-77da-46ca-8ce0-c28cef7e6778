<?php

namespace Modules\Delivery\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryTracking extends Model
{
    use HasFactory;

    protected $table = 'delivery_tracking';

    protected $fillable = [
        'delivery_assignment_id',
        'latitude',
        'longitude',
        'accuracy',
        'speed',
        'bearing',
        'recorded_at',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'accuracy' => 'decimal:2',
        'speed' => 'decimal:2',
        'recorded_at' => 'datetime',
    ];

    /**
     * Get the delivery assignment this tracking belongs to.
     */
    public function deliveryAssignment(): BelongsTo
    {
        return $this->belongsTo(DeliveryAssignment::class);
    }

    /**
     * Calculate distance from a point in kilometers.
     */
    public function distanceFrom(float $latitude, float $longitude): float
    {
        $earthRadius = 6371; // km
        $latDiff = deg2rad($latitude - $this->latitude);
        $lonDiff = deg2rad($longitude - $this->longitude);
        
        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($this->latitude)) * cos(deg2rad($latitude)) *
             sin($lonDiff / 2) * sin($lonDiff / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }

    /**
     * Calculate distance from delivery destination.
     */
    public function distanceFromDestination(): float
    {
        $order = $this->deliveryAssignment->order;
        
        if (!$order->delivery_coordinates) {
            return 0;
        }

        $coords = $order->delivery_coordinates;
        return $this->distanceFrom($coords['lat'], $coords['lng']);
    }

    /**
     * Get estimated time to destination based on current speed.
     */
    public function getEstimatedTimeToDestination(): ?int
    {
        if (!$this->speed || $this->speed <= 0) {
            return null;
        }

        $distance = $this->distanceFromDestination();
        return (int) ceil(($distance / $this->speed) * 60); // minutes
    }

    /**
     * Check if location is accurate enough for tracking.
     */
    public function isAccurate(): bool
    {
        return $this->accuracy <= 50; // 50 meters or better
    }

    /**
     * Get formatted coordinates.
     */
    public function getFormattedCoordinates(): string
    {
        return number_format($this->latitude, 6) . ', ' . number_format($this->longitude, 6);
    }

    /**
     * Get bearing direction as text.
     */
    public function getBearingDirection(): string
    {
        if ($this->bearing === null) {
            return 'Unknown';
        }

        $directions = [
            'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
            'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'
        ];
        
        $index = (int) round($this->bearing / 22.5) % 16;
        return $directions[$index];
    }

    /**
     * Get speed in different units.
     */
    public function getSpeedInUnit(string $unit = 'kmh'): float
    {
        return match($unit) {
            'mph' => $this->speed * 0.621371,
            'ms' => $this->speed / 3.6,
            'kmh', 'kph' => $this->speed,
            default => $this->speed
        };
    }

    /**
     * Check if delivery person is moving.
     */
    public function isMoving(): bool
    {
        return $this->speed > 1; // Moving if speed > 1 km/h
    }

    /**
     * Check if delivery person is stationary.
     */
    public function isStationary(): bool
    {
        return $this->speed <= 1;
    }

    /**
     * Scope for recent tracking records.
     */
    public function scopeRecent($query, int $minutes = 30)
    {
        return $query->where('recorded_at', '>=', now()->subMinutes($minutes));
    }

    /**
     * Scope for accurate tracking records.
     */
    public function scopeAccurate($query, float $maxAccuracy = 50)
    {
        return $query->where('accuracy', '<=', $maxAccuracy);
    }

    /**
     * Scope for moving tracking records.
     */
    public function scopeMoving($query, float $minSpeed = 1)
    {
        return $query->where('speed', '>', $minSpeed);
    }

    /**
     * Scope for tracking within area.
     */
    public function scopeWithinArea($query, float $centerLat, float $centerLng, float $radiusKm)
    {
        return $query->whereRaw(
            '(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?',
            [$centerLat, $centerLng, $centerLat, $radiusKm]
        );
    }

    /**
     * Get tracking route for an assignment.
     */
    public static function getRouteForAssignment(int $assignmentId): array
    {
        return static::where('delivery_assignment_id', $assignmentId)
            ->orderBy('recorded_at')
            ->get()
            ->map(function ($tracking) {
                return [
                    'lat' => $tracking->latitude,
                    'lng' => $tracking->longitude,
                    'timestamp' => $tracking->recorded_at->timestamp,
                    'speed' => $tracking->speed,
                    'bearing' => $tracking->bearing,
                ];
            })
            ->toArray();
    }

    /**
     * Calculate total distance traveled for an assignment.
     */
    public static function getTotalDistanceForAssignment(int $assignmentId): float
    {
        $trackingRecords = static::where('delivery_assignment_id', $assignmentId)
            ->orderBy('recorded_at')
            ->get();

        if ($trackingRecords->count() < 2) {
            return 0;
        }

        $totalDistance = 0;
        for ($i = 1; $i < $trackingRecords->count(); $i++) {
            $prev = $trackingRecords[$i - 1];
            $current = $trackingRecords[$i];
            $totalDistance += $prev->distanceFrom($current->latitude, $current->longitude);
        }

        return $totalDistance;
    }
}