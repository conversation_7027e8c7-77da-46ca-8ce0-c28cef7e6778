<?php $__env->startSection('title', 'إدارة صلاحيات الدور'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <i class="fas fa-key text-blue-600"></i>
                        إدارة صلاحيات الدور: <?php echo e($role->name); ?>

                    </h1>
                    <div class="flex space-x-2">
                        <a href="<?php echo e(route('roles.show', $role)); ?>" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-2"></i>
                            العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-shield text-blue-600"></i>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm text-gray-600">اسم الدور</p>
                        <p class="font-semibold text-gray-900"><?php echo e($role->name); ?></p>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shield-alt text-purple-600"></i>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm text-gray-600">نوع الحارس</p>
                        <p class="font-semibold text-gray-900"><?php echo e($role->guard_name); ?></p>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-key text-green-600"></i>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm text-gray-600">الصلاحيات الحالية</p>
                        <p class="font-semibold text-gray-900"><?php echo e(count($rolePermissions)); ?> صلاحية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions Form -->
        <form id="permissions-form" action="<?php echo e(route('roles.permissions.assign', $role)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">إجراءات سريعة</h3>
                    <div class="flex space-x-2">
                        <button type="button" onclick="selectAll()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-check-double mr-2"></i>
                            تحديد الكل
                        </button>
                        <button type="button" onclick="deselectAll()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>
                            إلغاء تحديد الكل
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>
                            حفظ الصلاحيات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Permissions Groups -->
            <div class="space-y-6">
                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupPermissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <input type="checkbox" id="group-<?php echo e($group); ?>" class="group-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" onchange="toggleGroup('<?php echo e($group); ?>')">
                                    <label for="group-<?php echo e($group); ?>" class="mr-3 text-lg font-semibold text-gray-900 capitalize cursor-pointer">
                                        <?php echo e($group); ?>

                                    </label>
                                </div>
                                <span class="text-sm text-gray-500"><?php echo e($groupPermissions->count()); ?> صلاحية</span>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php $__currentLoopData = $groupPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center">
                                        <input type="checkbox" 
                                               id="permission-<?php echo e($permission->id); ?>" 
                                               name="permissions[]" 
                                               value="<?php echo e($permission->name); ?>"
                                               class="permission-checkbox group-<?php echo e($group); ?> w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                                               <?php if(in_array($permission->name, $rolePermissions)): ?> checked <?php endif; ?>
                                               onchange="updateGroupCheckbox('<?php echo e($group); ?>')">
                                        <label for="permission-<?php echo e($permission->id); ?>" class="mr-3 text-sm text-gray-700 cursor-pointer flex-1">
                                            <div class="flex items-center justify-between">
                                                <span><?php echo e($permission->name); ?></span>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                    <?php if($permission->guard_name === 'web'): ?> bg-indigo-100 text-indigo-800
                                                    <?php elseif($permission->guard_name === 'api'): ?> bg-purple-100 text-purple-800
                                                    <?php elseif($permission->guard_name === 'sanctum'): ?> bg-yellow-100 text-yellow-800
                                                    <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                                    <?php echo e($permission->guard_name); ?>

                                                </span>
                                            </div>
                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex justify-end">
                <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    حفظ الصلاحيات
                </button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Initialize group checkboxes based on current state
    <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupPermissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        updateGroupCheckbox('<?php echo e($group); ?>');
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    // Form submission
    $('#permissions-form').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                Swal.fire({
                    title: 'نجح!',
                    text: 'تم تحديث صلاحيات الدور بنجاح',
                    icon: 'success',
                    confirmButtonText: 'موافق'
                }).then(() => {
                    window.location.href = '<?php echo e(route("roles.show", $role)); ?>';
                });
            },
            error: function(xhr) {
                let message = 'حدث خطأ أثناء حفظ الصلاحيات';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Swal.fire('خطأ!', message, 'error');
            }
        });
    });
});

function selectAll() {
    $('.permission-checkbox').prop('checked', true);
    $('.group-checkbox').prop('checked', true);
}

function deselectAll() {
    $('.permission-checkbox').prop('checked', false);
    $('.group-checkbox').prop('checked', false);
}

function toggleGroup(group) {
    const groupCheckbox = $(`#group-${group}`);
    const isChecked = groupCheckbox.is(':checked');
    
    $(`.group-${group}`).prop('checked', isChecked);
}

function updateGroupCheckbox(group) {
    const groupPermissions = $(`.group-${group}`);
    const checkedPermissions = $(`.group-${group}:checked`);
    const groupCheckbox = $(`#group-${group}`);
    
    if (checkedPermissions.length === 0) {
        groupCheckbox.prop('checked', false);
        groupCheckbox.prop('indeterminate', false);
    } else if (checkedPermissions.length === groupPermissions.length) {
        groupCheckbox.prop('checked', true);
        groupCheckbox.prop('indeterminate', false);
    } else {
        groupCheckbox.prop('checked', false);
        groupCheckbox.prop('indeterminate', true);
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/roles/permissions.blade.php ENDPATH**/ ?>