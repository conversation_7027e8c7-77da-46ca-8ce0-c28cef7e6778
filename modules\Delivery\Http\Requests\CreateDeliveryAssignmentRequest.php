<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateDeliveryAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // return Auth::check();
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'order_id' => 'required|exists:orders,id',
            'delivery_personnel_id' => 'nullable|exists:delivery_personnel,id',
            'delivery_zone_id' => 'nullable|exists:delivery_zones,id',
            'pickup_time' => 'nullable|date|after:now',
            'delivery_time' => 'nullable|date|after:pickup_time',
            'delivery_address' => 'required|string|max:500',
            'delivery_latitude' => 'required|numeric|between:-90,90',
            'delivery_longitude' => 'required|numeric|between:-180,180',
            'delivery_fee' => 'nullable|numeric|min:0',
            'estimated_delivery_time' => 'nullable|integer|min:1',
            'special_instructions' => 'nullable|string|max:1000',
            'priority' => 'nullable|in:low,normal,high,urgent',
            'auto_assign' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'order_id.required' => 'Order ID is required',
            'order_id.exists' => 'Selected order does not exist',
            'delivery_personnel_id.exists' => 'Selected delivery personnel does not exist',
            'delivery_zone_id.exists' => 'Selected delivery zone does not exist',
            'pickup_time.after' => 'Pickup time must be in the future',
            'delivery_time.after' => 'Delivery time must be after pickup time',
            'delivery_address.required' => 'Delivery address is required',
            'delivery_address.max' => 'Delivery address cannot exceed 500 characters',
            'delivery_latitude.required' => 'Delivery latitude is required',
            'delivery_latitude.between' => 'Delivery latitude must be between -90 and 90',
            'delivery_longitude.required' => 'Delivery longitude is required',
            'delivery_longitude.between' => 'Delivery longitude must be between -180 and 180',
            'delivery_fee.min' => 'Delivery fee cannot be negative',
            'estimated_delivery_time.min' => 'Estimated delivery time must be at least 1 minute',
            'special_instructions.max' => 'Special instructions cannot exceed 1000 characters',
            'priority.in' => 'Priority must be one of: low, normal, high, urgent',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default priority if not provided
        if (!$this->has('priority')) {
            $this->merge(['priority' => 'normal']);
        }

        // Set auto_assign to true if not provided
        if (!$this->has('auto_assign')) {
            $this->merge(['auto_assign' => true]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if order already has a delivery assignment
            if ($this->order_id) {
                $existingAssignment = \Modules\Delivery\Entities\DeliveryAssignment::where('order_id', $this->order_id)
                    ->first();
                
                if ($existingAssignment) {
                    $validator->errors()->add('order_id', 'This order already has a delivery assignment. Cannot create duplicate assignments.');
                    return;
                }
                
                // Check order status for new assignments
                $order = \App\Models\Order::find($this->order_id);
                if (!$order) {
                    $validator->errors()->add('order_id', 'Order not found.');
                    return;
                }
                
                if (!in_array($order->status, ['confirmed', 'preparing', 'ready'])) {
                    $validator->errors()->add('order_id', 'Order must be confirmed, preparing, or ready for delivery assignment.');
                    return;
                }
                
                // Check personnel availability
                if ($this->delivery_personnel_id) {
                    $personnel = \Modules\Delivery\Entities\DeliveryPersonnel::find($this->delivery_personnel_id);
                    if (!$personnel || !$personnel->is_available) {
                        $validator->errors()->add('delivery_personnel_id', 'Selected delivery personnel is not available.');
                    }
                }
            }
            
            // Validate pickup and delivery times
            if ($this->pickup_time && $this->delivery_time) {
                $pickupTime = \Carbon\Carbon::parse($this->pickup_time);
                $deliveryTime = \Carbon\Carbon::parse($this->delivery_time);
                
                if ($deliveryTime->lte($pickupTime)) {
                    $validator->errors()->add('delivery_time', 'Delivery time must be after pickup time.');
                }
            }
        });
    }
}