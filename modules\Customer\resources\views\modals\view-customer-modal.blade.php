<!-- View Customer Modal -->
<div id="viewCustomerModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-7xl mx-4 max-h-[95vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white">
            <div class="flex items-center justify-between">
                <h3 id="viewCustomerModalLabel" class="text-xl font-semibold flex items-center">
                    <i class="fas fa-user-circle mr-3"></i>Customer Details
                </h3>
                <button type="button" onclick="closeViewCustomerModal()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="flex flex-col lg:flex-row max-h-[calc(95vh-140px)] overflow-hidden">
            <!-- Main Content -->
            <div class="flex-1 p-6 overflow-y-auto">
                <!-- Personal Information Card -->
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 mb-6 overflow-hidden">
                    <div class="bg-blue-600 text-white px-4 py-3">
                        <h4 class="font-semibold flex items-center">
                            <i class="fas fa-user mr-2"></i>Personal Information
                        </h4>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Full Name</label>
                                <p class="text-lg font-semibold text-gray-900" id="view_full_name">-</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Email</label>
                                <p class="text-gray-900" id="view_email">-</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Phone</label>
                                <p class="text-gray-900" id="view_phone">-</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Date of Birth</label>
                                <p class="text-gray-900" id="view_date_of_birth">-</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Gender</label>
                                <p class="text-gray-900" id="view_gender">-</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Status</label>
                                <p class="text-gray-900" id="view_status">-</p>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-500 mb-1">Address</label>
                                <p class="text-gray-900" id="view_address">-</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders Card -->
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                    <div class="bg-green-600 text-white px-4 py-3">
                        <h4 class="font-semibold flex items-center">
                            <i class="fas fa-shopping-cart mr-2"></i>Recent Orders
                        </h4>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full" id="customerOrdersTable">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Order #</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Total</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <!-- Orders will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="w-full lg:w-80 bg-gray-50 border-l border-gray-200 p-6 overflow-y-auto">
                <!-- Loyalty Information Card -->
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 mb-6 overflow-hidden">
                    <div class="bg-yellow-500 text-white px-4 py-3">
                        <h4 class="font-semibold flex items-center">
                            <i class="fas fa-star mr-2"></i>Loyalty Information
                        </h4>
                    </div>
                    <div class="p-6 text-center">
                        <div class="mb-4">
                            <h3 class="text-3xl font-bold text-yellow-600 mb-1" id="view_loyalty_points">0</h3>
                            <p class="text-sm text-gray-500">Loyalty Points</p>
                        </div>
                        <div class="mb-4">
                            <span class="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium" id="view_customer_group">Regular</span>
                        </div>
                        <button type="button" onclick="openLoyaltyModal()" 
                            class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            <i class="fas fa-coins mr-2"></i>Manage Points
                        </button>
                    </div>
                </div>

                <!-- Statistics Card -->
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                    <div class="bg-cyan-600 text-white px-4 py-3">
                        <h4 class="font-semibold flex items-center">
                            <i class="fas fa-chart-bar mr-2"></i>Statistics
                        </h4>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div class="bg-blue-50 rounded-lg p-4">
                                <h5 class="text-2xl font-bold text-blue-600 mb-1" id="view_total_orders">0</h5>
                                <p class="text-sm text-gray-600">Total Orders</p>
                            </div>
                            <div class="bg-green-50 rounded-lg p-4">
                                <h5 class="text-2xl font-bold text-green-600 mb-1" id="view_total_spent">$0.00</h5>
                                <p class="text-sm text-gray-600">Total Spent</p>
                            </div>
                            <div class="bg-cyan-50 rounded-lg p-4">
                                <h5 class="text-2xl font-bold text-cyan-600 mb-1" id="view_avg_order">$0.00</h5>
                                <p class="text-sm text-gray-600">Avg Order</p>
                            </div>
                            <div class="bg-yellow-50 rounded-lg p-4">
                                <h5 class="text-lg font-bold text-yellow-600 mb-1" id="view_last_order">-</h5>
                                <p class="text-sm text-gray-600">Last Order</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t">
            <button type="button" onclick="closeViewCustomerModal()" 
                class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                <i class="fas fa-times mr-2"></i>Close
            </button>
            <button type="button" onclick="editCustomer()"
                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                <i class="fas fa-edit mr-2"></i>Edit Customer
            </button>
        </div>
    </div>
</div>

<script>
function closeViewCustomerModal() {
    document.getElementById('viewCustomerModal').classList.add('hidden');
}

function editCustomer() {
    closeViewCustomerModal();
    // Get customer data and populate edit form
    const customerId = document.getElementById('viewCustomerModal').dataset.customerId;
    if (customerId) {
        // Trigger edit modal with customer data
        editCustomerRecord(customerId);
    }
}

function openLoyaltyModal() {
    const customerId = document.getElementById('viewCustomerModal').dataset.customerId;
    if (customerId) {
        document.getElementById('loyaltyModal').dataset.customerId = customerId;
        document.getElementById('loyaltyModal').classList.remove('hidden');
        loadLoyaltyHistory(customerId);
    }
}

let currentViewingCustomerId = null;

// Update the view customer click handler to store the customer ID
document.addEventListener('click', function(e) {
    if (e.target.closest('.view-customer')) {
        const button = e.target.closest('.view-customer');
        currentViewingCustomerId = button.dataset.id;
        const customerName = button.dataset.name;
        
        fetch(`/customers/${currentViewingCustomerId}`)
            .then(response => response.json())
            .then(customer => {
                // Update modal content
                document.getElementById('view_full_name').textContent = customer.first_name + ' ' + customer.last_name;
                document.getElementById('view_email').textContent = customer.email || 'N/A';
                document.getElementById('view_phone').textContent = customer.phone || 'N/A';
                document.getElementById('view_date_of_birth').textContent = customer.date_of_birth || 'N/A';
                document.getElementById('view_gender').textContent = customer.gender || 'N/A';
                document.getElementById('view_address').textContent = customer.address || 'N/A';
                document.getElementById('view_status').innerHTML = customer.is_active ? 
                    '<span class="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>' : 
                    '<span class="inline-block px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm">Inactive</span>';
                document.getElementById('view_loyalty_points').textContent = customer.loyalty_points || '0';
                document.getElementById('view_customer_group').textContent = customer.customer_group || 'Regular';
                
                // Load customer statistics
                loadCustomerStatistics(currentViewingCustomerId);
                
                document.getElementById('viewCustomerModal').dataset.customerId = currentViewingCustomerId;
                document.getElementById('viewCustomerModal').classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error loading customer details:', error);
                alert('Failed to load customer details');
            });
    }
});

function loadCustomerStatistics(customerId) {
    // Mock data - replace with actual API call
    document.getElementById('view_total_orders').textContent = '12';
    document.getElementById('view_total_spent').textContent = '$1,245.50';
    document.getElementById('view_avg_order').textContent = '$103.79';
    document.getElementById('view_last_order').textContent = '2024-01-15';
    
    // Load recent orders
    const recentOrdersHtml = `
        <tr class="border-b border-gray-100">
            <td class="py-3 px-4">#ORD-001</td>
            <td class="py-3 px-4">2024-01-15</td>
            <td class="py-3 px-4">$45.50</td>
            <td class="py-3 px-4"><span class="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Completed</span></td>
            <td class="py-3 px-4"><button class="text-blue-600 hover:text-blue-800">View</button></td>
        </tr>
        <tr class="border-b border-gray-100">
            <td class="py-3 px-4">#ORD-002</td>
            <td class="py-3 px-4">2024-01-10</td>
            <td class="py-3 px-4">$32.75</td>
            <td class="py-3 px-4"><span class="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Completed</span></td>
            <td class="py-3 px-4"><button class="text-blue-600 hover:text-blue-800">View</button></td>
        </tr>
        <tr class="border-b border-gray-100">
            <td class="py-3 px-4">#ORD-003</td>
            <td class="py-3 px-4">2024-01-05</td>
            <td class="py-3 px-4">$67.25</td>
            <td class="py-3 px-4"><span class="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Completed</span></td>
            <td class="py-3 px-4"><button class="text-blue-600 hover:text-blue-800">View</button></td>
        </tr>
    `;
    
    document.querySelector('#customerOrdersTable tbody').innerHTML = recentOrdersHtml;
}
</script>