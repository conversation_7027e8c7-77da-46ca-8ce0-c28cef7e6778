<?php $__env->startSection('title', 'Kitchen Details'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com"></script>
<!-- DataTables CSS with Tailwind -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
    /* Custom DataTables styling for Tailwind */
    .dataTables_wrapper {
        @apply text-sm;
    }
    
    .dataTables_length select,
    .dataTables_filter input {
        @apply border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }
    
    .dataTables_paginate .paginate_button {
        @apply px-3 py-2 mx-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors;
    }
    
    .dataTables_paginate .paginate_button.current {
        @apply bg-blue-500 text-white border-blue-500 hover:bg-blue-600;
    }
    
    .dataTables_paginate .paginate_button.disabled {
        @apply opacity-50 cursor-not-allowed;
    }
    
    table.dataTable thead th {
        @apply bg-gray-50 border-b border-gray-200 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }
    
    table.dataTable tbody td {
        @apply px-4 py-3 border-b border-gray-200 text-sm text-gray-900;
    }
    
    table.dataTable tbody tr:hover {
        @apply bg-gray-50;
    }
    
    /* Select2 Tailwind styling */
    .select2-container--default .select2-selection--single {
        @apply border border-gray-300 rounded-md h-10;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        @apply leading-8 px-3;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        @apply h-8;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="flex justify-between items-center bg-white px-6 py-4 border-b border-gray-200">
    <div class="flex items-center space-x-2">
        <h1 class="text-2xl font-semibold text-gray-900">Kitchen</h1>
        <span class="text-gray-500">/</span>
        <span class="text-lg text-gray-600"><?php echo e($kitchen->name); ?></span>
    </div>
    <div class="flex space-x-3">
        <a href="<?php echo e(route('kitchens.edit', $kitchen)); ?>" 
           class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            Edit Kitchen
        </a>
        <a href="<?php echo e(route('kitchens.index')); ?>" 
           class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Kitchens
        </a>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Kitchen Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Kitchen Information</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Name:</span>
                                <span class="text-gray-900"><?php echo e($kitchen->name); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Code:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <?php echo e($kitchen->code); ?>

                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Station Type:</span>
                                <span class="text-gray-900"><?php echo e(ucfirst($kitchen->station_type)); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Branch:</span>
                                <span class="text-gray-900"><?php echo e($kitchen->branch?->name ?? 'N/A'); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Manager:</span>
                                <span class="text-gray-900"><?php echo e($kitchen->manager?->name ?? 'Unassigned'); ?></span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Status:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($kitchen->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($kitchen->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Operating:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($kitchen->isOperating() ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                    <?php echo e($kitchen->isOperating() ? 'Operating' : 'Closed'); ?>

                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Max Orders:</span>
                                <span class="text-gray-900"><?php echo e($kitchen->max_concurrent_orders ?? '∞'); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Current Workload:</span>
                                <span class="text-gray-900"><?php echo e($kitchen->getCurrentWorkload()); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Created:</span>
                                <span class="text-gray-900"><?php echo e($kitchen->created_at->format('M d, Y H:i')); ?></span>
                            </div>
                        </div>
                    </div>
                    <?php if($kitchen->description): ?>
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h6 class="text-sm font-medium text-gray-700 mb-2">Description:</h6>
                        <p class="text-gray-900"><?php echo e($kitchen->description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Quick Stats</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-600"><?php echo e($kitchen->kitchenMenuItems->count()); ?></div>
                            <div class="text-sm text-gray-500 mt-1">Menu Items</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-yellow-600"><?php echo e($kitchen->kotOrders->count()); ?></div>
                            <div class="text-sm text-gray-500 mt-1">Active KOTs</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Items -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Assigned Menu Items</h3>
            <button type="button" 
                    class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200"
                    onclick="openModal()">
                <i class="fas fa-plus mr-2"></i>
                Assign Menu Item
            </button>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="menu-items-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Menu Item</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $kitchen->kitchenMenuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kitchenMenuItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($loop->iteration); ?></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($kitchenMenuItem->menuItem->name); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo e($kitchenMenuItem->menuItem->category->name ?? 'N/A'); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                $<?php echo e(number_format($kitchenMenuItem->menuItem->base_price, 2)); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($kitchenMenuItem->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($kitchenMenuItem->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo e($kitchenMenuItem->created_at->format('M d, Y')); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button type="button" 
                                        class="inline-flex items-center px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors duration-200 remove-menu-item" 
                                        data-menu-item-id="<?php echo e($kitchenMenuItem->menu_item_id); ?>"
                                        title="Remove Menu Item">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center py-8">
                                    <i class="fas fa-utensils text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-lg font-medium">No menu items assigned</p>
                                    <p class="text-sm">Click "Assign Menu Item" to add menu items to this kitchen.</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent KOT Orders -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Recent KOT Orders</h3>
            <div class="flex space-x-2">
                <button type="button" 
                        class="inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200"
                        onclick="refreshKotOrders()">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
                <a href="<?php echo e(route('kot-orders.index')); ?>" 
                   class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    View All KOTs
                </a>
            </div>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="kot-orders-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KOT #</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order #</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $kitchen->kotOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kotOrder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($kotOrder->kot_number); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo e($kotOrder->order->order_number); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                    $statusColors = [
                                        'pending' => 'bg-yellow-100 text-yellow-800',
                                        'preparing' => 'bg-blue-100 text-blue-800',
                                        'ready' => 'bg-green-100 text-green-800',
                                        'completed' => 'bg-gray-100 text-gray-800',
                                        'cancelled' => 'bg-red-100 text-red-800'
                                    ];
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($statusColors[$kotOrder->status] ?? 'bg-gray-100 text-gray-800'); ?>">
                                    <?php echo e(ucfirst($kotOrder->status)); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                    $priorityColors = [
                                        'low' => 'bg-gray-100 text-gray-800',
                                        'normal' => 'bg-blue-100 text-blue-800',
                                        'high' => 'bg-yellow-100 text-yellow-800',
                                        'urgent' => 'bg-red-100 text-red-800'
                                    ];
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($priorityColors[$kotOrder->priority] ?? 'bg-gray-100 text-gray-800'); ?>">
                                    <?php echo e(ucfirst($kotOrder->priority)); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo e($kotOrder->kotOrderItems->count()); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo e($kotOrder->created_at->format('M d, H:i')); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="<?php echo e(route('kot-orders.show', $kotOrder)); ?>" 
                                       class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors duration-200"
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if($kotOrder->status !== 'completed' && $kotOrder->status !== 'cancelled'): ?>
                                    <button type="button" 
                                            class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded transition-colors duration-200 update-kot-status" 
                                            data-kot-id="<?php echo e($kotOrder->id); ?>"
                                            data-current-status="<?php echo e($kotOrder->status); ?>"
                                            title="Update Status">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center py-8">
                                    <i class="fas fa-clipboard-list text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-lg font-medium">No recent KOT orders</p>
                                    <p class="text-sm">KOT orders will appear here when they are created.</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Assign Menu Item Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="assignMenuItemModal" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <form id="assignMenuItemForm">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-plus text-blue-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                Assign Menu Item
                            </h3>
                            <div class="mt-4">
                                <label for="menu_item_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Menu Item <span class="text-red-500">*</span>
                                </label>
                                <select class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                        id="menu_item_id" 
                                        name="menu_item_id" 
                                        required>
                                    <option value="">Select Menu Item</option>
                                </select>
                                <p class="mt-2 text-sm text-gray-500">Choose a menu item to assign to this kitchen.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Assign
                    </button>
                    <button type="button" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-colors duration-200"
                            onclick="closeModal()">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Menu Items DataTable with enhanced styling and error handling
    let menuItemsTable;
    const menuItemsTableBody = $('#menu-items-table tbody tr');
    const hasMenuItems = menuItemsTableBody.length > 0 && !menuItemsTableBody.first().find('td[colspan]').length;
    
    if (hasMenuItems) {
        try {
            menuItemsTable = $('#menu-items-table').DataTable({
                responsive: true,
                pageLength: 10,
                order: [[0, 'asc']],
                dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"mb-2 sm:mb-0"l><"mb-2 sm:mb-0"f>>rtip',
                columnDefs: [
                    { targets: '_all', defaultContent: '' },
                    { targets: [0, 1, 2, 3, 4, 5, 6], className: 'text-center' }
                ],
                language: {
                    search: "",
                    searchPlaceholder: "Search menu items...",
                    lengthMenu: "Show _MENU_ items",
                    info: "Showing _START_ to _END_ of _TOTAL_ items",
                    infoEmpty: "No items to show",
                    infoFiltered: "(filtered from _MAX_ total items)",
                    paginate: {
                        first: "First",
                        last: "Last", 
                        next: "Next",
                        previous: "Previous"
                    },
                    emptyTable: "No menu items assigned to this kitchen"
                },
                drawCallback: function() {
                    bindMenuItemEvents();
                    styleDataTableElements();
                },
                initComplete: function() {
                    console.log('Menu Items DataTable initialized successfully');
                }
            });
        } catch (error) {
            console.error('Error initializing Menu Items DataTable:', error);
        }
    } else {
        console.log('Menu Items table is empty, skipping DataTable initialization');
        bindMenuItemEvents();
    }

    // Initialize KOT Orders DataTable with enhanced styling and error handling
    let kotOrdersTable;
    const kotOrdersTableBody = $('#kot-orders-table tbody tr');
    const hasKotOrders = kotOrdersTableBody.length > 0 && !kotOrdersTableBody.first().find('td[colspan]').length;
    
    if (hasKotOrders) {
        try {
            kotOrdersTable = $('#kot-orders-table').DataTable({
                responsive: true,
                pageLength: 10,
                order: [[5, 'desc']], // Order by created date descending
                dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"mb-2 sm:mb-0"l><"mb-2 sm:mb-0"f>>rtip',
                columnDefs: [
                    { targets: '_all', defaultContent: '' },
                    { targets: [0, 1, 2, 3, 4, 5, 6], className: 'text-center' }
                ],
                language: {
                    search: "",
                    searchPlaceholder: "Search KOT orders...",
                    lengthMenu: "Show _MENU_ orders",
                    info: "Showing _START_ to _END_ of _TOTAL_ orders",
                    infoEmpty: "No orders to show",
                    infoFiltered: "(filtered from _MAX_ total orders)",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next", 
                        previous: "Previous"
                    },
                    emptyTable: "No KOT orders found for this kitchen"
                },
                drawCallback: function() {
                    bindKotOrderEvents();
                    styleDataTableElements();
                },
                initComplete: function() {
                    console.log('KOT Orders DataTable initialized successfully');
                }
            });
        } catch (error) {
            console.error('Error initializing KOT Orders DataTable:', error);
        }
    } else {
        console.log('KOT Orders table is empty, skipping DataTable initialization');
        bindKotOrderEvents();
    }

    // Apply Tailwind styling to DataTable elements
    function styleDataTableElements() {
        // Style search inputs
        $('.dataTables_filter input').addClass('block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm').attr('placeholder', 'Search...');
        
        // Style length select
        $('.dataTables_length select').addClass('block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm');
        
        // Style pagination
        $('.dataTables_paginate .paginate_button').addClass('relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200');
        $('.dataTables_paginate .paginate_button.current').addClass('z-10 bg-blue-50 border-blue-500 text-blue-600');
        $('.dataTables_paginate .paginate_button.disabled').addClass('opacity-50 cursor-not-allowed');
        
        // Style info text
        $('.dataTables_info').addClass('text-sm text-gray-700');
        $('.dataTables_length label').addClass('text-sm text-gray-700');
        $('.dataTables_filter label').addClass('text-sm text-gray-700');
    }

    // Initial styling
    setTimeout(styleDataTableElements, 100);

    // Modal functions
    window.openModal = function() {
        $('#assignMenuItemModal').removeClass('hidden');
        loadAvailableMenuItems();
    };

    window.closeModal = function() {
        $('#assignMenuItemModal').addClass('hidden');
        $('#assignMenuItemForm')[0].reset();
    };

    // Load available menu items
    function loadAvailableMenuItems() {
        const select = $('#menu_item_id');
        select.empty().append('<option value="">Loading...</option>');
        
        $.ajax({
            url: '<?php echo e(route("kitchens.available-menu-items", $kitchen)); ?>',
            method: 'GET',
            success: function(response) {
                select.empty().append('<option value="">Select Menu Item</option>');
                
                if (response && response.length > 0) {
                    response.forEach(function(item) {
                        select.append(`<option value="${item.id}">${item.name} - ${item.category_name || item.category}</option>`);
                    });
                } else {
                    select.append('<option value="" disabled>No available menu items</option>');
                }
            },
            error: function(xhr) {
                select.empty().append('<option value="" disabled>Error loading items</option>');
                console.error('Failed to load menu items:', xhr);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to load available menu items',
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            }
        });
    }

    // Handle assign menu item form submission
    $('#assignMenuItemForm').on('submit', function(e) {
        e.preventDefault();

        const menuItemId = $('#menu_item_id').val();
        if (!menuItemId) {
            Swal.fire({
                title: 'Warning',
                text: 'Please select a menu item',
                icon: 'warning',
                confirmButtonColor: '#f59e0b'
            });
            return;
        }

        const submitButton = $(this).find('button[type="submit"]');
        const originalHtml = submitButton.html();
        submitButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Assigning...');

        $.ajax({
            url: '<?php echo e(route("kitchens.assign-menu-item", $kitchen)); ?>',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal();
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Menu item assigned successfully',
                        icon: 'success',
                        confirmButtonColor: '#10b981'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to assign menu item',
                        icon: 'error',
                        confirmButtonColor: '#ef4444'
                    });
                }
            },
            error: function(xhr) {
                let errorMessage = "Failed to assign menu item";
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                }
                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#ef4444'
                });
            },
            complete: function() {
                submitButton.prop('disabled', false).html(originalHtml);
            }
        });
    });

    // Bind menu item events
    function bindMenuItemEvents() {
        $('.remove-menu-item').off('click').on('click', function() {
            const menuItemId = $(this).data('menu-item-id');
            const menuItemName = $(this).data('menu-item-name');
            const kitchenId = <?php echo e($kitchen->id); ?>;
            
            Swal.fire({
                title: 'Are you sure?',
                text: `Remove "${menuItemName}" from this kitchen?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, remove it!',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#6b7280'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: `/kitchens/${kitchenId}/menu-items/${menuItemId}`,
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'Success!',
                                    text: 'Menu item removed successfully',
                                    icon: 'success',
                                    confirmButtonColor: '#10b981'
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message || 'Failed to remove menu item',
                                    icon: 'error',
                                    confirmButtonColor: '#ef4444'
                                });
                            }
                        },
                        error: function(xhr) {
                            let errorMessage = "Failed to remove menu item";
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                                errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                            }
                            Swal.fire({
                                title: 'Error!',
                                text: errorMessage,
                                icon: 'error',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    });
                }
            });
        });
    }

    // Bind KOT order events
    function bindKotOrderEvents() {
        $('.update-kot-status').off('click').on('click', function() {
            const kotId = $(this).data('kot-id');
            const currentStatus = $(this).data('current-status');
            
            // Define status options based on current status
            const statusOptions = {
                'pending': ['preparing', 'cancelled'],
                'preparing': ['ready', 'cancelled'],
                'ready': ['completed']
            };
            
            const availableStatuses = statusOptions[currentStatus] || [];
            
            if (availableStatuses.length === 0) {
                Swal.fire({
                    title: 'Info',
                    text: 'No status updates available for this KOT order.',
                    icon: 'info',
                    confirmButtonColor: '#3b82f6'
                });
                return;
            }
            
            // Create select options
            let selectOptions = '';
            availableStatuses.forEach(status => {
                const statusLabel = status.charAt(0).toUpperCase() + status.slice(1);
                selectOptions += `<option value="${status}">${statusLabel}</option>`;
            });
            
            Swal.fire({
                title: 'Update KOT Status',
                html: `
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select new status:</label>
                        <select id="new-status" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            ${selectOptions}
                        </select>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Update Status',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#3b82f6',
                cancelButtonColor: '#6b7280',
                preConfirm: () => {
                    const newStatus = document.getElementById('new-status').value;
                    if (!newStatus) {
                        Swal.showValidationMessage('Please select a status');
                        return false;
                    }
                    return newStatus;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    updateKotStatus(kotId, result.value);
                }
            });
        });
    }

    // Update KOT status function
    function updateKotStatus(kotId, newStatus) {
        $.ajax({
            url: `/api/kot-orders/${kotId}/status`,
            method: 'PUT',
            data: { status: newStatus },
            success: function(response) {
                Swal.fire({
                    title: 'Success',
                    text: 'KOT status updated successfully',
                    icon: 'success',
                    confirmButtonColor: '#10b981'
                }).then(() => {
                    location.reload();
                });
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to update KOT status';
                Swal.fire({
                    title: 'Error',
                    text: message,
                    icon: 'error',
                    confirmButtonColor: '#ef4444'
                });
            }
        });
    }

    // Refresh KOT orders function
    window.refreshKotOrders = function() {
        kotOrdersTable.ajax.reload(null, false);
        Swal.fire({
            title: 'Refreshed',
            text: 'KOT orders have been refreshed',
            icon: 'success',
            timer: 1500,
            showConfirmButton: false
        });
    };

    // Close modal when clicking outside
    $(document).on('click', function(e) {
        if ($(e.target).is('#assignMenuItemModal')) {
            closeModal();
        }
    });

    // Close modal with Escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });

    // Initial event binding
    bindMenuItemEvents();
    bindKotOrderEvents();
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Kitchen\Providers/../resources/views/kitchens/show.blade.php ENDPATH**/ ?>