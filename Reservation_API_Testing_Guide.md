# Reservation API Testing Guide

## Overview
This guide provides comprehensive instructions for testing the Restaurant POS Reservation module API endpoints using the provided Postman collection.

## Prerequisites
1. Laravel server running on `http://localhost:8000`
2. Postman application installed
3. Valid authentication credentials
4. Database with proper migrations and seeders

## Postman Collection Import
1. Open Postman
2. Click "Import" button
3. Select the `Reservation_API_Postman_Collection.json` file
4. The collection will be imported with all endpoints organized in folders

## Environment Variables
The collection uses the following variables that you can customize:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://localhost:8000` | API base URL |
| `auth_token` | (empty) | Authentication token (auto-populated after login) |
| `tenant_id` | `1` | Tenant ID for multi-tenancy |
| `branch_id` | `1` | Branch ID for restaurant branch |
| `area_id` | (empty) | Area ID (auto-populated after creating area) |
| `table_id` | (empty) | Table ID (auto-populated after creating table) |
| `reservation_id` | (empty) | Reservation ID (auto-populated after creating reservation) |
| `waiter_request_id` | (empty) | Waiter Request ID (auto-populated after creating request) |

## Testing Workflow

### 1. Authentication
Start by running the **Login** request in the "Authentication" folder:
- This will authenticate you and automatically save the auth token
- All subsequent requests will use this token

### 2. Areas Management
Test area management endpoints in this order:
1. **Create Area** - Creates a new dining area
2. **Get All Areas** - Lists all areas
3. **Get Area by ID** - Retrieves specific area details
4. **Update Area** - Modifies area information
5. **Delete Area** - Removes an area (run last)

### 3. Tables Management
Test table management endpoints:
1. **Create Table** - Creates a new table (requires area_id from step 2)
2. **Get All Tables** - Lists all tables
3. **Get Table by ID** - Retrieves specific table details
4. **Update Table** - Modifies table information
5. **Update Table Status** - Changes table status (available/occupied/reserved)
6. **Get Tables by Area** - Lists tables in specific area
7. **Get Available Tables** - Shows available tables for specific datetime
8. **Delete Table** - Removes a table (run last)

### 4. Reservations Management
Test reservation endpoints:
1. **Check Availability** - Verify table availability before booking
2. **Create Reservation** - Creates a new reservation
3. **Get All Reservations** - Lists all reservations with filters
4. **Get Reservation by ID** - Retrieves specific reservation
5. **Update Reservation** - Modifies reservation details
6. **Confirm Reservation** - Confirms a pending reservation
7. **Seat Reservation** - Marks customer as seated
8. **Complete Reservation** - Marks reservation as completed
9. **Mark as No Show** - Handles no-show customers
10. **Get Reservation Statistics** - Retrieves analytics data
11. **Cancel Reservation** - Cancels/deletes reservation

### 5. Waiter Requests
Test waiter request functionality:
1. **Create Waiter Request** - Customer requests waiter assistance
2. **Get All Waiter Requests** - Lists all requests
3. **Get Waiter Request by ID** - Retrieves specific request
4. **Update Waiter Request** - Modifies request details
5. **Complete Waiter Request** - Marks request as completed
6. **Cancel Waiter Request** - Cancels the request
7. **Get Requests by Table** - Lists requests for specific table
8. **Get Requests by Waiter** - Lists requests assigned to waiter
9. **Delete Waiter Request** - Removes request

### 6. QR Code Management
Test QR code functionality:
1. **Validate QR Code** - Validates table QR codes
2. **Generate Table QR Code** - Creates QR code for table
3. **Generate Menu QR Code** - Creates menu QR code for table
4. **Get QR Content for Table** - Retrieves QR code content
5. **Batch Generate QR Codes** - Creates multiple QR codes

### 7. Public API (No Authentication)
Test public endpoints that don't require authentication:
1. **Public QR Validation** - Validates QR codes publicly
2. **Public Waiter Request** - Allows customers to request waiter
3. **Public Check Availability** - Public reservation availability check

## API Response Formats

### Success Response
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // Response data
    }
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        // Validation errors if applicable
    }
}
```

### Paginated Response
```json
{
    "success": true,
    "data": {
        "data": [
            // Array of items
        ],
        "current_page": 1,
        "per_page": 15,
        "total": 100,
        "last_page": 7
    }
}
```

## Common Request Parameters

### Reservation Creation
```json
{
    "branch_id": 1,
    "table_id": 1,
    "customer_name": "John Doe",
    "customer_phone": "+1234567890",
    "email": "<EMAIL>",
    "party_size": 4,
    "reservation_datetime": "2024-12-25 19:00:00",
    "duration_minutes": 120,
    "special_requests": "Window table preferred",
    "notes": "Anniversary dinner",
    "area_id": 1
}
```

### Table Creation
```json
{
    "branch_id": 1,
    "area_id": 1,
    "table_number": "T001",
    "table_name": "Table 1",
    "seating_capacity": 4,
    "section": "Window Side",
    "status": "available",
    "notes": "Near the window",
    "is_active": true
}
```

### Area Creation
```json
{
    "branch_id": 1,
    "name": "Main Dining Area",
    "description": "Primary dining area with 20 tables"
}
```

## Status Values

### Reservation Status
- `pending` - Awaiting confirmation
- `confirmed` - Confirmed by restaurant
- `seated` - Customer has been seated
- `completed` - Reservation finished
- `cancelled` - Cancelled by customer/restaurant
- `no_show` - Customer didn't show up

### Table Status
- `available` - Ready for seating
- `occupied` - Currently in use
- `reserved` - Reserved for upcoming reservation
- `maintenance` - Under maintenance
- `inactive` - Not in service

### Waiter Request Types
- `service` - General service request
- `bill` - Request for bill/check
- `menu` - Menu assistance needed
- `complaint` - Customer complaint
- `assistance` - General assistance

## Testing Tips

1. **Sequential Testing**: Follow the workflow order as some endpoints depend on data created by previous requests

2. **Variable Auto-Population**: The collection automatically saves IDs from creation responses, so you don't need to manually copy/paste them

3. **Error Handling**: Test both valid and invalid data to ensure proper error responses

4. **Authentication**: Most endpoints require authentication except those in the "Public API" folder

5. **Pagination**: Use query parameters like `per_page`, `page`, and filters for list endpoints

6. **Date Formats**: Use `YYYY-MM-DD HH:MM:SS` format for datetime fields

7. **Status Transitions**: Test reservation status transitions in logical order (pending → confirmed → seated → completed)

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Ensure you've run the login request and the auth token is saved
2. **404 Not Found**: Check if the resource ID exists and is valid
3. **422 Validation Error**: Review the request body for required fields and correct formats
4. **500 Server Error**: Check Laravel logs for detailed error information

### Debug Steps

1. Check Laravel logs: `storage/logs/laravel.log`
2. Verify database connections and migrations
3. Ensure proper environment configuration
4. Check middleware and route definitions

## Additional Notes

- All timestamps are in UTC format
- Phone numbers should include country codes
- Email validation follows standard RFC format
- QR codes are generated as base64 encoded images
- Batch operations have limits to prevent server overload

This collection provides comprehensive coverage of all Reservation module functionality and can be used for both development testing and API documentation purposes.