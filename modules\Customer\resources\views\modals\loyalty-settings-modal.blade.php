<!-- Loyalty Settings Modal -->
<div class="fixed z-10 inset-0 overflow-y-auto hidden" id="loyaltySettingsModal">
    <div class="flex items-center justify-center min-h-screen px-4 py-8">
        <div class="fixed inset-0 transition-opacity">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <div class="bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full">
            <div class="modal-header flex items-center justify-between bg-gray-100 py-3 px-5 border-b">
                <h5 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-cog mr-2"></i>Loyalty Program Settings
                </h5>
                <button type="button" class="text-gray-500 hover:text-gray-700 focus:outline-none" data-bs-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="loyaltySettingsForm">
                @csrf
                <div class="modal-body max-h-[70vh] overflow-y-auto p-4">
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Program Status -->
                        <div class="mb-4">
                            <h6 class="text-indigo-600 border-b-2 border-gray-200 pb-2 mb-3 flex items-center">
                                <i class="fas fa-toggle-on mr-2"></i>Program Status
                            </h6>
                            <div class="flex items-center">
                                <input class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" type="checkbox" id="loyalty_enabled" name="loyalty_enabled" checked>
                                <label class="ml-2 font-bold text-gray-700" for="loyalty_enabled">
                                    Enable Loyalty Program
                                </label>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">When disabled, customers cannot earn or redeem points</div>
                        </div>
                        
                        <!-- Earning Rules -->
                        <div class="mb-4">
                            <h6 class="text-indigo-600 border-b-2 border-gray-200 pb-2 mb-3 flex items-center">
                                <i class="fas fa-coins mr-2"></i>Points Earning Rules
                            </h6>
                        </div>
                        
                        <div class="mb-3">
                            <label for="points_per_dollar" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-dollar-sign mr-1"></i>Points per Dollar Spent
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="points_per_dollar" name="points_per_dollar"
                                   min="0" step="0.01" value="1" required>
                            <div class="mt-1 text-sm text-gray-500">How many points customers earn per dollar spent</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="minimum_order_amount" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-shopping-cart mr-1"></i>Minimum Order Amount ($)
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="minimum_order_amount" name="minimum_order_amount"
                                   min="0" step="0.01" value="0">
                            <div class="mt-1 text-sm text-gray-500">Minimum order amount to earn points (0 = no minimum)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="signup_bonus" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-gift mr-1"></i>Signup Bonus Points
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="signup_bonus" name="signup_bonus"
                                   min="0" step="1" value="100">
                            <div class="mt-1 text-sm text-gray-500">Points awarded when customer signs up</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="birthday_bonus" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-birthday-cake mr-1"></i>Birthday Bonus Points
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="birthday_bonus" name="birthday_bonus"
                                   min="0" step="1" value="50">
                            <div class="mt-1 text-sm text-gray-500">Points awarded on customer's birthday</div>
                        </div>
                        
                        <!-- Redemption Rules -->
                        <div class="mb-4 mt-3">
                            <h6 class="text-indigo-600 border-b-2 border-gray-200 pb-2 mb-3 flex items-center">
                                <i class="fas fa-gift mr-2"></i>Points Redemption Rules
                            </h6>
                        </div>
                        
                        <div class="mb-3">
                            <label for="points_value" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-percentage mr-1"></i>Points Value ($ per 100 points)
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="points_value" name="points_value"
                                   min="0" step="0.01" value="5.00" required>
                            <div class="mt-1 text-sm text-gray-500">Dollar value of 100 points when redeemed</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="minimum_redemption" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-coins mr-1"></i>Minimum Points for Redemption
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="minimum_redemption" name="minimum_redemption"
                                   min="1" step="1" value="100" required>
                            <div class="mt-1 text-sm text-gray-500">Minimum points required to redeem</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="maximum_redemption_percent" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-percent mr-1"></i>Max Redemption (% of order)
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="maximum_redemption_percent" name="maximum_redemption_percent"
                                   min="1" max="100" step="1" value="50">
                            <div class="mt-1 text-sm text-gray-500">Maximum percentage of order that can be paid with points</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="points_expiry_months" class="block font-semibold text-gray-700 flex items-center">
                                <i class="fas fa-calendar mr-1"></i>Points Expiry (Months)
                            </label>
                            <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" id="points_expiry_months" name="points_expiry_months"
                                   min="0" step="1" value="0">
                            <div class="mt-1 text-sm text-gray-500">Points expire after this many months (0 = never expire)</div>
                        </div>
                        
                        <!-- Tier System -->
                        <div class="mb-4 mt-3">
                            <h6 class="text-indigo-600 border-b-2 border-gray-200 pb-2 mb-3 flex items-center">
                                <i class="fas fa-layer-group mr-2"></i>Customer Tiers
                            </h6>
                        </div>
                        
                        <div class="mb-3">
                            <div class="flex items-center">
                                <input class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" type="checkbox" id="enable_tiers" name="enable_tiers">
                                <label class="ml-2 font-bold text-gray-700" for="enable_tiers">
                                    Enable Customer Tiers
                                </label>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">Customers advance to higher tiers based on spending</div>
                        </div>
                        
                        <div id="tier-settings" class="bg-gray-50 p-4 rounded-lg border border-gray-200 hidden">
                            <div class="grid grid-cols-1 gap-4">
                                <div class="mb-3">
                                    <label class="block font-semibold text-gray-700">Silver Tier Threshold ($)</label>
                                    <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" name="silver_threshold" value="500" min="0" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label class="block font-semibold text-gray-700">Gold Tier Threshold ($)</label>
                                    <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" name="gold_threshold" value="1000" min="0" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label class="block font-semibold text-gray-700">Platinum Tier Threshold ($)</label>
                                    <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" name="platinum_threshold" value="2500" min="0" step="0.01">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-4">
                                <div class="mb-3">
                                    <label class="block font-semibold text-gray-700">Silver Multiplier</label>
                                    <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" name="silver_multiplier" value="1.25" min="1" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label class="block font-semibold text-gray-700">Gold Multiplier</label>
                                    <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" name="gold_multiplier" value="1.5" min="1" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label class="block font-semibold text-gray-700">Platinum Multiplier</label>
                                    <input type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50" name="platinum_multiplier" value="2.0" min="1" step="0.01">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notifications -->
                        <div class="mb-4 mt-3">
                            <h6 class="text-indigo-600 border-b-2 border-gray-200 pb-2 mb-3 flex items-center">
                                <i class="fas fa-bell mr-2"></i>Notifications
                            </h6>
                        </div>
                        
                        <div class="mb-3">
                            <div class="flex items-center">
                                <input class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" type="checkbox" id="notify_points_earned" name="notify_points_earned" checked>
                                <label class="ml-2 text-gray-700" for="notify_points_earned">
                                    Notify when points are earned
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="flex items-center">
                                <input class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" type="checkbox" id="notify_points_redeemed" name="notify_points_redeemed" checked>
                                <label class="ml-2 text-gray-700" for="notify_points_redeemed">
                                    Notify when points are redeemed
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="flex items-center">
                                <input class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" type="checkbox" id="notify_tier_upgrade" name="notify_tier_upgrade" checked>
                                <label class="ml-2 text-gray-700" for="notify_tier_upgrade">
                                    Notify on tier upgrades
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="flex items-center">
                                <input class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" type="checkbox" id="notify_birthday" name="notify_birthday" checked>
                                <label class="ml-2 text-gray-700" for="notify_birthday">
                                    Send birthday notifications
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer bg-gray-100 py-3 px-5 flex justify-end">
                    <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none" data-bs-dismiss="modal" onclick="closeModal()">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-md hover:from-indigo-600 hover:to-purple-700 focus:outline-none">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Toggle tier settings visibility
    $('#enable_tiers').on('change', function() {
        $('#tier-settings').slideToggle();
    });
    
    // Load current settings when modal is shown
    $('#loyaltySettingsModal').on('show.bs.modal', function() {
        loadLoyaltySettings();
    });
    
    // Save loyalty settings
    $('#loyaltySettingsForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        $.ajax({
            url: '{{ route("loyalty.settings.save") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    closeModal();
                    Swal.fire('Success!', response.message, 'success');
                }
            },
            error: function(xhr) {
                const errors = xhr.responseJSON?.errors || {};
                let errorMessage = xhr.responseJSON?.message || 'An error occurred';
                
                if (Object.keys(errors).length > 0) {
                    errorMessage = Object.values(errors).flat().join('<br>');
                }
                
                Swal.fire('Error!', errorMessage, 'error');
            }
        });
    });
    
    function loadLoyaltySettings() {
        $.get('{{ route("loyalty.settings.get") }}')
            .done(function(response) {
                if (response.success) {
                    const settings = response.data;
                    
                    // Populate form fields
                    $('#loyalty_enabled').prop('checked', settings.loyalty_enabled || false);
                    $('#points_per_dollar').val(settings.points_per_dollar || 1);
                    $('#minimum_order_amount').val(settings.minimum_order_amount || 0);
                    $('#signup_bonus').val(settings.signup_bonus || 100);
                    $('#birthday_bonus').val(settings.birthday_bonus || 50);
                    $('#points_value').val(settings.points_value || 5.00);
                    $('#minimum_redemption').val(settings.minimum_redemption || 100);
                    $('#maximum_redemption_percent').val(settings.maximum_redemption_percent || 50);
                    $('#points_expiry_months').val(settings.points_expiry_months || 0);
                    
                    // Tier settings
                    $('#enable_tiers').prop('checked', settings.enable_tiers || false);
                    $('#tier-settings').toggle(settings.enable_tiers);
                    
                    $('input[name="silver_threshold"]').val(settings.silver_threshold || 500);
                    $('input[name="gold_threshold"]').val(settings.gold_threshold || 1000);
                    $('input[name="platinum_threshold"]').val(settings.platinum_threshold || 2500);
                    $('input[name="silver_multiplier"]').val(settings.silver_multiplier || 1.25);
                    $('input[name="gold_multiplier"]').val(settings.gold_multiplier || 1.5);
                    $('input[name="platinum_multiplier"]').val(settings.platinum_multiplier || 2.0);
                    
                    // Notifications
                    $('#notify_points_earned').prop('checked', settings.notify_points_earned !== false);
                    $('#notify_points_redeemed').prop('checked', settings.notify_points_redeemed !== false);
                    $('#notify_tier_upgrade').prop('checked', settings.notify_tier_upgrade !== false);
                    $('#notify_birthday').prop('checked', settings.notify_birthday !== false);
                }
            })
            .fail(function() {
                console.log('Failed to load loyalty settings, using defaults');
            });
    }
});

function openModal() {
  $('#loyaltySettingsModal').removeClass('hidden');
}

function closeModal() {
  $('#loyaltySettingsModal').addClass('hidden');
}
</script>