<form id="createPersonnelForm" action="<?php echo e(route('delivery.personnel.store')); ?>" method="POST" class="space-y-6">
    <?php echo csrf_field(); ?>
    
    <!-- Personal Information Section -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-lg font-medium text-gray-900 mb-4">المعلومات الشخصية</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل *</label>
                <input type="text" name="name" id="name" required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف *</label>
                <input type="text" name="phone" id="phone" required placeholder="+966xxxxxxxxx"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني *</label>
                <input type="email" name="email" id="email" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="national_id" class="block text-sm font-medium text-gray-700 mb-1">رقم الهوية الوطنية *</label>
                <input type="text" name="national_id" id="national_id" required maxlength="10"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">تاريخ الميلاد *</label>
                <input type="date" name="date_of_birth" id="date_of_birth" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="branch_id" class="block text-sm font-medium text-gray-700 mb-1">الفرع *</label>
                <select name="branch_id" id="branch_id" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">اختر الفرع</option>
                    <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($branch->id); ?>"><?php echo e($branch->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
    </div>

    <!-- Vehicle Information Section -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-lg font-medium text-gray-900 mb-4">معلومات المركبة</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="vehicle_type" class="block text-sm font-medium text-gray-700 mb-1">نوع المركبة *</label>
                <select name="vehicle_type" id="vehicle_type" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">اختر نوع المركبة</option>
                    <option value="motorcycle">دراجة نارية</option>
                    <option value="bicycle">دراجة هوائية</option>
                    <option value="car">سيارة</option>
                    <option value="scooter">سكوتر</option>
                </select>
            </div>
            
            <div>
                <label for="vehicle_model" class="block text-sm font-medium text-gray-700 mb-1">موديل المركبة</label>
                <input type="text" name="vehicle_model" id="vehicle_model"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="vehicle_plate_number" class="block text-sm font-medium text-gray-700 mb-1">رقم اللوحة *</label>
                <input type="text" name="vehicle_plate_number" id="vehicle_plate_number" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="vehicle_color" class="block text-sm font-medium text-gray-700 mb-1">لون المركبة</label>
                <input type="text" name="vehicle_color" id="vehicle_color"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
        </div>
    </div>

    <!-- License Information Section -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-lg font-medium text-gray-900 mb-4">معلومات الرخصة</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="driving_license_number" class="block text-sm font-medium text-gray-700 mb-1">رقم رخصة القيادة *</label>
                <input type="text" name="driving_license_number" id="driving_license_number" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="license_expiry_date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء الرخصة *</label>
                <input type="date" name="license_expiry_date" id="license_expiry_date" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
        </div>
    </div>

    <!-- Emergency Contact Section -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-lg font-medium text-gray-900 mb-4">جهة الاتصال في حالات الطوارئ</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 mb-1">اسم جهة الاتصال *</label>
                <input type="text" name="emergency_contact_name" id="emergency_contact_name" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 mb-1">رقم هاتف جهة الاتصال *</label>
                <input type="text" name="emergency_contact_phone" id="emergency_contact_phone" required placeholder="+966xxxxxxxxx"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
        </div>
    </div>

    <!-- Status and Settings Section -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-lg font-medium text-gray-900 mb-4">الحالة والإعدادات</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                <select name="status" id="status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="suspended">معلق</option>
                </select>
            </div>
            
            <div>
                <label for="max_concurrent_orders" class="block text-sm font-medium text-gray-700 mb-1">الحد الأقصى للطلبات المتزامنة</label>
                <input type="number" name="max_concurrent_orders" id="max_concurrent_orders" min="1" max="10" value="3"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
        </div>
    </div>

    <!-- Notes Section -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-lg font-medium text-gray-900 mb-4">ملاحظات</h4>
        <div>
            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات إضافية</label>
            <textarea name="notes" id="notes" rows="3"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
        </div>
    </div>

    <!-- Form Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t">
        <button type="button" onclick="closeModal('createPersonnelModal')" 
                class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 text-sm font-medium rounded-md transition-colors duration-200">
            إلغاء
        </button>
        <button type="submit" 
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
            حفظ
        </button>
    </div>
</form>

<script>
document.getElementById('createPersonnelForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('createPersonnelModal');
            // Reload the DataTable
            if (typeof table !== 'undefined') {
                table.ajax.reload();
            }
            // Show success message
            alert('تم إضافة الموظف بنجاح');
        } else {
            // Handle validation errors
            if (data.errors) {
                let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                Object.values(data.errors).forEach(error => {
                    errorMessage += '- ' + error[0] + '\n';
                });
                alert(errorMessage);
            } else {
                alert('حدث خطأ أثناء إضافة الموظف');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إضافة الموظف');
    });
});
</script><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Delivery\Providers/../resources/views/personnel/partials/create-form.blade.php ENDPATH**/ ?>