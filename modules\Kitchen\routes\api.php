<?php

use Illuminate\Support\Facades\Route;
use Modules\Kitchen\Http\Controllers\KitchenController;
use Modules\Kitchen\Http\Controllers\KotOrderController;

/*
|--------------------------------------------------------------------------
| Kitchen Module API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Kitchen module.
| These routes are loaded by the KitchenServiceProvider within a group
| which is assigned the "api" middleware group.
|
*/

Route::middleware(['auth:sanctum'])->group(function () {
    
    // Kitchen Management Routes
    Route::prefix('kitchens')->group(function () {
        Route::get('/', [KitchenController::class, 'index'])->name('api.kitchens.index');
        Route::post('/', [KitchenController::class, 'store'])->name('api.kitchens.store');
        Route::get('/{kitchen}', [KitchenController::class, 'show'])->name('api.kitchens.show');
        Route::put('/{kitchen}', [KitchenController::class, 'update'])->name('api.kitchens.update');
        Route::delete('/{kitchen}', [KitchenController::class, 'destroy'])->name('api.kitchens.destroy');
        
        // Kitchen Menu Item Management
        Route::get('/{kitchen}/menu-items', [KitchenController::class, 'menuItems'])->name('api.kitchens.menu-items');
        Route::post('/{kitchen}/menu-items', [KitchenController::class, 'assignMenuItem'])->name('api.kitchens.assign-menu-item');
        Route::put('/{kitchen}/menu-items/{menuItem}', [KitchenController::class, 'updateMenuItem'])->name('api.kitchens.update-menu-item');
        Route::delete('/{kitchen}/menu-items/{menuItem}', [KitchenController::class, 'removeMenuItem'])->name('api.kitchens.remove-menu-item');
        Route::get('/{kitchen}/available-menu-items', [KitchenController::class, 'availableMenuItems'])->name('api.kitchens.available-menu-items');
    });

    // KOT Order Management Routes
    Route::prefix('kot-orders')->group(function () {
        Route::get('/', [KotOrderController::class, 'index'])->name('api.kot-orders.index');
        Route::get('/statistics', [KotOrderController::class, 'statistics'])->name('api.kot-orders.statistics');
        Route::get('/active', [KotOrderController::class, 'activeKots'])->name('api.kot-orders.active');
        Route::get('/{kotOrder}', [KotOrderController::class, 'show'])->name('api.kot-orders.show');
        Route::patch('/{kotOrder}/status', [KotOrderController::class, 'updateStatus'])->name('api.kot-orders.update-status');
        Route::patch('/{kotOrder}/priority', [KotOrderController::class, 'updatePriority'])->name('api.kot-orders.update-priority');
        Route::patch('/{kotOrder}/assign', [KotOrderController::class, 'assign'])->name('api.kot-orders.assign');
    });

    // Kitchen Display Routes
    Route::prefix('kitchen-display')->group(function () {
        Route::get('/', [KotOrderController::class, 'kitchenDisplay'])->name('api.kitchen-display.index');
        Route::get('/{kitchen}', [KotOrderController::class, 'kitchenDisplay'])->name('api.kitchen-display.kitchen');
    });
});
