<?php

namespace Modules\Customer\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class CustomerServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register services
        $this->app->bind(
            \Modules\Customer\Services\CustomerService::class,
            \Modules\Customer\Services\CustomerService::class
        );
        
        $this->app->bind(
            \Modules\Customer\Services\LoyaltyService::class,
            \Modules\Customer\Services\LoyaltyService::class
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'customer');
    }

    /**
     * Load module routes
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            Route::group([
                'middleware' => 'api',
                'prefix' => 'api',
                'namespace' => 'Modules\\Customer\\Http\\Controllers\\Api',
            ], function () {
                require __DIR__ . '/../routes/api.php';
            });
        }
        
        // Load web routes
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            Route::group([
                'middleware' => 'web',
                'namespace' => 'Modules\\Customer\\Http\\Controllers\\Web',
            ], function () {
                require __DIR__ . '/../routes/web.php';
            });
        }
    }
}