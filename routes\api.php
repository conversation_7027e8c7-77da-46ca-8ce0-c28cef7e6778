<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Route::get('/user', function (Request $request) {
//     return $request->user();
// });

// // Include module API routes
// $modules = [
//     'Auth',
//     'Customer',
//     'Delivery',
//     'HR',
//     'Inventory',
//     'Kitchen',
//     'Menu',
//     'Orders',
//     'Payment',
//     'Payroll',
//     'Reports',
//     'Reservation',
//     'Settings',
//     'Tenant',
// ];

// foreach ($modules as $module) {
//     $apiRoutePath = base_path("modules/{$module}/routes/api.php");
//     if (file_exists($apiRoutePath)) {
//         include $apiRoutePath;
//     }
// }
