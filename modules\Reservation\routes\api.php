<?php

use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\Api\ReservationController;
use Modules\Reservation\Http\Controllers\Api\WaiterRequestController;
use Modules\Reservation\Http\Controllers\Api\AreaController;
use Modules\Reservation\Http\Controllers\Api\TableController;
use Modules\Reservation\Http\Controllers\Api\QRCodeController;

/*
|--------------------------------------------------------------------------
| API Routes for Reservation Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Reservation module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum'])->name('api.reservation.')->group(function () {
    
    // Reservation API Routes
    Route::prefix('api/reservations')->name('reservations.')->group(function () {
        Route::get('/', [ReservationController::class, 'index'])->name('index');
        Route::post('/', [ReservationController::class, 'store'])->name('store');
        Route::get('/{id}', [ReservationController::class, 'show'])->name('show');
        Route::put('/{id}', [ReservationController::class, 'update'])->name('update');
        Route::delete('/{id}', [ReservationController::class, 'destroy'])->name('destroy');
        
        // Additional reservation actions
        Route::post('/{id}/confirm', [ReservationController::class, 'confirm'])->name('confirm');
        Route::post('/{id}/seat', [ReservationController::class, 'seat'])->name('seat');
        Route::post('/{id}/complete', [ReservationController::class, 'complete'])->name('complete');
        Route::post('/{id}/no-show', [ReservationController::class, 'noShow'])->name('no-show');
        
        // Availability check
        Route::post('/check-availability', [ReservationController::class, 'checkAvailability'])->name('check-availability');
        
        // Statistics
        Route::get('/statistics/overview', [ReservationController::class, 'statistics'])->name('statistics');
    });

    // Waiter Request API Routes
    Route::prefix('api/waiter-requests')->name('waiter-requests.')->group(function () {
        Route::get('/', [WaiterRequestController::class, 'index'])->name('index');
        Route::post('/', [WaiterRequestController::class, 'store'])->name('store');
        Route::get('/{id}', [WaiterRequestController::class, 'show'])->name('show');
        Route::put('/{id}', [WaiterRequestController::class, 'update'])->name('update');
        Route::delete('/{id}', [WaiterRequestController::class, 'destroy'])->name('destroy');
        
        // Additional waiter request actions
        Route::post('/{id}/complete', [WaiterRequestController::class, 'complete'])->name('complete');
        Route::post('/{id}/cancel', [WaiterRequestController::class, 'cancel'])->name('cancel');
        
        // Get requests by table or waiter
        Route::get('/table/{tableId}', [WaiterRequestController::class, 'getTableRequests'])->name('table-requests');
        Route::get('/waiter/{waiterId}', [WaiterRequestController::class, 'getWaiterRequests'])->name('waiter-requests');
    });

    // Area API Routes
    Route::prefix('api/areas')->name('areas.')->group(function () {
        Route::get('/', [AreaController::class, 'index'])->name('index');
        Route::post('/', [AreaController::class, 'store'])->name('store');
        Route::get('/{id}', [AreaController::class, 'show'])->name('show');
        Route::put('/{id}', [AreaController::class, 'update'])->name('update');
        Route::delete('/{id}', [AreaController::class, 'destroy'])->name('destroy');
    });

    // Table API Routes
    Route::prefix('api/tables')->name('tables.')->group(function () {
        Route::get('/', [TableController::class, 'index'])->name('index');
        Route::post('/', [TableController::class, 'store'])->name('store');
        Route::get('/{id}', [TableController::class, 'show'])->name('show');
        Route::put('/{id}', [TableController::class, 'update'])->name('update');
        Route::delete('/{id}', [TableController::class, 'destroy'])->name('destroy');
        
        // Additional table actions
        Route::post('/{id}/status', [TableController::class, 'updateStatus'])->name('update-status');
        Route::get('/area/{areaId}', [TableController::class, 'getTablesByArea'])->name('by-area');
        Route::get('/available/{datetime}', [TableController::class, 'getAvailableTables'])->name('available');
    });

    // QR Code API Routes
    Route::prefix('qr-codes')->name('qr-codes.')->group(function () {
        Route::post('/validate', [QRCodeController::class, 'validateQR'])->name('validate');
        Route::post('/batch-generate', [QRCodeController::class, 'batchGenerate'])->name('batch-generate');
        Route::get('/table/{tableId}/content', [QRCodeController::class, 'getQRContent'])->name('table-content');
        Route::post('/table/{tableId}/generate', [QRCodeController::class, 'generateTableQR'])->name('generate-table');
        Route::post('/table/{tableId}/menu', [QRCodeController::class, 'generateMenuQR'])->name('generate-menu');
    });
});

// Public API Routes (no authentication required)
Route::prefix('public')->name('api.reservation.public.')->group(function () {
    
    // Public QR validation (for customer access)
    Route::post('qr/validate', [QRCodeController::class, 'validateQR'])->name('qr.validate');
    
    // Public waiter request creation (for customers)
    Route::post('waiter-requests', [WaiterRequestController::class, 'store'])->name('waiter-requests.store');
    
    // Public availability check
    Route::post('reservations/check-availability', [ReservationController::class, 'checkAvailability'])->name('reservations.check-availability');
});