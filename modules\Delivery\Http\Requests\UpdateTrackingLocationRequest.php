<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateTrackingLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'assignment_id' => 'required|exists:delivery_assignments,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'accuracy' => 'nullable|numeric|min:0',
            'speed' => 'nullable|numeric|min:0',
            'heading' => 'nullable|numeric|between:0,360',
            'altitude' => 'nullable|numeric',
            'notes' => 'nullable|string|max:500',
            'battery_level' => 'nullable|integer|between:0,100',
            'is_manual' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'assignment_id.required' => 'Assignment ID is required',
            'assignment_id.exists' => 'Selected assignment does not exist',
            'latitude.required' => 'Latitude is required',
            'latitude.between' => 'Latitude must be between -90 and 90',
            'longitude.required' => 'Longitude is required',
            'longitude.between' => 'Longitude must be between -180 and 180',
            'accuracy.min' => 'Accuracy cannot be negative',
            'speed.min' => 'Speed cannot be negative',
            'heading.between' => 'Heading must be between 0 and 360 degrees',
            'notes.max' => 'Notes cannot exceed 500 characters',
            'battery_level.between' => 'Battery level must be between 0 and 100',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('is_manual')) {
            $this->merge(['is_manual' => false]);
        }

        // Round coordinates to reasonable precision (6 decimal places)
        if ($this->has('latitude')) {
            $this->merge(['latitude' => round($this->latitude, 6)]);
        }

        if ($this->has('longitude')) {
            $this->merge(['longitude' => round($this->longitude, 6)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if assignment exists and is active
            if ($this->assignment_id) {
                $assignment = \Modules\Delivery\Entities\DeliveryAssignment::find($this->assignment_id);
                
                if (!$assignment) {
                    $validator->errors()->add('assignment_id', 'Assignment not found');
                    return;
                }

                // Check if assignment is in a trackable status
                if (!in_array($assignment->status, ['assigned', 'picked_up', 'in_transit'])) {
                    $validator->errors()->add('assignment_id', 'Cannot track location for assignment with status: ' . $assignment->status);
                }

                // Check if user is authorized to update this assignment
                $user = Auth::user();
                if ($assignment->delivery_personnel_id) {
                    $personnel = $assignment->deliveryPersonnel;
                    if ($personnel && $personnel->user_id !== $user->id && !$user->hasRole(['admin', 'manager'])) {
                        $validator->errors()->add('assignment_id', 'You are not authorized to update location for this assignment');
                    }
                }

                // Check if assignment belongs to the same tenant
                if ($assignment->order && $assignment->order->tenant_id !== $user->tenant_id) {
                    $validator->errors()->add('assignment_id', 'Assignment does not belong to your organization');
                }
            }

            // Validate coordinates are reasonable (not in the middle of ocean, etc.)
            if ($this->has('latitude') && $this->has('longitude')) {
                $lat = $this->latitude;
                $lng = $this->longitude;

                // Basic sanity check - coordinates should not be 0,0 (Gulf of Guinea)
                if ($lat == 0 && $lng == 0) {
                    $validator->errors()->add('latitude', 'Invalid coordinates detected');
                }

                // Check for obviously invalid coordinates
                if (abs($lat) < 0.001 && abs($lng) < 0.001) {
                    $validator->errors()->add('latitude', 'Coordinates appear to be invalid or too precise');
                }
            }

            // Validate speed is reasonable (not faster than 200 km/h for delivery)
            if ($this->has('speed') && $this->speed > 200) {
                $validator->errors()->add('speed', 'Speed seems unreasonably high for delivery');
            }
        });
    }
}