<?php

namespace Modules\Reports\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Report;
use Modules\Reports\Services\ReportsService;
use Modules\Reports\Http\Requests\GenerateReportRequest;
use App\Models\Branch;

class ReportsWebController extends Controller
{
    protected $reportsService;

    public function __construct(ReportsService $reportsService)
    {
        $this->reportsService = $reportsService;
    }

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Display reports dashboard
     */
    public function index()
    {
        $tenantId = $this->getTenantId();
        
        $reports = Report::where('tenant_id', $tenantId)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('Reports::index', compact('reports'));
    }

    /**
     * Show the form for creating a new report
     */
    public function create(Request $request)
    {
        $tenantId = $this->getTenantId();
        
        $branches = Branch::where('tenant_id', $tenantId)->get();
        
        $recentReports = Report::where('tenant_id', $tenantId)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('reports::generate', compact('branches', 'recentReports'));
    }

    /**
     * Store a newly created report
     */
    public function store(GenerateReportRequest $request)
    {
        try {
            $tenantId = $this->getTenantId();
            $data = $request->validated();
            $data['tenant_id'] = $tenantId;
            $data['generated_by'] = $request->user()->id ?? 1;

            $report = $this->reportsService->generateReport($data);

            return redirect()->route('reports.show', $report)
                ->with('success', 'تم إنشاء التقرير بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ في إنشاء التقرير: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified report
     */
    public function show(Request $request, Report $report)
    {
        $tenantId = $this->getTenantId();

        // Ensure the report belongs to the current tenant
        if ($report->tenant_id !== $tenantId) {
            abort(404);
        }

        return view('reports::show', compact('report'));
    }

    /**
     * Download the specified report
     */
    public function download(Request $request, Report $report)
    {
        $tenantId = $this->getTenantId();

        // Ensure the report belongs to the current tenant
        if ($report->tenant_id !== $tenantId) {
            abort(404);
        }

        $format = $request->get('format', $report->format ?? 'pdf');
        
        try {
            $filePath = $this->reportsService->exportReport($report, $format);
            $report->update(['file_path' => $filePath, 'status' => 'completed']);
            
            return response()->download(storage_path('app/' . $filePath));
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ في تحميل التقرير: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified report
     */
    public function destroy(Request $request, Report $report)
    {
        $tenantId = $this->getTenantId();

        // Ensure the report belongs to the current tenant
        if ($report->tenant_id !== $tenantId) {
            abort(404);
        }

        $report->delete();

        return redirect()->route('reports.index')
            ->with('success', 'تم حذف التقرير بنجاح');
    }

    /**
     * Quick daily sales report
     */
    public function quickDailySales(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branchId = $request->get('branch_id');
        
        $data = [
            'name' => 'ملخص المبيعات اليومية - ' . now()->format('Y-m-d'),
            'type' => 'daily_sales_summary',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'period' => 'today',
            'format' => 'pdf',
            'generated_by' => $request->user()->id ?? 1,
        ];

        try {
            $report = $this->reportsService->generateReport($data);
            return redirect()->route('reports.show', $report);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ في إنشاء التقرير: ' . $e->getMessage()]);
        }
    }

    /**
     * Quick hourly sales report
     */
    public function quickHourlySales(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branchId = $request->get('branch_id');
        
        $data = [
            'name' => 'تقرير المبيعات بالساعة - ' . now()->format('Y-m-d'),
            'type' => 'hourly_sales',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'period' => 'today',
            'format' => 'pdf',
            'generated_by' => $request->user()->id ?? 1,
        ];

        try {
            $report = $this->reportsService->generateReport($data);
            return redirect()->route('reports.show', $report);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ في إنشاء التقرير: ' . $e->getMessage()]);
        }
    }

    /**
     * Quick tax report
     */
    public function quickTaxReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branchId = $request->get('branch_id');
        
        $data = [
            'name' => 'تقرير الضرائب - ' . now()->format('Y-m-d'),
            'type' => 'tax_report',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'period' => 'today',
            'format' => 'pdf',
            'generated_by' => $request->user()->id ?? 1,
        ];

        try {
            $report = $this->reportsService->generateReport($data);
            return redirect()->route('reports.show', $report);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ في إنشاء التقرير: ' . $e->getMessage()]);
        }
    }

    /**
     * Quick void/cancelled orders report
     */
    public function quickVoidCancelled(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branchId = $request->get('branch_id');
        
        $data = [
            'name' => 'تقرير الطلبات الملغية - ' . now()->format('Y-m-d'),
            'type' => 'void_cancelled',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'period' => 'today',
            'format' => 'pdf',
            'generated_by' => $request->user()->id ?? 1,
        ];

        try {
            $report = $this->reportsService->generateReport($data);
            return redirect()->route('reports.show', $report);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ في إنشاء التقرير: ' . $e->getMessage()]);
        }
    }

    /**
     * Daily reports category page
     */
    public function dailyReports(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();
        
        $dailyReportTypes = [
            'daily_sales_summary' => 'ملخص المبيعات اليومية',
            'daily_stock_drawer' => 'تقرير المخزون اليومي',
            'daily_payments' => 'تقرير المدفوعات اليومية',
            'daily_discounts' => 'تقرير الخصومات اليومية',
            'daily_returns' => 'تقرير المرتجعات اليومية',
            'daily_cash_drawer' => 'تقرير الخزنة اليومية',
            'hourly_sales' => 'تقرير المبيعات بالساعة',
            'tax_report' => 'تقرير الضرائب',
        ];

        return view('reports::categories.daily', compact('branches', 'dailyReportTypes'));
    }

    /**
     * Periodic reports category page
     */
    public function periodicReports(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();
        
        $periodicReportTypes = [
            'product_performance' => 'أداء المنتجات',
            'staff_performance' => 'أداء الموظفين',
            'profit_report' => 'تقرير الأرباح',
            'customer_report' => 'تقرير العملاء',
        ];

        return view('reports::categories.periodic', compact('branches', 'periodicReportTypes'));
    }

    /**
     * Advanced reports category page
     */
    public function advancedReports(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();
        
        $advancedReportTypes = [
            'stock_movement' => 'حركة المخزون',
            'reorder_point' => 'نقاط إعادة الطلب',
            'void_cancelled' => 'الطلبات الملغية',
            'table_turnover' => 'دوران الطاولات',
            'waste_tracking' => 'تتبع الهدر',
            'supplier_performance' => 'أداء الموردين',
        ];

        return view('reports::categories.advanced', compact('branches', 'advancedReportTypes'));
    }

    /**
     * Restaurant reports category page
     */
    public function restaurantReports(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();
        
        $restaurantReportTypes = [
            'menu_items_report' => 'تقرير عناصر القائمة',
            'meal_time_sales' => 'مبيعات أوقات الوجبات',
        ];

        return view('reports::categories.restaurant', compact('branches', 'restaurantReportTypes'));
    }

    // Sales Reports Methods
    public function dailySalesReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::sales.daily', compact('branches'));
    }

    public function monthlySalesReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::sales.monthly', compact('branches'));
    }

    public function yearlySalesReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::sales.yearly', compact('branches'));
    }

    public function itemSalesReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::sales.items', compact('branches'));
    }

    // Financial Reports Methods
    public function profitLossReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::financial.profit-loss', compact('branches'));
    }

    public function revenueReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::financial.revenue', compact('branches'));
    }

    public function expensesReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::financial.expenses', compact('branches'));
    }

    public function taxReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::financial.tax', compact('branches'));
    }

    // Inventory Reports Methods
    public function stockLevelsReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::inventory.stock-levels', compact('branches'));
    }

    public function lowStockReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::inventory.low-stock', compact('branches'));
    }

    public function stockMovementsReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::inventory.movements', compact('branches'));
    }

    public function wasteReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::inventory.waste', compact('branches'));
    }

    // Staff Reports Methods
    public function attendanceReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::staff.attendance', compact('branches'));
    }

    public function performanceReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::staff.performance', compact('branches'));
    }

    public function payrollReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::staff.payroll', compact('branches'));
    }

    public function workingHoursReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::staff.working-hours', compact('branches'));
    }

    // Customer Reports Methods
    public function customerAnalyticsReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::customers.analytics', compact('branches'));
    }

    public function loyaltyReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::customers.loyalty', compact('branches'));
    }

    public function feedbackReport(Request $request)
    {
        $tenantId = $this->getTenantId();
        $branches = Branch::where('tenant_id', $tenantId)->get();

        return view('reports::customers.feedback', compact('branches'));
    }
}
