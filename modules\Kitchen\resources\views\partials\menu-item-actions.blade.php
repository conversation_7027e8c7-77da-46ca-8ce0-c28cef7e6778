<div class="flex space-x-1">
    <button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors edit-menu-item" 
            data-kitchen-id="{{ $kitchen->id }}"
            data-menu-item-id="{{ $kitchenMenuItem->menu_item_id }}"
            data-prep-time="{{ $kitchenMenuItem->prep_time_minutes }}"
            data-priority="{{ $kitchenMenuItem->priority_level }}"
            data-instructions="{{ $kitchenMenuItem->special_instructions }}"
            data-active="{{ $kitchenMenuItem->is_active ? 'true' : 'false' }}"
            title="Edit Assignment">
        <i class="fas fa-edit"></i>
    </button>
    
    <button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium {{ $kitchenMenuItem->is_active ? 'text-yellow-600 bg-yellow-50 border-yellow-200 hover:bg-yellow-100' : 'text-green-600 bg-green-50 border-green-200 hover:bg-green-100' }} border rounded transition-colors toggle-menu-item-status" 
            data-kitchen-id="{{ $kitchen->id }}"
            data-menu-item-id="{{ $kitchenMenuItem->menu_item_id }}"
            data-current-status="{{ $kitchenMenuItem->is_active ? 'active' : 'inactive' }}"
            title="{{ $kitchenMenuItem->is_active ? 'Deactivate' : 'Activate' }}">
        <i class="fas fa-{{ $kitchenMenuItem->is_active ? 'pause' : 'play' }}"></i>
    </button>
    
    <button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded hover:bg-red-100 transition-colors remove-menu-item" 
            data-kitchen-id="{{ $kitchen->id }}"
            data-menu-item-id="{{ $kitchenMenuItem->menu_item_id }}"
            data-menu-item-name="{{ $kitchenMenuItem->menuItem?->name ?? 'Unknown Item' }}"
            title="Remove from Kitchen">
        <i class="fas fa-trash"></i>
    </button>
</div>