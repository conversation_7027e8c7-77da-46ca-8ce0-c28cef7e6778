<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Branch;
use Modules\Delivery\Entities\DeliveryPersonnel;
use Modules\Delivery\Services\DeliveryPersonnelService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Yajra\DataTables\Facades\DataTables;

class DeliveryPersonnelWebController extends Controller
{
    protected $personnelService;

    public function __construct(DeliveryPersonnelService $personnelService)
    {
        $this->personnelService = $personnelService;
    }

    /**
     * Display delivery personnel management page
     */
    public function index()
    {
        $user = Auth::user();
        $branches = Branch::where('tenant_id', $user->tenant_id)->get();
        
        return view('delivery::personnel.index', compact('branches'));
    }

    /**
     * Show create delivery personnel form
     */
    public function create()
    {
        $user = Auth::user();
        $branches = Branch::where('tenant_id', $user->tenant_id)->get();
        
        return view('delivery::personnel.create', compact('branches'));
    }

    /**
     * Store new delivery personnel
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:users,email',
                'phone' => 'required|string|max:20',
                'national_id' => 'required|string|max:20',
                'date_of_birth' => 'nullable|date',
                'branch_id' => 'required|exists:branches,id',
                'driving_license_number' => 'nullable|string',
                'license_expiry_date' => 'nullable|date|after:today',
                'vehicle_type' => 'required|in:motorcycle,car,bicycle,scooter',
                'vehicle_plate_number' => 'nullable|string',
                'vehicle_model' => 'nullable|string',
                'vehicle_color' => 'nullable|string',
                'emergency_contact_name' => 'nullable|string',
                'emergency_contact_phone' => 'nullable|string',
                'max_concurrent_orders' => 'nullable|integer|min:1|max:10',
                'status' => 'required|in:active,inactive',
                'notes' => 'nullable|string',
            ]);

            $user = Auth::user();
            
            // Use authenticated user's branch_id if user doesn't have permission to select branch
            $branchId = $user->branch_id ?: $request->branch_id;
            
            // Generate a default password if not provided
            $password = $request->password ?: 'delivery123';
            
            // Create user account
            $userData = [
                'name' => $request->name,
                'email' => $request->email ?: $request->phone . '@delivery.local',
                'phone' => $request->phone,
                'password' => Hash::make($password),
                'tenant_id' => $user->tenant_id,
                'branch_id' => $branchId,
                'role' => 'delivery_personnel',
                'is_active' => $request->status === 'active',
            ];
            
            $newUser = User::create($userData);
            
            // Create delivery personnel record with correct field mappings
            $personnelData = [
                'tenant_id' => $user->tenant_id,
                'user_id' => $newUser->id,
                'branch_id' => $branchId,
                'license_number' => $request->driving_license_number, // Map driving_license_number to license_number
                'license_expiry_date' => $request->license_expiry_date,
                'vehicle_type' => $request->vehicle_type,
                'vehicle_plate_number' => $request->vehicle_plate_number,
                'vehicle_model' => $request->vehicle_model,
                'phone_number' => $request->phone, // Use phone as phone_number
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'max_concurrent_deliveries' => $request->max_concurrent_orders ?: 3, // Map max_concurrent_orders to max_concurrent_deliveries
                'status' => $request->status,
                'is_verified' => false,
                'delivery_radius_km' => 10, // Default value
                'hourly_rate' => 0,
                'commission_rate' => 0,
                'rating' => 0,
                'total_deliveries' => 0,
                'total_earnings' => 0,
                'working_hours' => [],
            ];
            
            DeliveryPersonnel::create($personnelData);

            return redirect()->route('delivery.personnel.index')
                ->with('success', 'تم إنشاء موظف التوصيل بنجاح');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->validator)
                ->withInput()
                ->with('error', 'يرجى تصحيح الأخطاء في النموذج');
        } catch (\Exception $e) {
            \Log::error('Error creating delivery personnel: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء موظف التوصيل: ' . $e->getMessage());
        }
    }

    /**
     * Show delivery personnel details
     */
    public function show(DeliveryPersonnel $personnel)
    {
        $personnel->load(['user', 'branch', 'deliveryAssignments' => function($query) {
            $query->latest()->limit(10);
        }]);
        
        return view('delivery::personnel.show', compact('personnel'));
    }

    /**
     * Show edit delivery personnel form
     */
    public function edit(DeliveryPersonnel $personnel)
    {
        $user = Auth::user();
        $branches = Branch::where('tenant_id', $user->tenant_id)->get();
        $personnel->load('user');
        
        return view('delivery::personnel.edit', compact('personnel', 'branches'));
    }

    /**
     * Update delivery personnel
     */
    public function update(Request $request, DeliveryPersonnel $personnel)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:users,email,' . $personnel->user_id,
                'phone' => 'required|string|max:20',
                'national_id' => 'required|string|max:20',
                'date_of_birth' => 'nullable|date',
                'branch_id' => 'required|exists:branches,id',
                'driving_license_number' => 'nullable|string',
                'license_expiry_date' => 'nullable|date|after:today',
                'vehicle_type' => 'required|in:motorcycle,car,bicycle,scooter',
                'vehicle_plate_number' => 'nullable|string',
                'vehicle_model' => 'nullable|string',
                'vehicle_color' => 'nullable|string',
                'emergency_contact_name' => 'nullable|string',
                'emergency_contact_phone' => 'nullable|string',
                'max_concurrent_orders' => 'nullable|integer|min:1|max:10',
                'status' => 'required|in:active,inactive,busy',
                'notes' => 'nullable|string',
                'password' => 'nullable|string|min:6|confirmed',
            ]);

            $user = Auth::user();
            
            // Use authenticated user's branch_id if user doesn't have permission to select branch
            $branchId = $user->branch_id ?: $request->branch_id;

            // Update user account
            $userData = [
                'name' => $request->name,
                'email' => $request->email ?: $request->phone . '@delivery.local',
                'phone' => $request->phone,
                'branch_id' => $branchId,
                'is_active' => $request->status === 'active',
            ];
            
            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            }
            
            $personnel->user->update($userData);
            
            // Update delivery personnel record with correct field mappings
            $personnelData = [
                'national_id' => $request->national_id,
                'date_of_birth' => $request->date_of_birth,
                'branch_id' => $branchId,
                'license_number' => $request->driving_license_number, // Map driving_license_number to license_number
                'license_expiry_date' => $request->license_expiry_date,
                'vehicle_type' => $request->vehicle_type,
                'vehicle_plate_number' => $request->vehicle_plate_number,
                'vehicle_model' => $request->vehicle_model,
                'vehicle_color' => $request->vehicle_color,
                'phone_number' => $request->phone, // Use phone as phone_number
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'max_concurrent_deliveries' => $request->max_concurrent_orders ?: 3, // Map max_concurrent_orders to max_concurrent_deliveries
                'status' => $request->status,
                'notes' => $request->notes,
            ];
            
            $personnel->update($personnelData);

            return redirect()->route('delivery.personnel.show', $personnel->id)
                ->with('success', 'تم تحديث بيانات موظف التوصيل بنجاح');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->validator)
                ->withInput()
                ->with('error', 'يرجى تصحيح الأخطاء في النموذج');
        } catch (\Exception $e) {
            \Log::error('Error updating delivery personnel: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث بيانات موظف التوصيل: ' . $e->getMessage());
        }
    }

    /**
     * Delete delivery personnel
     */
    public function destroy(DeliveryPersonnel $personnel)
    {
        // Soft delete the user account
        $personnel->user->delete();
        
        // Delete the personnel record
        $personnel->delete();

        return redirect()->route('delivery.personnel.index')
            ->with('success', 'تم حذف موظف التوصيل بنجاح');
    }

    /**
     * Verify delivery personnel
     */
    public function verify(DeliveryPersonnel $personnel)
    {
        $personnel->update([
            'is_verified' => true,
            'verified_at' => now(),
            'status' => 'active'
        ]);

        return redirect()->back()->with('success', 'تم التحقق من موظف التوصيل بنجاح');
    }

    /**
     * Suspend delivery personnel
     */
    public function suspend(DeliveryPersonnel $personnel)
    {
        $personnel->update(['status' => 'inactive']);
        $personnel->user->update(['is_active' => false]);

        return redirect()->back()->with('success', 'تم إيقاف موظف التوصيل بنجاح');
    }

    /**
     * Activate delivery personnel
     */
    public function activate(DeliveryPersonnel $personnel)
    {
        $personnel->update(['status' => 'active']);
        $personnel->user->update(['is_active' => true]);

        return redirect()->back()->with('success', 'تم تفعيل موظف التوصيل بنجاح');
    }

    /**
     * Show personnel performance
     */
    public function performance(DeliveryPersonnel $personnel)
    {
        $personnel->load(['deliveryAssignments' => function($query) {
            $query->with('order')->latest();
        }]);
        
        return view('delivery::personnel.performance', compact('personnel'));
    }

    /**
     * Get delivery personnel data for DataTable
     */
    public function getPersonnelData(Request $request)
    {
        $user = Auth::user();
        
        $query = DeliveryPersonnel::with(['user', 'branch'])
            ->whereHas('user', function($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id);
            });
            
        // Filter by branch if specified
        if ($request->has('branch_id') && $request->branch_id) {
            $query->where('branch_id', $request->branch_id);
        } elseif ($user->branch_id) {
            $query->where('branch_id', $user->branch_id);
        }
        
        // Filter by status if specified
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }
        
        // Filter by vehicle type if specified
        if ($request->has('vehicle_type') && $request->vehicle_type) {
            $query->where('vehicle_type', $request->vehicle_type);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('name', function ($personnel) {
                return $personnel->user->name;
            })
            ->addColumn('contact', function ($personnel) {
                $contact = '';
                if ($personnel->user->phone) {
                    $contact .= '<div class="flex items-center gap-2"><i class="fa fa-phone"></i> ' . $personnel->user->phone . '</div>';
                }
                if ($personnel->user->email) {
                    $contact .= '<div class="flex items-center gap-2"><i class="fa fa-envelope"></i> ' . $personnel->user->email . '</div>';
                }
                return $contact;
            })
            ->addColumn('branch_name', function ($personnel) {
                return $personnel->branch ? $personnel->branch->name : '-';
            })
            ->addColumn('vehicle_info', function ($personnel) {
                $vehicle = '<div class="space-y-1">';
                $vehicle .= '<div class="font-medium">' . ucfirst($personnel->vehicle_type) . '</div>';
                if ($personnel->vehicle_model) {
                    $vehicle .= '<div class="text-sm text-gray-600">' . $personnel->vehicle_model . '</div>';
                }
                $vehicle .= '<div class="text-sm text-gray-600">' . $personnel->vehicle_plate_number . '</div>';
                $vehicle .= '</div>';
                return $vehicle;
            })
            ->addColumn('status_badge', function ($personnel) {
                $badges = [
                    'active' => '<span class="px-2 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800">نشط</span>',
                    'inactive' => '<span class="px-2 py-1 text-sm font-medium rounded-full bg-gray-100 text-gray-800">غير نشط</span>',
                    'on_delivery' => '<span class="px-2 py-1 text-sm font-medium rounded-full bg-yellow-100 text-yellow-800">في التوصيل</span>',
                    'break' => '<span class="px-2 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800">استراحة</span>'
                ];
                return $badges[$personnel->status] ?? $personnel->status;
            })
            ->addColumn('verification_status', function ($personnel) {
                if ($personnel->is_verified) {
                    return '<span class="inline-flex items-center px-2 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800"><i class="fa fa-check mr-1"></i> محقق</span>';
                } else {
                    return '<span class="inline-flex items-center px-2 py-1 text-sm font-medium rounded-full bg-yellow-100 text-yellow-800"><i class="fa fa-clock mr-1"></i> في الانتظار</span>';
                }
            })
            ->addColumn('rating', function ($personnel) {
                $rating = number_format($personnel->rating, 1);
                $stars = '<div class="flex flex-col items-center">';
                $stars .= '<div class="flex gap-1">';
                for ($i = 1; $i <= 5; $i++) {
                    if ($i <= $personnel->rating) {
                        $stars .= '<i class="fa fa-star text-yellow-400"></i>';
                    } else {
                        $stars .= '<i class="fa fa-star text-gray-300"></i>';
                    }
                }
                $stars .= '</div>';
                $stars .= '<div class="text-sm text-gray-600 mt-1">' . $rating . '/5</div>';
                $stars .= '</div>';
                return $stars;
            })
            ->addColumn('deliveries_count', function ($personnel) {
                return '<span class="px-2 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800">' . $personnel->total_deliveries . '</span>';
            })
            ->addColumn('action', function ($personnel) {
                $actions = '<div class="flex items-center gap-1">';
                $actions .= '<a href="' . route('delivery.personnel.show', $personnel) . '" class="p-1 text-white bg-blue-500 rounded hover:bg-blue-600" title="عرض"><i class="fa fa-eye"></i></a>';
                $actions .= '<a href="' . route('delivery.personnel.edit', $personnel) . '" class="p-1 text-white bg-indigo-500 rounded hover:bg-indigo-600" title="تعديل"><i class="fa fa-edit"></i></a>';
                $actions .= '<a href="' . route('delivery.personnel.performance', $personnel) . '" class="p-1 text-white bg-green-500 rounded hover:bg-green-600" title="الأداء"><i class="fa fa-chart-line"></i></a>';
                
                if (!$personnel->is_verified) {
                    $actions .= '<button type="button" class="p-1 text-white bg-yellow-500 rounded hover:bg-yellow-600 verify-personnel" data-id="' . $personnel->id . '" title="تحقق"><i class="fa fa-check"></i></button>';
                }
                
                if ($personnel->status === 'active') {
                    $actions .= '<button type="button" class="p-1 text-white bg-gray-500 rounded hover:bg-gray-600 suspend-personnel" data-id="' . $personnel->id . '" title="إيقاف"><i class="fa fa-pause"></i></button>';
                } else {
                    $actions .= '<button type="button" class="p-1 text-white bg-green-500 rounded hover:bg-green-600 activate-personnel" data-id="' . $personnel->id . '" title="تفعيل"><i class="fa fa-play"></i></button>';
                }
                
                $actions .= '<button type="button" class="p-1 text-white bg-red-500 rounded hover:bg-red-600 delete-personnel" data-id="' . $personnel->id . '" title="حذف"><i class="fa fa-trash"></i></button>';
                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['contact', 'vehicle_info', 'status_badge', 'verification_status', 'rating', 'deliveries_count', 'action'])
            ->make(true);
    }
}
