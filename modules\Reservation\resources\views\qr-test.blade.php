<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Testing - Reservation Module</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4f46e5',
                        secondary: '#6366f1'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <i class="fas fa-qrcode text-purple-500 text-2xl ml-3"></i>
                        <h1 class="text-2xl font-bold text-gray-900">QR Code Testing</h1>
                    </div>
                    <a href="{{ route('reservation.dashboard') }}" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            
            <!-- QR Code Generator -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">
                    <i class="fas fa-magic text-purple-500 ml-2"></i>
                    مولد رمز QR
                </h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Generator Form -->
                    <div>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">نوع الرمز</label>
                                <select id="qrType" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="table">رمز طاولة</option>
                                    <option value="menu">رمز قائمة الطعام</option>
                                    <option value="custom">رمز مخصص</option>
                                </select>
                            </div>
                            
                            <div id="tableSection">
                                <label class="block text-sm font-medium text-gray-700 mb-2">رقم الطاولة</label>
                                <input type="number" id="tableNumber" placeholder="أدخل رقم الطاولة" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            
                            <div id="customSection" style="display: none;">
                                <label class="block text-sm font-medium text-gray-700 mb-2">النص المخصص</label>
                                <input type="text" id="customText" placeholder="أدخل النص المخصص" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حجم الرمز</label>
                                <select id="qrSize" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="200">صغير (200x200)</option>
                                    <option value="300" selected>متوسط (300x300)</option>
                                    <option value="400">كبير (400x400)</option>
                                    <option value="500">كبير جداً (500x500)</option>
                                </select>
                            </div>
                            
                            <button onclick="generateQR()" class="w-full bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors font-medium">
                                <i class="fas fa-qrcode ml-2"></i>
                                إنشاء رمز QR
                            </button>
                        </div>
                    </div>
                    
                    <!-- Generated QR Code -->
                    <div class="text-center">
                        <div id="qrCodeContainer" class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 min-h-[300px] flex items-center justify-center">
                            <div class="text-gray-500">
                                <i class="fas fa-qrcode text-4xl mb-4"></i>
                                <p>سيظهر رمز QR هنا</p>
                            </div>
                        </div>
                        
                        <div id="qrActions" class="mt-4 space-y-2" style="display: none;">
                            <button onclick="downloadQR()" class="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors">
                                <i class="fas fa-download ml-2"></i>
                                تحميل الرمز
                            </button>
                            <button onclick="printQR()" class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-print ml-2"></i>
                                طباعة الرمز
                            </button>
                        </div>
                        
                        <div id="qrInfo" class="mt-4 text-sm text-gray-600" style="display: none;">
                            <p><strong>الرابط:</strong> <span id="qrUrl"></span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Validator -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">
                    <i class="fas fa-check-circle text-green-500 ml-2"></i>
                    فحص رمز QR
                </h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Validator Form -->
                    <div>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">رمز QR للفحص</label>
                                <input type="text" id="qrCodeToValidate" placeholder="أدخل رمز QR أو الرابط" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            
                            <button onclick="validateQR()" class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition-colors font-medium">
                                <i class="fas fa-search ml-2"></i>
                                فحص الرمز
                            </button>
                            
                            <!-- Quick Test Buttons -->
                            <div class="border-t pt-4">
                                <h3 class="text-sm font-medium text-gray-700 mb-3">اختبارات سريعة:</h3>
                                <div class="space-y-2">
                                    <button onclick="quickValidate('TBL_1_' + Date.now())" class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                        اختبار رمز طاولة صحيح
                                    </button>
                                    <button onclick="quickValidate('INVALID_CODE')" class="w-full bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition-colors text-sm">
                                        اختبار رمز غير صحيح
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Validation Result -->
                    <div>
                        <div id="validationResult" class="bg-gray-50 border border-gray-200 rounded-lg p-4 min-h-[200px]">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-search text-2xl mb-2"></i>
                                <p>نتيجة الفحص ستظهر هنا</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Scanner Simulation -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">
                    <i class="fas fa-camera text-blue-500 ml-2"></i>
                    محاكي ماسح QR
                </h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Sample QR Codes -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">رموز QR تجريبية</h3>
                        <div class="space-y-4">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="text-center">
                                    <canvas id="sampleQR1" class="mx-auto mb-2"></canvas>
                                    <p class="text-sm text-gray-600">طاولة رقم 1</p>
                                    <button onclick="scanSample('TBL_1_123456')" class="mt-2 bg-blue-500 text-white px-4 py-1 rounded text-sm hover:bg-blue-600">
                                        مسح الرمز
                                    </button>
                                </div>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="text-center">
                                    <canvas id="sampleQR2" class="mx-auto mb-2"></canvas>
                                    <p class="text-sm text-gray-600">طاولة رقم 5</p>
                                    <button onclick="scanSample('TBL_5_789012')" class="mt-2 bg-blue-500 text-white px-4 py-1 rounded text-sm hover:bg-blue-600">
                                        مسح الرمز
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Scanner Interface -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">واجهة الماسح</h3>
                        <div class="border-2 border-dashed border-blue-300 rounded-lg p-8 text-center bg-blue-50">
                            <i class="fas fa-camera text-blue-500 text-4xl mb-4"></i>
                            <p class="text-blue-700 mb-4">ضع رمز QR هنا للمسح</p>
                            <div id="scannerStatus" class="text-sm text-blue-600">
                                جاهز للمسح
                            </div>
                        </div>
                    </div>
                    
                    <!-- Scan Result -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">نتيجة المسح</h3>
                        <div id="scanResult" class="bg-gray-50 border border-gray-200 rounded-lg p-4 min-h-[200px]">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-qrcode text-2xl mb-2"></i>
                                <p>نتيجة المسح ستظهر هنا</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set up axios defaults
        axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        let currentQRCanvas = null;

        // Handle QR type change
        document.getElementById('qrType').addEventListener('change', function() {
            const type = this.value;
            const tableSection = document.getElementById('tableSection');
            const customSection = document.getElementById('customSection');
            
            if (type === 'custom') {
                tableSection.style.display = 'none';
                customSection.style.display = 'block';
            } else {
                tableSection.style.display = 'block';
                customSection.style.display = 'none';
            }
        });

        function generateQR() {
            const type = document.getElementById('qrType').value;
            const tableNumber = document.getElementById('tableNumber').value;
            const customText = document.getElementById('customText').value;
            const size = parseInt(document.getElementById('qrSize').value);
            
            let qrText = '';
            let url = '';
            
            if (type === 'table') {
                if (!tableNumber) {
                    alert('يرجى إدخال رقم الطاولة');
                    return;
                }
                qrText = `TBL_${tableNumber}_${Date.now()}`;
                url = `{{ url('/restaurant/table') }}/${qrText}`;
            } else if (type === 'menu') {
                if (!tableNumber) {
                    alert('يرجى إدخال رقم الطاولة');
                    return;
                }
                qrText = `TBL_${tableNumber}_${Date.now()}`;
                url = `{{ url('/restaurant/table') }}/${qrText}/menu`;
            } else if (type === 'custom') {
                if (!customText) {
                    alert('يرجى إدخال النص المخصص');
                    return;
                }
                qrText = customText;
                url = customText;
            }
            
            // Clear previous QR code
            const container = document.getElementById('qrCodeContainer');
            container.innerHTML = '';
            
            // Create canvas
            const canvas = document.createElement('canvas');
            container.appendChild(canvas);
            currentQRCanvas = canvas;
            
            // Generate QR code using QRious
            try {
                const qr = new QRious({
                    element: canvas,
                    value: url,
                    size: size,
                    background: '#FFFFFF',
                    foreground: '#000000'
                });
                
                // Show actions and info
                document.getElementById('qrActions').style.display = 'block';
                document.getElementById('qrInfo').style.display = 'block';
                document.getElementById('qrUrl').textContent = url;
            } catch (error) {
                console.error(error);
                alert('خطأ في إنشاء رمز QR');
            }
        }

        function downloadQR() {
            if (currentQRCanvas) {
                const link = document.createElement('a');
                link.download = 'qr-code.png';
                link.href = currentQRCanvas.toDataURL();
                link.click();
            }
        }

        function printQR() {
            if (currentQRCanvas) {
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head><title>Print QR Code</title></head>
                        <body style="text-align: center; padding: 20px;">
                            <h2>QR Code</h2>
                            <img src="${currentQRCanvas.toDataURL()}" style="max-width: 100%;">
                        </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.print();
            }
        }

        function validateQR() {
            const qrCode = document.getElementById('qrCodeToValidate').value.trim();
            if (!qrCode) {
                alert('يرجى إدخال رمز QR');
                return;
            }
            
            // Extract QR code from URL if needed
            let codeToValidate = qrCode;
            if (qrCode.includes('/table/')) {
                codeToValidate = qrCode.split('/table/')[1].split('/')[0];
            }
            
            // Show loading
            const resultDiv = document.getElementById('validationResult');
            resultDiv.innerHTML = `
                <div class="text-center text-blue-600">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>جاري فحص الرمز...</p>
                </div>
            `;
            
            // Simulate API call
            axios.post('/api/reservation/qr-codes/validate', {
                qr_code: codeToValidate
            })
            .then(response => {
                const data = response.data;
                resultDiv.innerHTML = `
                    <div class="text-center text-green-600">
                        <i class="fas fa-check-circle text-2xl mb-2"></i>
                        <p class="font-medium mb-4">رمز QR صحيح!</p>
                        <div class="text-right space-y-2">
                            <p><strong>رقم الطاولة:</strong> ${data.data.table_number}</p>
                            <p><strong>المنطقة:</strong> ${data.data.area || 'غير محدد'}</p>
                            <p><strong>الفرع:</strong> ${data.data.branch || 'غير محدد'}</p>
                        </div>
                    </div>
                `;
            })
            .catch(error => {
                const message = error.response?.data?.message || 'رمز QR غير صحيح';
                resultDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <i class="fas fa-times-circle text-2xl mb-2"></i>
                        <p class="font-medium">${message}</p>
                    </div>
                `;
            });
        }

        function quickValidate(code) {
            document.getElementById('qrCodeToValidate').value = code;
            validateQR();
        }

        function scanSample(code) {
            // Update scanner status
            document.getElementById('scannerStatus').textContent = 'جاري المسح...';
            
            // Simulate scanning delay
            setTimeout(() => {
                document.getElementById('scannerStatus').textContent = 'تم المسح بنجاح';
                
                // Show scan result
                const resultDiv = document.getElementById('scanResult');
                resultDiv.innerHTML = `
                    <div class="text-center text-green-600">
                        <i class="fas fa-check-circle text-2xl mb-2"></i>
                        <p class="font-medium mb-4">تم مسح الرمز بنجاح!</p>
                        <div class="text-right space-y-2">
                            <p><strong>رمز QR:</strong> ${code}</p>
                            <p><strong>النوع:</strong> رمز طاولة</p>
                            <p><strong>الحالة:</strong> صالح</p>
                        </div>
                        <button onclick="validateScannedCode('${code}')" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                            التحقق من الرمز
                        </button>
                    </div>
                `;
                
                // Reset scanner status after delay
                setTimeout(() => {
                    document.getElementById('scannerStatus').textContent = 'جاهز للمسح';
                }, 2000);
            }, 1000);
        }

        function validateScannedCode(code) {
            document.getElementById('qrCodeToValidate').value = code;
            validateQR();
        }

        // Generate sample QR codes on page load
        window.addEventListener('load', function() {
            // Sample QR 1
            try {
                new QRious({
                    element: document.getElementById('sampleQR1'),
                    value: '{{ url("/restaurant/table") }}/TBL_1_123456',
                    size: 100,
                    background: '#FFFFFF',
                    foreground: '#000000'
                });
            } catch (error) {
                console.error('Error generating sample QR 1:', error);
            }
            
            // Sample QR 2
            try {
                new QRious({
                    element: document.getElementById('sampleQR2'),
                    value: '{{ url("/restaurant/table") }}/TBL_5_789012',
                    size: 100,
                    background: '#FFFFFF',
                    foreground: '#000000'
                });
            } catch (error) {
                console.error('Error generating sample QR 2:', error);
            }
        });
    </script>
</body>
</html>