<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\Branch;
use App\Models\Menu;
use App\Models\MenuCategory;
use App\Models\MenuItem;
use App\Models\Table;
use App\Models\Area;
use App\Models\WaiterRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PublicRestaurantController extends Controller
{
    /**
     * Get tenant and branch by tenant name and optional branch ID
     */
    private function getTenantAndBranch(string $tenantName, ?int $branchId = null)
    {
        $tenant = Tenant::where('name', $tenantName)
            ->orWhere('code', $tenantName)
            ->where('status', 'active')
            ->first();

        if (!$tenant) {
            return [null, null];
        }

        if ($branchId) {
            // Get specific branch if branch ID is provided
            $branch = $tenant->branches()
                ->where('id', $branchId)
                ->where('status', 'active')
                ->first();
        } else {
            // Get first active branch if no branch ID is provided
            $branch = $tenant->branches()->where('status', 'active')->first();
        }

        return [$tenant, $branch];
    }

    /**
     * Display the public restaurant page
     */
    public function index(Request $request, string $tenantName)
    {
        $branchId = $request->input('branch_id');
        [$tenant, $branch] = $this->getTenantAndBranch($tenantName, $branchId);
        if (!$tenant || !$branch) {
            abort(404, 'Restaurant not found or inactive');
        }

        return $this->getRestaurantData($tenant, $branch, $tenantName);
    }

    /**
     * Show restaurant page by tenant name (simplified route)
     */
    public function showByTenantName(Request $request, string $tenantName)
    {
        // Get branch_id from request if provided
        $branchId = $request->input('branch_id');
        
        // Get tenant and branch (first active branch if no specific branch provided)
        [$tenant, $branch] = $this->getTenantAndBranch($tenantName, $branchId);
        
        if (!$tenant || !$branch) {
            abort(404, 'Restaurant not found or inactive');
        }
      
        return $this->getRestaurantData($tenant, $branch, $tenantName);
    }

    /**
     * Get restaurant data for display
     */
    private function getRestaurantData($tenant, $branch, $tenantName)
    {
        // Get restaurant data
        $restaurant = [
            'name' => $tenant->name,
            'code' => $tenant->code,
            'branch' => $branch->name,
            'branch_id' => $branch->id,
            'business_hours' => $tenant->business_hours,
            'contact_phone' => $tenant->contact_phone,
            'business_address' => $tenant->business_address,
        ];
         
        // Get all active branches for this tenant
        $branches = $tenant->branches()
            ->where('status', 'active')
            ->orderBy('name')
            ->get(['id', 'name', 'code']);
            
        // Get areas for this branch (simple query)
        $areas = Area::where('branch_id', $branch->id)
            ->orderBy('name')
            ->get();

        // Get tables for this branch (simple query)
        $tables = Table::where('branch_id', $branch->id)
            ->with('area')
            ->orderBy('table_number')
            ->get();

        // Get menu categories for this branch (simple query)
        $categories = MenuCategory::where('branch_id', $branch->id)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Get menu items for this branch (simple query)
        $menuItems = MenuItem::where('branch_id', $branch->id)
            ->with(['category', 'branches'])
            ->get();

        return view('public.restaurant', compact(
            'restaurant',
            'branches',
            'areas',
            'tables',
            'categories',
            'menuItems',
            'tenantName'
        ));
    }

    /**
     * Get menu data for the restaurant
     */
    public function getMenuData(Request $request, string $tenantName): JsonResponse
    {
        $branchId = $request->input('branch_id');
        [$tenant, $branch] = $this->getTenantAndBranch($tenantName, $branchId);

        if (!$tenant || !$branch) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        // Get active menus with categories and items
        $menus = Menu::with([
            'categories' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'categories.menuItems' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'categories.menuItems.variants' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'categories.menuItems.addons' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }
        ])
        ->where('tenant_id', $tenant->id)
        ->where('branch_id', $branch->id)
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'tenant' => [
                    'id' => $tenant->id,
                    'name' => $tenant->name,
                    'code' => $tenant->code,
                ],
                'branch' => [
                    'id' => $branch->id,
                    'name' => $branch->name,
                ],
                'menus' => $menus
            ]
        ]);
    }

    /**
     * Get tables for waiter request
     */
    public function getTables(Request $request, string $tenantName): JsonResponse
    {
        $branchId = $request->input('branch_id');
        [$tenant, $branch] = $this->getTenantAndBranch($tenantName, $branchId);

        if (!$tenant || !$branch) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        $areas = Area::with(['tables' => function($query) {
            $query->where('status', 'available')->orderBy('table_number');
        }])
        ->where('branch_id', $branch->id)
        ->where('is_active', true)
        ->orderBy('name')
        ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'tenant' => [
                    'id' => $tenant->id,
                    'name' => $tenant->name,
                    'code' => $tenant->code,
                ],
                'branch' => [
                    'id' => $branch->id,
                    'name' => $branch->name,
                ],
                'areas' => $areas
            ]
        ]);
    }

    /**
     * Request a waiter
     */
    public function requestWaiter(Request $request, string $tenantName): JsonResponse
    {
        $branchId = $request->input('branch_id');
        [$tenant, $branch] = $this->getTenantAndBranch($tenantName, $branchId);

        if (!$tenant || !$branch) {
            \Log::error('Tenant or branch not found', [
                'tenantName' => $tenantName,
                'branchId' => $branchId,
                'tenant' => $tenant ? $tenant->id : null,
                'branch' => $branch ? $branch->id : null
            ]);
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'request_type' => 'required|in:service,bill,assistance,order',
            'notes' => 'nullable|string|max:500',
            'branch_id' => 'nullable|integer|exists:branches,id'
        ]);

        // Debug: Check all tables for this branch
        $allTables = Table::where('branch_id', $branch->id)->get(['id', 'table_number', 'branch_id', 'area_id']);
        
        // Verify table belongs to this branch
        $table = Table::where('id', $request->table_id)
            ->where('branch_id', $branch->id)
            ->first();

        \Log::info('Table lookup for waiter request', [
            'table_id' => $request->table_id,
            'branch_id' => $branch->id,
            'all_tables_for_branch' => $allTables->toArray(),
            'table_found' => $table ? true : false,
            'table_data' => $table ? $table->toArray() : null
        ]);

        if (!$table) {
            return response()->json(['error' => 'Table not found'], 404);
        }

        // Create waiter request
        $waiterRequest = WaiterRequest::create([
            'tenant_id' => $tenant->id,
            'table_id' => $request->table_id,
            'branch_id' => $branch->id,
            'request_type' => $request->request_type ?? 'service',
            'notes' => $request->notes,
            'status' => 'pending'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Waiter request submitted successfully',
            'data' => $waiterRequest->load('table.area')
        ]);
    }

    /**
     * Submit an order
     */
    public function submitOrder(Request $request, string $tenantName): JsonResponse
    {
        try {
            $branchId = $request->input('branch_id');
            [$tenant, $branch] = $this->getTenantAndBranch($tenantName, $branchId);

            if (!$tenant || !$branch) {
                return response()->json(['error' => 'Restaurant not found'], 404);
            }

            // Validate the request
            $validatedData = $request->validate([
                'table_id' => 'required|exists:tables,id',
                'order_type' => 'required|in:dine_in,takeaway,delivery',
                'status' => 'string|in:pending,confirmed,preparing,ready,served,cancelled',
                'items' => 'required|array|min:1',
                'items.*.menu_item_id' => 'required|exists:menu_items,id',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.unit_price' => 'required|numeric|min:0',
                'items.*.special_instructions' => 'nullable|string|max:500',
                'customer_name' => 'nullable|string|max:255',
                'customer_phone' => 'nullable|string|max:20',
                'special_instructions' => 'nullable|string|max:1000',
                'branch_id' => 'nullable|integer|exists:branches,id'
            ]);

            // Debug: Check all tables for this branch
            $allTables = Table::where('branch_id', $branch->id)->get(['id', 'table_number', 'branch_id', 'area_id']);
            
            // Verify the table belongs to this branch
        $table = Table::where('id', $request->table_id)
            ->where('branch_id', $branch->id)
            ->first();

        \Log::info('Table lookup for order submission', [
            'table_id' => $request->table_id,
            'branch_id' => $branch->id,
            'all_tables_for_branch' => $allTables->toArray(),
            'table_found' => $table ? true : false,
            'table_data' => $table ? $table->toArray() : null
        ]);
                
            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid table for this branch'
                ], 400);
            }

            // Use the OrderService to create the order
            $orderService = app(\Modules\Orders\Services\OrderService::class);
            
            // Prepare order data
            $orderData = [
                'branch_id' => $branch->id,
                'table_id' => $validatedData['table_id'],
                'order_type' => $validatedData['order_type'] ?? 'dine_in',
                'status' => $validatedData['status'] ?? 'pending',
                'items' => $validatedData['items'],
                'notes' => $validatedData['special_instructions'] ?? 'Order submitted from public menu',
                'customer_info' => [
                    'name' => $validatedData['customer_name'] ?? null,
                    'phone' => $validatedData['customer_phone'] ?? null,
                ],
            ];

            // Create the order
            $order = $orderService->createOrder($orderData);

            return response()->json([
                'success' => true,
                'message' => 'Order submitted successfully',
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'data' => [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'status' => $order->status,
                    'total_amount' => $order->total_amount,
                    'table' => [
                        'id' => $order->table->id,
                        'table_number' => $order->table->table_number,
                    ],
                ],
                'tenant' => [
                    'id' => $tenant->id,
                    'name' => $tenant->name,
                    'code' => $tenant->code,
                ],
                'branch' => [
                    'id' => $branch->id,
                    'name' => $branch->name,
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error creating order from public menu: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show restaurant page by table QR code
     * URL pattern: /restaurant/table/{table_qrcode_hash}?hash={tenantname}
     */
    public function showByTableQRCode(Request $request, string $qrcode)
    {
        $tenantName = $request->query('hash');
        
        // Find the table by QR code
        $table = Table::where('qr_code', $qrcode)
            ->with(['area.branch.tenant'])
            ->first();

        if (!$table) {
            abort(404, 'Table not found');
        }

        $tenant = $table->area->branch->tenant;
        $branch = $table->area->branch;

        // Check if tenant and branch are active
        if ($tenant->status !== 'active' || $branch->status !== 'active') {
            abort(404, 'Restaurant not available');
        }

        // Get restaurant data
        $restaurant = [
            'name' => $tenant->name,
            'code' => $tenant->code,
            'branch' => $branch->name,
            'branch_id' => $branch->id,
            'business_hours' => $tenant->business_hours,
            'contact_phone' => $tenant->contact_phone,
            'business_address' => $tenant->business_address,
            'current_table' => [
                'id' => $table->id,
                'table_number' => $table->table_number,
                'table_name' => $table->table_name,
                'area_name' => $table->area->name,
                'qr_code' => $table->qr_code
            ]
        ];
         
        // Get all active branches for this tenant
        $branches = $tenant->branches()
            ->where('status', 'active')
            ->orderBy('name')
            ->get(['id', 'name', 'code']);
            
        // Get areas and tables for waiter requests
        $areas = Area::with(['tables' => function($query) {
            $query->where('status', 'available')->orderBy('table_number');
        }])
        ->where('branch_id', $branch->id)
        ->orderBy('name')
        ->get();

        // Get all tables for this branch
        $tables = Table::whereHas('area', function($query) use ($branch) {
            $query->where('branch_id', $branch->id);
        })
        ->with('area')
        ->orderBy('table_number')
        ->get();

        // Get menu categories that have menu items available in this branch
        $categories = MenuCategory::whereHas('menuItems', function ($query) use ($branch) {
            $query->where('is_active', true)
                  ->whereHas('branches', function ($branchQuery) use ($branch) {
                      $branchQuery->where('branch_id', $branch->id);
                  });
        })
        ->with(['menuItems' => function ($query) use ($branch) {
            $query->where('is_active', true)
                  ->whereHas('branches', function ($branchQuery) use ($branch) {
                      $branchQuery->where('branch_id', $branch->id);
                  })
                  ->with(['variants', 'addons'])
                  ->orderBy('sort_order');
        }])
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->get();

        // Get all menu items for this branch
        $menuItems = MenuItem::where('is_active', true)
            ->whereHas('branches', function ($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })
            ->with(['category', 'variants', 'addons'])
            ->orderBy('sort_order')
            ->get();

        return view('public.restaurant', compact('restaurant', 'branches', 'areas', 'tables', 'tenantName', 'categories', 'menuItems'));
    }

    /**
     * Legacy method for backward compatibility
     * Show restaurant page by QR code (old pattern)
     */
    public function showByQRCode(Request $request, string $tenantSlug, string $hash)
    {
        // This method can be kept for backward compatibility
        // or redirect to the new pattern
        return redirect()->route('public.restaurant.table.qr', [
            'qrcode' => $hash,
            'hash' => $tenantSlug
        ]);
    }

    /**
     * Show restaurant page by tenant name
     */

}