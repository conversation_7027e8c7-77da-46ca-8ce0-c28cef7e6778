<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Tester - Reservation Module</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4f46e5',
                        secondary: '#6366f1'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <i class="fas fa-flask text-green-500 text-2xl ml-3"></i>
                        <h1 class="text-2xl font-bold text-gray-900">API Tester</h1>
                    </div>
                    <div class="flex space-x-4">
                        <a href="{{ route('reservation.api-documentation') }}" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            <i class="fas fa-book ml-2"></i>
                            API Documentation
                        </a>
                        <a href="{{ route('reservation.dashboard') }}" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Request Panel -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">
                        <i class="fas fa-paper-plane text-blue-500 ml-2"></i>
                        إرسال طلب API
                    </h2>

                    <!-- Method Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">HTTP Method</label>
                        <select id="method" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                        </select>
                    </div>

                    <!-- Endpoint Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Endpoint</label>
                        <select id="endpoint" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                            <optgroup label="Reservations">
                                <option value="/api/reservation/reservations">GET /reservations</option>
                                <option value="/api/reservation/reservations" data-method="POST">POST /reservations</option>
                                <option value="/api/reservation/reservations/1">GET /reservations/{id}</option>
                                <option value="/api/reservation/reservations/1" data-method="PUT">PUT /reservations/{id}</option>
                                <option value="/api/reservation/reservations/1" data-method="DELETE">DELETE /reservations/{id}</option>
                                <option value="/api/reservation/reservations/1/confirm" data-method="POST">POST /reservations/{id}/confirm</option>
                                <option value="/api/reservation/reservations/check-availability" data-method="POST">POST /reservations/check-availability</option>
                                <option value="/api/reservation/reservations/statistics/overview">GET /reservations/statistics</option>
                            </optgroup>
                            <optgroup label="Waiter Requests">
                                <option value="/api/reservation/waiter-requests">GET /waiter-requests</option>
                                <option value="/api/reservation/waiter-requests" data-method="POST">POST /waiter-requests</option>
                                <option value="/api/reservation/waiter-requests/1">GET /waiter-requests/{id}</option>
                                <option value="/api/reservation/waiter-requests/1" data-method="PUT">PUT /waiter-requests/{id}</option>
                                <option value="/api/reservation/waiter-requests/1/complete" data-method="POST">POST /waiter-requests/{id}/complete</option>
                                <option value="/api/reservation/waiter-requests/table/1">GET /waiter-requests/table/{id}</option>
                            </optgroup>
                            <optgroup label="Areas">
                                <option value="/api/reservation/areas">GET /areas</option>
                                <option value="/api/reservation/areas" data-method="POST">POST /areas</option>
                                <option value="/api/reservation/areas/1">GET /areas/{id}</option>
                                <option value="/api/reservation/areas/1" data-method="PUT">PUT /areas/{id}</option>
                                <option value="/api/reservation/areas/1" data-method="DELETE">DELETE /areas/{id}</option>
                            </optgroup>
                            <optgroup label="Tables">
                                <option value="/api/reservation/tables">GET /tables</option>
                                <option value="/api/reservation/tables" data-method="POST">POST /tables</option>
                                <option value="/api/reservation/tables/1">GET /tables/{id}</option>
                                <option value="/api/reservation/tables/1" data-method="PUT">PUT /tables/{id}</option>
                                <option value="/api/reservation/tables/area/1">GET /tables/area/{id}</option>
                            </optgroup>
                            <optgroup label="QR Codes">
                                <option value="/api/reservation/qr-codes/validate" data-method="POST">POST /qr-codes/validate</option>
                                <option value="/api/reservation/qr-codes/table/1/content">GET /qr-codes/table/{id}/content</option>
                                <option value="/api/reservation/qr-codes/table/1/generate" data-method="POST">POST /qr-codes/table/{id}/generate</option>
                            </optgroup>
                        </select>
                    </div>

                    <!-- Custom URL -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Custom URL (اختياري)</label>
                        <input type="text" id="customUrl" placeholder="أو أدخل URL مخصص" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>

                    <!-- Headers -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Headers</label>
                        <textarea id="headers" rows="4" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder='{"Authorization": "Bearer your-token", "Accept": "application/json"}'></textarea>
                    </div>

                    <!-- Request Body -->
                    <div class="mb-6" id="bodySection">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Request Body (JSON)</label>
                        <textarea id="requestBody" rows="6" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder='{"key": "value"}'></textarea>
                    </div>

                    <!-- Send Button -->
                    <button onclick="sendRequest()" class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition-colors font-medium">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال الطلب
                    </button>

                    <!-- Quick Test Buttons -->
                    <div class="mt-6 border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">اختبارات سريعة</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="quickTest('reservations')" class="bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                <i class="fas fa-calendar ml-1"></i>
                                الحجوزات
                            </button>
                            <button onclick="quickTest('waiter-requests')" class="bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors text-sm">
                                <i class="fas fa-bell ml-1"></i>
                                طلبات النادل
                            </button>
                            <button onclick="quickTest('areas')" class="bg-teal-500 text-white py-2 px-4 rounded-lg hover:bg-teal-600 transition-colors text-sm">
                                <i class="fas fa-map ml-1"></i>
                                المناطق
                            </button>
                            <button onclick="quickTest('tables')" class="bg-indigo-500 text-white py-2 px-4 rounded-lg hover:bg-indigo-600 transition-colors text-sm">
                                <i class="fas fa-table ml-1"></i>
                                الطاولات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Panel -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">
                        <i class="fas fa-code text-green-500 ml-2"></i>
                        استجابة API
                    </h2>

                    <!-- Status -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Status:</span>
                            <span id="responseStatus" class="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                جاهز للإرسال
                            </span>
                        </div>
                    </div>

                    <!-- Response Time -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Response Time:</span>
                            <span id="responseTime" class="text-sm text-gray-600">--</span>
                        </div>
                    </div>

                    <!-- Response Headers -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Response Headers</label>
                        <div id="responseHeaders" class="bg-gray-50 border border-gray-200 rounded-lg p-3 text-sm text-gray-600 min-h-[100px] overflow-auto">
                            سيتم عرض headers الاستجابة هنا...
                        </div>
                    </div>

                    <!-- Response Body -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Response Body</label>
                        <div id="responseBody" class="bg-gray-50 border border-gray-200 rounded-lg p-3 text-sm text-gray-600 min-h-[300px] overflow-auto font-mono">
                            سيتم عرض محتوى الاستجابة هنا...
                        </div>
                    </div>

                    <!-- Copy Response Button -->
                    <button onclick="copyResponse()" class="w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-copy ml-2"></i>
                        نسخ الاستجابة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set up axios defaults
        axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        let lastResponse = null;
        let startTime = null;

        // Update method when endpoint changes
        document.getElementById('endpoint').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const method = selectedOption.getAttribute('data-method') || 'GET';
            document.getElementById('method').value = method;
            
            // Show/hide body section based on method
            const bodySection = document.getElementById('bodySection');
            if (method === 'GET' || method === 'DELETE') {
                bodySection.style.display = 'none';
            } else {
                bodySection.style.display = 'block';
            }
        });

        // Update body section visibility when method changes
        document.getElementById('method').addEventListener('change', function() {
            const method = this.value;
            const bodySection = document.getElementById('bodySection');
            if (method === 'GET' || method === 'DELETE') {
                bodySection.style.display = 'none';
            } else {
                bodySection.style.display = 'block';
            }
        });

        function sendRequest() {
            const method = document.getElementById('method').value;
            const endpoint = document.getElementById('endpoint').value;
            const customUrl = document.getElementById('customUrl').value;
            const headersText = document.getElementById('headers').value;
            const bodyText = document.getElementById('requestBody').value;

            // Determine URL
            const url = customUrl || `{{ url('') }}${endpoint}`;

            // Parse headers
            let headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            };
            
            if (headersText.trim()) {
                try {
                    const customHeaders = JSON.parse(headersText);
                    headers = { ...headers, ...customHeaders };
                } catch (e) {
                    alert('خطأ في تنسيق Headers JSON');
                    return;
                }
            }

            // Parse body
            let data = null;
            if (bodyText.trim() && (method === 'POST' || method === 'PUT')) {
                try {
                    data = JSON.parse(bodyText);
                } catch (e) {
                    alert('خطأ في تنسيق Request Body JSON');
                    return;
                }
            }

            // Update status
            updateStatus('جاري الإرسال...', 'bg-yellow-100 text-yellow-800');
            
            // Record start time
            startTime = Date.now();

            // Send request
            axios({
                method: method.toLowerCase(),
                url: url,
                headers: headers,
                data: data
            })
            .then(response => {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                lastResponse = response;
                updateStatus(`${response.status} ${response.statusText}`, 'bg-green-100 text-green-800');
                updateResponseTime(responseTime);
                updateResponseHeaders(response.headers);
                updateResponseBody(response.data);
            })
            .catch(error => {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                lastResponse = error.response;
                const status = error.response ? `${error.response.status} ${error.response.statusText}` : 'Network Error';
                updateStatus(status, 'bg-red-100 text-red-800');
                updateResponseTime(responseTime);
                
                if (error.response) {
                    updateResponseHeaders(error.response.headers);
                    updateResponseBody(error.response.data);
                } else {
                    updateResponseHeaders({});
                    updateResponseBody({ error: error.message });
                }
            });
        }

        function updateStatus(status, className) {
            const statusElement = document.getElementById('responseStatus');
            statusElement.textContent = status;
            statusElement.className = `px-3 py-1 rounded-full text-sm font-medium ${className}`;
        }

        function updateResponseTime(time) {
            document.getElementById('responseTime').textContent = `${time}ms`;
        }

        function updateResponseHeaders(headers) {
            const headersElement = document.getElementById('responseHeaders');
            headersElement.textContent = JSON.stringify(headers, null, 2);
        }

        function updateResponseBody(data) {
            const bodyElement = document.getElementById('responseBody');
            bodyElement.textContent = JSON.stringify(data, null, 2);
        }

        function copyResponse() {
            if (lastResponse) {
                const responseText = JSON.stringify(lastResponse.data, null, 2);
                navigator.clipboard.writeText(responseText).then(() => {
                    alert('تم نسخ الاستجابة!');
                });
            }
        }

        function quickTest(type) {
            const endpoints = {
                'reservations': '/api/reservation/reservations',
                'waiter-requests': '/api/reservation/waiter-requests',
                'areas': '/api/reservation/areas',
                'tables': '/api/reservation/tables'
            };

            document.getElementById('method').value = 'GET';
            document.getElementById('endpoint').value = endpoints[type];
            document.getElementById('customUrl').value = '';
            document.getElementById('headers').value = '';
            document.getElementById('requestBody').value = '';
            
            // Hide body section for GET requests
            document.getElementById('bodySection').style.display = 'none';
            
            // Auto-send the request
            sendRequest();
        }

        // Initialize
        document.getElementById('bodySection').style.display = 'none';
    </script>
</body>
</html>