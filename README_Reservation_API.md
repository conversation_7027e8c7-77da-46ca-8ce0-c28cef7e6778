# Restaurant POS - Reservation API Testing Guide

## Overview
This guide provides comprehensive instructions for testing the Reservation module API endpoints using the provided Postman collection.

## 📁 Files Included
- `Reservation_API_Postman_Collection.json` - Complete Postman collection with all API endpoints
- `README_Reservation_API.md` - This documentation file

## 🚀 Quick Start

### 1. Import the Postman Collection
1. Open Postman
2. Click "Import" button
3. Select the `Reservation_API_Postman_Collection.json` file
4. The collection will be imported with all endpoints organized in folders

### 2. Configure Environment Variables
The collection uses the following variables that you can customize:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://localhost:8000` | Your Laravel application URL |
| `auth_token` | (empty) | Bearer token for authentication |
| `tenant_id` | `1` | Tenant ID for multi-tenancy |
| `branch_id` | `1` | Branch ID for restaurant location |
| `area_id` | (auto-set) | Area ID (set automatically after creating an area) |
| `table_id` | (auto-set) | Table ID (set automatically after creating a table) |
| `reservation_id` | (auto-set) | Reservation ID (set automatically after creating a reservation) |
| `waiter_request_id` | (auto-set) | Waiter Request ID (set automatically after creating a request) |

### 3. Authentication Setup
1. **Login First**: Use the "Authentication > Login" endpoint to get an auth token
2. **Auto Token Storage**: The collection automatically saves the token from login response
3. **Manual Token**: If needed, you can manually set the `auth_token` variable

## 📋 API Endpoints Overview

### 🔐 Authentication
- **POST** `/api/login` - User login (sets auth token automatically)

### 🏢 Areas Management
- **GET** `/areas` - Get all areas
- **POST** `/areas` - Create new area
- **GET** `/areas/{id}` - Get area by ID
- **PUT** `/areas/{id}` - Update area
- **DELETE** `/areas/{id}` - Delete area

### 🪑 Tables Management
- **GET** `/tables` - Get all tables
- **POST** `/tables` - Create new table
- **GET** `/tables/{id}` - Get table by ID
- **PUT** `/tables/{id}` - Update table
- **POST** `/tables/{id}/status` - Update table status
- **GET** `/tables/area/{area_id}` - Get tables by area
- **GET** `/tables/available/{datetime}` - Get available tables
- **DELETE** `/tables/{id}` - Delete table

### 📅 Reservations Management
- **GET** `/api/reservations` - Get all reservations (with filters)
- **POST** `/api/reservations` - Create new reservation
- **GET** `/api/reservations/{id}` - Get reservation by ID
- **PUT** `/api/reservations/{id}` - Update reservation
- **POST** `/api/reservations/{id}/confirm` - Confirm reservation
- **POST** `/api/reservations/{id}/seat` - Seat customers
- **POST** `/api/reservations/{id}/complete` - Complete reservation
- **POST** `/api/reservations/{id}/no-show` - Mark as no-show
- **POST** `/api/reservations/check-availability` - Check availability
- **GET** `/api/reservations/statistics/overview` - Get statistics
- **DELETE** `/api/reservations/{id}` - Cancel reservation

### 🙋 Waiter Requests
- **GET** `/waiter-requests` - Get all waiter requests
- **POST** `/waiter-requests` - Create waiter request
- **GET** `/waiter-requests/{id}` - Get request by ID
- **PUT** `/waiter-requests/{id}` - Update request
- **POST** `/waiter-requests/{id}/complete` - Complete request
- **POST** `/waiter-requests/{id}/cancel` - Cancel request
- **GET** `/waiter-requests/table/{table_id}` - Get requests by table
- **GET** `/waiter-requests/waiter/{waiter_id}` - Get requests by waiter
- **DELETE** `/waiter-requests/{id}` - Delete request

### 📱 QR Code Management
- **POST** `/qr-codes/validate` - Validate QR code
- **POST** `/qr-codes/table/{table_id}/generate` - Generate table QR code
- **POST** `/qr-codes/table/{table_id}/menu` - Generate menu QR code
- **GET** `/qr-codes/table/{table_id}/content` - Get QR content
- **POST** `/qr-codes/batch-generate` - Batch generate QR codes

### 🌐 Public API (No Authentication Required)
- **POST** `/public/qr/validate` - Public QR validation
- **POST** `/public/waiter-requests` - Public waiter request
- **POST** `/public/reservations/check-availability` - Public availability check

## 🔄 Testing Workflow

### Recommended Testing Sequence:

1. **Authentication**
   ```
   POST /api/login
   ```

2. **Create Area**
   ```
   POST /areas
   ```

3. **Create Table**
   ```
   POST /tables
   ```

4. **Create Reservation**
   ```
   POST /api/reservations
   ```

5. **Test Reservation Workflow**
   ```
   POST /api/reservations/{id}/confirm
   POST /api/reservations/{id}/seat
   POST /api/reservations/{id}/complete
   ```

6. **Test Waiter Requests**
   ```
   POST /waiter-requests
   POST /waiter-requests/{id}/complete
   ```

7. **Test QR Codes**
   ```
   POST /qr-codes/table/{table_id}/generate
   POST /qr-codes/validate
   ```

## 📝 Sample Request Bodies

### Create Area
```json
{
    "branch_id": 1,
    "name": "Main Dining Area",
    "description": "Primary dining area with 20 tables"
}
```

### Create Table
```json
{
    "branch_id": 1,
    "area_id": 1,
    "table_number": "T001",
    "table_name": "Table 1",
    "seating_capacity": 4,
    "section": "Window Side",
    "status": "available",
    "notes": "Near the window with a nice view",
    "is_active": true
}
```

### Create Reservation
```json
{
    "branch_id": 1,
    "table_id": 1,
    "customer_name": "John Doe",
    "customer_phone": "+1234567890",
    "email": "<EMAIL>",
    "party_size": 4,
    "reservation_datetime": "2024-12-25 19:00:00",
    "duration_minutes": 120,
    "special_requests": "Window table preferred",
    "notes": "Anniversary dinner",
    "area_id": 1
}
```

### Create Waiter Request
```json
{
    "table_id": 1,
    "branch_id": 1,
    "request_type": "service",
    "notes": "Customer needs assistance with menu",
    "status": "pending"
}
```

## 🔍 Response Handling

### Automatic Variable Setting
The collection automatically extracts and stores IDs from successful responses:
- Area creation → sets `area_id`
- Table creation → sets `table_id`
- Reservation creation → sets `reservation_id`
- Waiter request creation → sets `waiter_request_id`

### Expected Response Codes
- **200** - Success (GET, PUT requests)
- **201** - Created (POST requests)
- **204** - No Content (DELETE requests)
- **400** - Bad Request (validation errors)
- **401** - Unauthorized (authentication required)
- **404** - Not Found (resource doesn't exist)
- **422** - Unprocessable Entity (validation errors)

## 🛠️ Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Ensure you've logged in using the Authentication endpoint
   - Check that the auth token is properly set in collection variables

2. **404 Not Found**
   - Verify the Laravel application is running on the correct URL
   - Check that the route exists in the application

3. **400/422 Validation Errors**
   - Review the request body format
   - Ensure all required fields are provided
   - Check data types and formats

4. **Missing Variables**
   - Ensure you've run the prerequisite endpoints (e.g., create area before creating table)
   - Check that automatic variable setting is working in the test scripts

### Debug Tips
- Use Postman Console to view request/response details
- Check the Laravel application logs for detailed error information
- Verify database connections and migrations are up to date

## 📊 Testing Checklist

### Basic CRUD Operations
- [ ] Create, read, update, delete areas
- [ ] Create, read, update, delete tables
- [ ] Create, read, update, delete reservations
- [ ] Create, read, update, delete waiter requests

### Business Logic
- [ ] Reservation workflow (confirm → seat → complete)
- [ ] Table status management
- [ ] Availability checking
- [ ] QR code generation and validation

### Authentication & Authorization
- [ ] Protected endpoints require authentication
- [ ] Public endpoints work without authentication
- [ ] Proper error responses for unauthorized access

### Data Validation
- [ ] Required fields validation
- [ ] Data type validation
- [ ] Business rule validation (e.g., party size limits)

## 🎯 Advanced Testing

### Load Testing
- Use Postman's Collection Runner for bulk testing
- Test concurrent reservation creation
- Validate system behavior under load

### Integration Testing
- Test complete user journeys
- Verify data consistency across endpoints
- Test error handling and recovery

### Security Testing
- Test authentication bypass attempts
- Validate input sanitization
- Check for SQL injection vulnerabilities

## 📞 Support

If you encounter issues:
1. Check the Laravel application logs
2. Verify database connectivity
3. Ensure all required environment variables are set
4. Review the API route definitions in the Laravel application

---

**Happy Testing! 🚀**