<?php

namespace Modules\Orders\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\MenuItem;
use App\Models\MenuItemAddon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Orders\Helpers\OrderHelper;
use Modules\Inventory\Services\InventoryService;
use Modules\Kitchen\Services\KitchenService;
use Modules\Transaction\Services\TransactionService;

class OrderService
{
    protected $inventoryService;
    protected $kitchenService;
    protected $transactionService;

    public function __construct(
        InventoryService $inventoryService,
        KitchenService $kitchenService,
        TransactionService $transactionService = null
    ) {
        $this->inventoryService = $inventoryService;
        $this->kitchenService = $kitchenService;
        $this->transactionService = $transactionService;
    }

    /**
     * Get all orders with pagination and filters
     */
    public function getAllOrders(array $filters = []): LengthAwarePaginator
    {
        $query = Order::with(['items', 'customer', 'table', 'payments']);

        // Apply filters
        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['table_id'])) {
            $query->where('table_id', $filters['table_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get orders for a specific branch with optional filtering
     */
    public function getOrdersForBranch(string $branchId, array $filters = []): Collection
    {
        $query = Order::where('branch_id', $branchId)
            ->with(['items.menuItem', 'customer', 'table']);

        // Apply filters if provided
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['order_type'])) {
            $query->where('order_type', $filters['order_type']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['table_id'])) {
            $query->where('table_id', $filters['table_id']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get order by ID
     */
    public function getOrderById(string $id, string $branchId = null): Order
    {
        $query = Order::with(['items.menuItem', 'customer', 'table', 'payments', 'transaction']);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        return $query->findOrFail($id);
    }

    /**
     * Create a new order
     */
    public function createOrder(array $data): Order
    {
        $order = Order::create([
            'branch_id' => $data['branch_id'],
            'customer_id' => $data['customer_id'] ?? null,
            'table_id' => $data['table_id'] ?? null,
            'order_number' => OrderHelper::generateOrderNumber(),
            'status' => $data['status'] ?? 'pending',
            'order_type' => $data['order_type'] ?? 'dine_in',
            'pax' => $data['pax'] ?? null, // Number of people
            'subtotal' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
            'notes' => $data['notes'] ?? null,
            'delivery_man_id' => $data['delivery_man_id'] ?? null,
            'delivery_address' => $data['delivery_address'] ?? null,
            'delivery_coordinates' => $data['delivery_coordinates'] ?? null,
            'waiter_id' => $data['waiter_id'] ?? null,
            'cashier_id' => $data['cashier_id'] ?? null,
        ]);

        // Add order items if provided
        if (isset($data['items']) && is_array($data['items'])) {
            $this->addOrderItems($order, $data['items']);
        }

        // Auto-create KOT for confirmed dine_in or takeaway orders
        if ($order->status === 'confirmed' && in_array($order->order_type, ['dine_in', 'takeaway'])) {
            try {
                // Check if order already has an active KOT
                if (!$order->hasActiveKot()) {
                    // Load order items with menu items for KOT creation
                    $order->load(['orderItems.menuItem']);
                    $this->kitchenService->createKotFromOrder($order);
                    \Log::info('KOT created for new confirmed order ' . $order->id);
                } else {
                    \Log::info('Order ' . $order->id . ' already has an active KOT, skipping creation');
                }
            } catch (\Exception $e) {
                // Log the error but don't fail the order creation
                \Log::warning('Failed to create KOT for order: ' . $order->id, ['error' => $e->getMessage()]);
            }
        }

        return $order->load(['items.menuItem', 'customer', 'table']);
    }

    /**
     * Update an existing order
     */
    public function updateOrder(string $id, array $data): Order
    {
        $order = Order::findOrFail($id);
        
        // Check if order can be modified
        if (!OrderHelper::canBeModified($order->status)) {
            throw new \Exception("Order cannot be modified in '{$order->status}' status");
        }
        
        $order->update([
            'branch_id' => $data['branch_id'] ?? $order->branch_id,
            'status' => $data['status'] ?? $order->status,
            'notes' => $data['notes'] ?? $order->notes,
        ]);

        // Update order items if provided
        if (isset($data['items']) && is_array($data['items'])) {
            $order->items()->delete();
            $this->addOrderItems($order, $data['items']);
        }

        return $order->load(['items.menuItem', 'customer', 'table']);
    }

    /**
     * Delete an order
     */
    public function deleteOrder(string $id): void
    {
        $order = Order::findOrFail($id);
        
        // Check if order can be cancelled (deleted)
        if (!OrderHelper::canBeCancelled($order->status)) {
            throw new \Exception("Order cannot be deleted in '{$order->status}' status");
        }
        
        $order->items()->delete();
        $order->delete();
    }

    /**
     * Add items to an order
     */
    private function addOrderItems(Order $order, array $items): void
    {
        $subtotal = 0;

        foreach ($items as $item) {
            // Fetch menu item name from database
            $menuItem = MenuItem::findOrFail($item['menu_item_id']);
            
            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'menu_item_id' => $item['menu_item_id'],
                'menu_item_name' => $menuItem->name, // Fetch name from database for historical reference
                'variant_id' => $item['variant_id'] ?? null,
                'quantity' => $item['quantity'],
                'unit_price' => $item['unit_price'],
                'total_price' => $item['quantity'] * $item['unit_price'],
            ]);

            $subtotal += $orderItem->total_price;

            // Handle addons for this order item
            if (isset($item['addons']) && is_array($item['addons'])) {
                foreach ($item['addons'] as $addon) {
                    // Fetch addon name from database
                    $addonItem = MenuItemAddon::findOrFail($addon['addon_id']);
                    
                    $orderItemAddon = $orderItem->addons()->create([
                        'addon_id' => $addon['addon_id'],
                        'addon_name' => $addonItem->name, // Fetch name from database for historical reference
                        'quantity' => $addon['quantity'],
                        'unit_price' => $addon['unit_price'],
                        'total_price' => $addon['total_price'],
                    ]);
                    
                    // Add addon price to subtotal
                    $subtotal += $orderItemAddon->total_price;
                }
            }
        }

        // Calculate totals
        $taxAmount = OrderHelper::calculateTax($subtotal);
        $totalAmount = $subtotal + $taxAmount;

        $order->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(string $id, string $status): Order
    {
        $order = Order::findOrFail($id);

        // Validate status transition
        $allowedStatuses = OrderHelper::getNextStatuses($order->status);
        if (!in_array($status, $allowedStatuses) && $order->status !== $status) {
            throw new \Exception("Cannot change order status from '{$order->status}' to '{$status}'");
        }

        $oldStatus = $order->status;
        $order->update(['status' => $status]);

        // Handle transaction creation/updates when status changes
        $this->handleTransactionStatusChange($order, $oldStatus, $status);

        // Deduct inventory when order status is 'completed' or 'served'
        if (in_array($status, ['completed', 'served'])) {
            foreach ($order->items as $orderItem) {
                try {
                    $this->inventoryService->deductIngredientsForOrderItem($orderItem);
                } catch (\Exception $e) {
                    // Log the error but don't fail the status update
                    \Log::warning('Failed to deduct inventory for order item: ' . $orderItem->id, ['error' => $e->getMessage()]);
                }
            }
        }

        return $order;
    }

    /**
     * Get order items
     */
    public function getOrderItems(string $orderId): Collection
    {
        $order = Order::findOrFail($orderId);
        return $order->items()->with(['menuItem', 'variant', 'addons'])->get();
    }

    /**
     * Handle transaction status changes when order status changes
     */
    protected function handleTransactionStatusChange(Order $order, string $oldStatus, string $newStatus): void
    {
        // Only proceed if transaction service is available
        if (!$this->transactionService) {
            return;
        }

        try {
            // Create transaction when order is confirmed
            if ($newStatus === 'confirmed' && !$order->transaction) {
                $this->transactionService->createTransactionFromOrder($order);
            }

            // Void transaction when order is cancelled
            elseif ($newStatus === 'cancelled' && $order->transaction) {
                $this->transactionService->voidTransaction(
                    $order->transaction,
                    "Order cancelled from status '{$oldStatus}'"
                );
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the order status update
            \Log::warning('Failed to handle transaction status change for order: ' . $order->id, [
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Add item to order
     */
    public function addOrderItem(string $orderId, array $itemData): OrderItem
    {
        $order = Order::findOrFail($orderId);
        
        // Check if order can be modified
        if (!OrderHelper::canBeModified($order->status)) {
            throw new \Exception("Cannot add items to order in '{$order->status}' status");
        }
        
        // Fetch menu item name from database
        $menuItem = MenuItem::findOrFail($itemData['menu_item_id']);
        
        $orderItem = OrderItem::create([
            'order_id' => $order->id,
            'menu_item_id' => $itemData['menu_item_id'],
            'menu_item_name' => $menuItem->name, // Fetch name from database for historical reference
            'quantity' => $itemData['quantity'],
            'unit_price' => $itemData['price'],
            'total_price' => $itemData['quantity'] * $itemData['price'],
            'notes' => $itemData['notes'] ?? null,
        ]);

        // Recalculate order totals
        $this->recalculateOrderTotals($order);

        return $orderItem->load(['menuItem']);
    }

    /**
     * Remove item from order
     */
    public function removeOrderItem(string $orderId, string $itemId): void
    {
        $order = Order::findOrFail($orderId);
        
        // Check if order can be modified
        if (!OrderHelper::canBeModified($order->status)) {
            throw new \Exception("Cannot remove items from order in '{$order->status}' status");
        }
        
        $orderItem = OrderItem::where('order_id', $orderId)->findOrFail($itemId);
        
        $orderItem->delete();

        // Recalculate order totals
        $this->recalculateOrderTotals($order);
    }

    /**
     * Recalculate order totals
     */
    private function recalculateOrderTotals(Order $order): void
    {
        $subtotal = $order->items()->sum('total_price');
        $taxAmount = OrderHelper::calculateTax($subtotal);
        $totalAmount = $subtotal + $taxAmount;

        $order->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }
}