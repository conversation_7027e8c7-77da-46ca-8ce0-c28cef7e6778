<?php

namespace Modules\Delivery\Services;

use Modules\Delivery\Entities\DeliveryZone;
use App\Models\Branch;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class DeliveryZoneService
{
    /**
     * Create new delivery zone
     */
    public function createZone(array $data): DeliveryZone
    {
        $zoneData = [
            'tenant_id' => $data['tenant_id'],
            'branch_id' => $data['branch_id'],
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'delivery_fee' => $data['delivery_fee'],
            'minimum_order_amount' => $data['minimum_order_amount'] ?? 0,
            'estimated_delivery_time_minutes' => $data['estimated_delivery_time_minutes'] ?? 30,
            'is_active' => $data['is_active'] ?? true,
            'priority' => $data['priority'] ?? 1,
        ];
        
        // Add coordinates if provided
        if (isset($data['coordinates'])) {
            $zoneData['coordinates'] = $data['coordinates'];
        }
        
        // Add address fields if provided
        if (isset($data['address'])) {
            $zoneData['address'] = $data['address'];
            $zoneData['city'] = $data['city'] ?? null;
            $zoneData['state'] = $data['state'] ?? null;
            $zoneData['postal_code'] = $data['postal_code'] ?? null;
            $zoneData['country'] = $data['country'] ?? null;
        }
        
        return DeliveryZone::create($zoneData);
    }

    /**
     * Update delivery zone
     */
    public function updateZone(int $zoneId, array $data): DeliveryZone
    {
        $zone = DeliveryZone::findOrFail($zoneId);
        
        $zoneData = [
            'tenant_id' => $data['tenant_id'],
            'branch_id' => $data['branch_id'],
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'delivery_fee' => $data['delivery_fee'],
            'minimum_order_amount' => $data['minimum_order_amount'] ?? 0,
            'estimated_delivery_time_minutes' => $data['estimated_delivery_time_minutes'] ?? 30,
            'is_active' => $data['is_active'] ?? true,
            'priority' => $data['priority'] ?? 1,
        ];
        
        // Add coordinates if provided
        if (isset($data['coordinates'])) {
            $zoneData['coordinates'] = $data['coordinates'];
        }
        
        // Add address fields if provided
        if (isset($data['address'])) {
            $zoneData['address'] = $data['address'];
            $zoneData['city'] = $data['city'] ?? null;
            $zoneData['state'] = $data['state'] ?? null;
            $zoneData['postal_code'] = $data['postal_code'] ?? null;
            $zoneData['country'] = $data['country'] ?? null;
        }
        
        $zone->update($zoneData);
        return $zone->fresh(['branch']);
    }

    /**
     * Get zones with filters
     */
    public function getZones(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = DeliveryZone::with(['branch']);

        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->orderBy('priority')->orderBy('name')->paginate($perPage);
    }

    /**
     * Get zone by ID
     */
    public function getZoneById(int $id): ?DeliveryZone
    {
        return DeliveryZone::with(['branch'])->find($id);
    }

    /**
     * Find zone by coordinates using point-in-polygon algorithm
     */
    public function findZoneByCoordinates(float $lat, float $lng): ?DeliveryZone
    {
        $zones = DeliveryZone::where('is_active', true)
            ->orderBy('priority')
            ->get();

        foreach ($zones as $zone) {
            if ($zone->coordinates && $this->isPointInPolygon($lat, $lng, $zone->coordinates)) {
                return $zone;
            }
        }

        return null;
    }
    
    /**
     * Find zone by address
     */
    public function findZoneByAddress(string $address, ?string $city = null, ?string $state = null, ?string $postalCode = null, ?string $country = null): ?DeliveryZone
    {
        $query = DeliveryZone::where('is_active', true);
        
        // Build query based on provided address components
        $query->where(function($q) use ($address, $city, $state, $postalCode, $country) {
            $q->where('address', 'like', "%{$address}%");
            
            if ($city) {
                $q->where('city', 'like', "%{$city}%");
            }
            
            if ($state) {
                $q->where('state', 'like', "%{$state}%");
            }
            
            if ($postalCode) {
                $q->where('postal_code', 'like', "%{$postalCode}%");
            }
            
            if ($country) {
                $q->where('country', 'like', "%{$country}%");
            }
        });
        
        return $query->orderBy('priority')->first();
    }

    /**
     * Check if coordinates are within delivery area
     */
    public function isDeliveryAvailable(float $lat, float $lng): bool
    {
        return $this->findZoneByCoordinates($lat, $lng) !== null;
    }
    
    /**
     * Check if address is within delivery area
     */
    public function isDeliveryAvailableByAddress(string $address, ?string $city = null, ?string $state = null, ?string $postalCode = null, ?string $country = null): bool
    {
        return $this->findZoneByAddress($address, $city, $state, $postalCode, $country) !== null;
    }
    
    /**
     * Check delivery availability by address with detailed response
     */
    public function checkDeliveryAvailabilityByAddress(string $address, ?string $city = null, ?string $state = null, ?string $postalCode = null, ?string $country = null, ?int $branchId = null): array
    {
        $query = DeliveryZone::where('is_active', true);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        // Build query based on provided address components
        $query->where(function($q) use ($address, $city, $state, $postalCode, $country) {
            $q->where('address', 'like', "%{$address}%");
            
            if ($city) {
                $q->where('city', 'like', "%{$city}%");
            }
            
            if ($state) {
                $q->where('state', 'like', "%{$state}%");
            }
            
            if ($postalCode) {
                $q->where('postal_code', 'like', "%{$postalCode}%");
            }
            
            if ($country) {
                $q->where('country', 'like', "%{$country}%");
            }
        });
        
        $zone = $query->orderBy('priority')->first();
        
        if ($zone) {
            return [
                'available' => true,
                'zone' => $zone,
                'delivery_fee' => $zone->delivery_fee,
                'minimum_order_amount' => $zone->minimum_order_amount,
                'estimated_delivery_time' => $zone->estimated_delivery_time_minutes,
            ];
        }
        
        return ['available' => false];
    }

    /**
     * Check delivery availability by coordinates with detailed response
     */
    public function checkDeliveryAvailability(float $lat, float $lng, ?int $branchId = null): array
    {
        $query = DeliveryZone::where('is_active', true);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        $zones = $query->orderBy('priority')->get();
        
        foreach ($zones as $zone) {
            if ($zone->coordinates && $this->isPointInPolygon($lat, $lng, $zone->coordinates)) {
                return [
                    'available' => true,
                    'zone' => $zone,
                    'delivery_fee' => $zone->delivery_fee,
                    'minimum_order_amount' => $zone->minimum_order_amount,
                    'estimated_delivery_time' => $zone->estimated_delivery_time_minutes,
                ];
            }
        }
        
        return ['available' => false];
    }
    
    /**
     * Get active zones for a branch
     */
    public function getActiveZonesForBranch(int $branchId): Collection
    {
        return DeliveryZone::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('priority')
            ->get();
    }

    /**
     * Calculate delivery fee for coordinates
     */
    public function calculateDeliveryFee(float $lat, float $lng): array
    {
        $zone = $this->findZoneByCoordinates($lat, $lng);

        if (!$zone) {
            return [
                'available' => false,
                'fee' => 0,
                'zone' => null,
                'message' => 'Delivery not available in this area'
            ];
        }

        return [
            'available' => true,
            'fee' => $zone->delivery_fee,
            'zone' => $zone,
            'minimum_order' => $zone->minimum_order_amount,
            'estimated_time' => $zone->estimated_delivery_time_minutes,
            'message' => 'Delivery available'
        ];
    }

    /**
     * Delete zone
     */
    public function deleteZone(int $zoneId): bool
    {
        $zone = DeliveryZone::findOrFail($zoneId);
        
        // Check if zone has active deliveries
        if ($zone->deliveryAssignments()->whereIn('status', ['assigned', 'picked_up', 'in_transit'])->count() > 0) {
            throw new \Exception('Cannot delete zone with active deliveries');
        }

        return $zone->delete();
    }

    /**
     * Toggle zone status
     */
    public function toggleZoneStatus(int $zoneId): DeliveryZone
    {
        $zone = DeliveryZone::findOrFail($zoneId);
        $zone->update(['is_active' => !$zone->is_active]);
        
        return $zone;
    }

    /**
     * Get zone statistics
     */
    public function getZoneStatistics(int $zoneId, array $filters = []): array
    {
        $zone = DeliveryZone::with(['deliveryAssignments' => function ($query) use ($filters) {
            if (isset($filters['date_from'])) {
                $query->whereDate('assigned_at', '>=', $filters['date_from']);
            }
            if (isset($filters['date_to'])) {
                $query->whereDate('assigned_at', '<=', $filters['date_to']);
            }
        }])->findOrFail($zoneId);

        $assignments = $zone->deliveryAssignments;
        
        $totalDeliveries = $assignments->count();
        $successfulDeliveries = $assignments->where('status', 'delivered')->count();
        $failedDeliveries = $assignments->where('status', 'failed')->count();
        
        $successRate = $totalDeliveries > 0 ? ($successfulDeliveries / $totalDeliveries) * 100 : 0;
        
        $avgDeliveryTime = $assignments->where('status', 'delivered')
            ->whereNotNull('actual_duration_minutes')
            ->avg('actual_duration_minutes');
            
        $totalRevenue = $assignments->where('status', 'delivered')
            ->sum('delivery_fee_earned');

        return [
            'zone' => $zone,
            'total_deliveries' => $totalDeliveries,
            'successful_deliveries' => $successfulDeliveries,
            'failed_deliveries' => $failedDeliveries,
            'success_rate' => round($successRate, 2),
            'average_delivery_time' => round($avgDeliveryTime ?? 0, 2),
            'total_revenue' => $totalRevenue,
        ];
    }

    /**
     * Point-in-polygon algorithm to check if coordinates are within zone
     */
    private function isPointInPolygon(float $lat, float $lng, array $polygon): bool
    {
        $vertices = count($polygon);
        $inside = false;

        for ($i = 0, $j = $vertices - 1; $i < $vertices; $j = $i++) {
            $xi = $polygon[$i]['latitude'];
            $yi = $polygon[$i]['longitude'];
            $xj = $polygon[$j]['latitude'];
            $yj = $polygon[$j]['longitude'];

            if ((($yi > $lng) !== ($yj > $lng)) && 
                ($lat < ($xj - $xi) * ($lng - $yi) / ($yj - $yi) + $xi)) {
                $inside = !$inside;
            }
        }

        return $inside;
    }

    /**
     * Get zones within radius of coordinates
     */
    public function getZonesWithinRadius(float $lat, float $lng, float $radiusKm = 10): Collection
    {
        return DeliveryZone::where('is_active', true)
            ->get()
            ->filter(function ($zone) use ($lat, $lng, $radiusKm) {
                // Calculate distance to zone center (approximate)
                $zoneCenterLat = collect($zone->coordinates)->avg('latitude');
                $zoneCenterLng = collect($zone->coordinates)->avg('longitude');
                
                $distance = $this->calculateDistance($lat, $lng, $zoneCenterLat, $zoneCenterLng);
                
                return $distance <= $radiusKm;
            });
    }

    /**
     * Calculate distance between two coordinates
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // km
        $latDiff = deg2rad($lat2 - $lat1);
        $lngDiff = deg2rad($lng2 - $lng1);
        
        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($lngDiff / 2) * sin($lngDiff / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }
}