<?php

namespace Modules\Menu\Services;

use App\Models\MenuItem;
use Illuminate\Pagination\LengthAwarePaginator;

class MenuItemService
{
    protected $codeGenerator;

    public function __construct(CodeGeneratorService $codeGenerator)
    {
        $this->codeGenerator = $codeGenerator;
    }
    /**
     * Get menu items for a specific branch with pagination and filtering.
     */
    public function getMenuItemsForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = MenuItem::with(['menu', 'category', 'variants', 'addons'])
            ->withCount(['variants', 'addons'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply filters
        if (isset($filters['menu_id'])) {
            $query->where('menu_id', $filters['menu_id']);
        }

        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['is_featured'])) {
            $query->where('is_featured', $filters['is_featured']);
        }

        if (isset($filters['is_spicy'])) {
            $query->where('is_spicy', $filters['is_spicy']);
        }

        if (isset($filters['min_price'])) {
            $query->where('base_price', '>=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $query->where('base_price', '<=', $filters['max_price']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get menu item by ID with relationships.
     */
    public function getMenuItemById(int $id): ?MenuItem
    {
        return MenuItem::with(['menu', 'category', 'recipe', 'variants', 'addons', 'branches', 'availability'])
            ->withCount(['variants', 'addons'])
            ->find($id);
    }

    /**
     * Create a new menu item.
     */
    public function createMenuItem(array $data): MenuItem
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateMenuItemCode(
                $data['name'], 
                $data['category_id'] ?? null
            );
        }

        return MenuItem::create($data);
    }

    /**
     * Update an existing menu item.
     */
    public function updateMenuItem(int $id, array $data): ?MenuItem
    {
        $menuItem = MenuItem::find($id);
        
        if (!$menuItem) {
            return null;
        }

        $menuItem->update($data);
        
        return $menuItem->fresh(['menu', 'category', 'variants', 'addons']);
    }

    /**
     * Delete a menu item.
     */
    public function deleteMenuItem(int $id): bool
    {
        $menuItem = MenuItem::find($id);
        
        if (!$menuItem) {
            return false;
        }

        return $menuItem->delete();
    }

    /**
     * Delete a menu item for a specific branch.
     */
    public function deleteMenuItemForBranch(int $id, int $branchId): bool
    {
        $menuItem = MenuItem::whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$menuItem) {
            return false;
        }

        return $menuItem->delete();
    }

    /**
     * Get active menu items for a menu.
     */
    public function getActiveMenuItemsForMenu(int $menuId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItem::with(['category', 'variants', 'addons'])
            ->where('menu_id', $menuId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection Collection of featured menu items ordered by sort_order
     */
    public function getFeaturedMenuItemsForBranch(int $branchId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItem::with(['menu', 'category', 'variants', 'addons', 'branches'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->where('is_featured', true)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get menu items by category.
     * 
     * @param int $categoryId The ID of the category to get menu items for
     * @return \Illuminate\Database\Eloquent\Collection Collection of active menu items in the category ordered by sort_order
     */
    public function getMenuItemsByCategory(int $categoryId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItem::with(['menu', 'category', 'variants', 'addons', 'branches'])
            ->where('category_id', $categoryId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get menu item by ID for a specific branch.
     * 
     * @param int $id The ID of the menu item to retrieve
     * @param int $branchId The ID of the branch that should own the menu item's menu
     * @return MenuItem|null The menu item with relationships or null if not found or not owned by branch
     */
    public function getMenuItemByIdForBranch(int $id, int $branchId): ?MenuItem
    {
        return MenuItem::with(['menu:id,name', 'category:id,name'])
            ->select('id', 'menu_id', 'category_id', 'name', 'code', 'sku', 'description', 'short_description', 'base_price', 'cost_price', 'image', 'prep_time_minutes', 'calories', 'nutritional_info', 'allergens', 'dietary_info', 'recipe_id', 'barcode', 'is_active', 'is_featured', 'is_spicy', 'spice_level', 'sort_order', 'created_at', 'updated_at')
            ->withCount(['variants', 'addons'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
    }

    /**
     * Update menu item for a specific branch.
     * 
     * @param int $id The ID of the menu item to update
     * @param array $data Updated menu item data
     * @param int $branchId The ID of the branch that should own the menu item's menu
     * @return MenuItem|null The updated menu item with fresh relationships or null if not found or not owned by branch
     */
    public function updateMenuItemForBranch(int $id, array $data, int $branchId): ?MenuItem
    {
        $menuItem = MenuItem::whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$menuItem) {
            return null;
        }

        $menuItem->update($data);
        
        return $menuItem->fresh(['menu', 'category', 'variants', 'addons']);
    }

    /**
     * Create menu item for web interface.
     * 
     * @param array $data Menu item data from web form
     * @param \Illuminate\Http\Request|null $request The request object for file uploads
     * @return MenuItem The created menu item instance with loaded relationships
     */
    public function createMenuItemForWeb(array $data, ?\Illuminate\Http\Request $request = null): MenuItem
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateMenuItemCode(
                $data['name'], 
                $data['category_id'] ?? null
            );
        }

        // Handle image upload
        if ($request && $request->hasFile('image')) {
            $imagePath = \App\Helpers\ImageHelper::storeImage($request->file('image'), 'menu-items');
            if ($imagePath) {
                $data['image'] = $imagePath;
            }
        }

        $menuItem = MenuItem::create($data);
        return $menuItem->load(['menu', 'category', 'variants', 'addons']);
    }

    /**
     * Update menu item for web interface.
     * 
     * @param int $id The ID of the menu item to update
     * @param array $data Updated menu item data from web form
     * @param int $branchId The ID of the branch that should own the menu item's menu
     * @param \Illuminate\Http\Request|null $request The request object for file uploads
     * @return MenuItem|null The updated menu item or null if not found or not owned by branch
     */
    public function updateMenuItemForWeb(int $id, array $data, int $branchId, ?\Illuminate\Http\Request $request = null): ?MenuItem
    {
        $menuItem = MenuItem::whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$menuItem) {
            return null;
        }

        // Handle image upload
        if ($request && $request->hasFile('image')) {
            // Delete old image if exists
            if ($menuItem->image) {
                \App\Helpers\ImageHelper::deleteImage($menuItem->image);
            }
            
            $imagePath = \App\Helpers\ImageHelper::storeImage($request->file('image'), 'menu-items');
            if ($imagePath) {
                $data['image'] = $imagePath;
            }
        }

        $menuItem->update($data);
        
        return $menuItem->fresh(['menu', 'category', 'variants', 'addons']);
    }

    /**
     * Get menu items for DataTable.
     * 
     * @param int $branchId The ID of the branch to get menu items for
     * @param \Illuminate\Http\Request $request The request object with search, sort, and pagination parameters
     * @return \Illuminate\Pagination\LengthAwarePaginator Laravel pagination response with menu item data
     */
    public function getMenuItemsForDataTable(int $branchId, \Illuminate\Http\Request $request): \Illuminate\Pagination\LengthAwarePaginator
    {
        $query = MenuItem::with(['menu:id,name', 'category:id,name'])
            ->select('id', 'menu_id', 'category_id', 'name', 'code', 'base_price', 'image', 'is_active', 'created_at')
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply search
        if ($request->has('search') && !empty($request->get('search'))) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        // Apply category filter
        if ($request->has('category_id') && !empty($request->get('category_id'))) {
            $query->where('category_id', $request->get('category_id'));
        }

        // Apply status filter
        if ($request->has('status') && $request->get('status') !== '') {
            $query->where('is_active', $request->get('status'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        if ($sortBy === 'category_name') {
            $query->join('categories', 'menu_items.category_id', '=', 'categories.id')
                  ->orderBy('categories.name', $sortDirection)
                  ->select('menu_items.*');
        } elseif ($sortBy === 'price') {
            $query->orderBy('base_price', $sortDirection);
        } else {
            $query->orderBy($sortBy, $sortDirection);
        }

        // Apply pagination
        $perPage = $request->get('per_page', 10);
        
        return $query->paginate($perPage);
    }

    /**
     * Get menu items list for branch.
     * 
     * @param int $branchId The ID of the branch to get menu items for
     * @param int|null $categoryId Optional category ID to filter menu items
     * @return \Illuminate\Database\Eloquent\Collection Collection of menu items with id, name, code, and base_price fields only
     */
    public function getMenuItemsListForBranch(int $branchId, ?int $categoryId = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = MenuItem::whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->where('is_active', true)
            ->select('id', 'name', 'code', 'base_price');

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        return $query->orderBy('name')->get();
    }

    /**
     * Get menu items query for custom pagination.
     * 
     * @param int $branchId The ID of the branch to get menu items for
     * @return \Illuminate\Database\Eloquent\Builder Query builder for custom pagination
     */
    public function getMenuItemsQuery(int $branchId)
    {
        return MenuItem::with(['menu:id,name', 'category:id,name'])
            ->select('id', 'menu_id', 'category_id', 'name', 'code', 'sku', 'description', 'short_description', 'base_price', 'cost_price', 'image', 'is_active', 'is_featured', 'is_available', 'prep_time_minutes', 'calories', 'is_spicy', 'spice_level', 'sort_order', 'created_at', 'updated_at')
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
    }

    /**
     * Get action buttons for DataTable.
     * 
     * @param int $id The ID of the menu item for the action buttons
     * @return string HTML string containing the action button group
     */
    private function getActionButtons(int $id): string
    {
        return '
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-info show-item" data-id="' . $id . '" title="عرض">
                    <i class="mdi mdi-eye"></i>
                </button>
                <button type="button" class="btn btn-sm btn-warning edit-item" data-id="' . $id . '" title="تعديل">
                    <i class="mdi mdi-pencil"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger delete-item" data-id="' . $id . '" title="حذف">
                    <i class="mdi mdi-delete"></i>
                </button>
            </div>
        ';
    }
}