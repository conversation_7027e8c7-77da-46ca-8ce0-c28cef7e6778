<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItemVariant;
use App\Models\MenuItem;

class MenuItemVariantSeeder extends Seeder
{
    public function run()
    {
        // Get the first menu item that exists with its menu and branch info
        $menuItem = MenuItem::with('menu')->first();
        
        if (!$menuItem) {
            $this->command->info('No menu items found. Skipping MenuItemVariantSeeder.');
            return;
        }

        // Get tenant_id and branch_id from the menu item's menu
        $tenantId = $menuItem->menu->tenant_id ?? 1;
        $branchId = $menuItem->menu->branch_id ?? 1;

        // Create variants for the menu item
        $variants = [
            [
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'menu_item_id' => $menuItem->id,
                'name' => 'صغير',
                'name_en' => 'Small',
                'code' => 'VARIANT_SMALL',
                'price_modifier' => -1.00,
                'cost_modifier' => -0.25,
                'is_default' => false,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'menu_item_id' => $menuItem->id,
                'name' => 'متوسط',
                'name_en' => 'Medium',
                'code' => 'VARIANT_MEDIUM',
                'price_modifier' => 0.00,
                'cost_modifier' => 0.00,
                'is_default' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'menu_item_id' => $menuItem->id,
                'name' => 'كبير',
                'name_en' => 'Large',
                'code' => 'VARIANT_LARGE',
                'price_modifier' => 2.00,
                'cost_modifier' => 0.50,
                'is_default' => false,
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($variants as $variantData) {
            MenuItemVariant::create($variantData);
        }

        // Create variants for other menu items if they exist
        $otherMenuItems = MenuItem::with('menu')->skip(1)->take(2)->get();
        
        foreach ($otherMenuItems as $item) {
            $itemTenantId = $item->menu->tenant_id ?? 1;
            $itemBranchId = $item->menu->branch_id ?? 1;
            
            MenuItemVariant::create([
                'tenant_id' => $itemTenantId,
                'branch_id' => $itemBranchId,
                'menu_item_id' => $item->id,
                'name' => 'عادي',
                'name_en' => 'Regular',
                'code' => 'VARIANT_REGULAR_' . $item->id,
                'price_modifier' => 0.00,
                'cost_modifier' => 0.00,
                'is_default' => true,
                'is_active' => true,
                'sort_order' => 1,
            ]);
        }
    }
}