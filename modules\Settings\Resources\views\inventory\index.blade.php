@extends('layouts.master')

@section('title', 'Inventory Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-rose-600 to-pink-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-boxes text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Inventory Settings</h1>
                        <p class="text-rose-100">Configure inventory management and stock control settings</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-rose-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-rose-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-rose-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-rose-200 mx-2"></i>
                                <span class="text-white font-medium">Inventory Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white rounded-lg shadow-lg mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active" data-tab="general">
                    <i class="fas fa-cog mr-2"></i>
                    General Settings
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="alerts">
                    <i class="fas fa-bell mr-2"></i>
                    Stock Alerts
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="tracking">
                    <i class="fas fa-search mr-2"></i>
                    Stock Tracking
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="suppliers">
                    <i class="fas fa-truck mr-2"></i>
                    Suppliers
                </button>
            </nav>
        </div>
    </div>

    <!-- General Settings Tab -->
    <div id="general-tab" class="tab-content">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">General Inventory Settings</h3>
                <p class="text-gray-600 mt-1">Configure basic inventory management preferences</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="stock_valuation_method" class="block text-sm font-medium text-gray-700 mb-2">
                            Stock Valuation Method
                        </label>
                        <select id="stock_valuation_method" name="stock_valuation_method" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                            <option value="fifo">FIFO (First In, First Out)</option>
                            <option value="lifo">LIFO (Last In, First Out)</option>
                            <option value="average">Weighted Average</option>
                            <option value="specific">Specific Identification</option>
                        </select>
                    </div>

                    <div>
                        <label for="default_unit" class="block text-sm font-medium text-gray-700 mb-2">
                            Default Unit of Measurement
                        </label>
                        <select id="default_unit" name="default_unit" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                            <option value="pieces">Pieces</option>
                            <option value="kg">Kilograms</option>
                            <option value="lbs">Pounds</option>
                            <option value="liters">Liters</option>
                            <option value="gallons">Gallons</option>
                            <option value="boxes">Boxes</option>
                        </select>
                    </div>

                    <div>
                        <label for="reorder_point_days" class="block text-sm font-medium text-gray-700 mb-2">
                            Default Reorder Point (days)
                        </label>
                        <input type="number" id="reorder_point_days" name="reorder_point_days" value="7" min="1" max="90"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                        <p class="text-sm text-gray-500 mt-1">Days of stock to maintain before reordering</p>
                    </div>

                    <div>
                        <label for="lead_time_days" class="block text-sm font-medium text-gray-700 mb-2">
                            Default Lead Time (days)
                        </label>
                        <input type="number" id="lead_time_days" name="lead_time_days" value="3" min="1" max="30"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                        <p class="text-sm text-gray-500 mt-1">Time between ordering and receiving stock</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_deduct_stock" name="auto_deduct_stock" value="1" checked
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="auto_deduct_stock" class="ml-2 text-sm text-gray-700">
                            Automatically deduct stock when orders are completed
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="track_expiry_dates" name="track_expiry_dates" value="1" checked
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="track_expiry_dates" class="ml-2 text-sm text-gray-700">
                            Track expiry dates for perishable items
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="allow_negative_stock" name="allow_negative_stock" value="1"
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="allow_negative_stock" class="ml-2 text-sm text-gray-700">
                            Allow negative stock levels (backorders)
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="batch_tracking" name="batch_tracking" value="1"
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="batch_tracking" class="ml-2 text-sm text-gray-700">
                            Enable batch/lot tracking for inventory items
                        </label>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-rose-600 border border-transparent rounded-lg hover:bg-rose-700 focus:ring-2 focus:ring-rose-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save General Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Stock Alerts Tab -->
    <div id="alerts-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Stock Alert Settings</h3>
                <p class="text-gray-600 mt-1">Configure low stock alerts and notifications</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="low_stock_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                            Low Stock Threshold (%)
                        </label>
                        <input type="number" id="low_stock_threshold" name="low_stock_threshold" value="20" min="1" max="50"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                        <p class="text-sm text-gray-500 mt-1">Alert when stock falls below this percentage</p>
                    </div>

                    <div>
                        <label for="critical_stock_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                            Critical Stock Threshold (%)
                        </label>
                        <input type="number" id="critical_stock_threshold" name="critical_stock_threshold" value="5" min="1" max="20"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                        <p class="text-sm text-gray-500 mt-1">Critical alert when stock falls below this percentage</p>
                    </div>

                    <div>
                        <label for="expiry_alert_days" class="block text-sm font-medium text-gray-700 mb-2">
                            Expiry Alert (days before)
                        </label>
                        <input type="number" id="expiry_alert_days" name="expiry_alert_days" value="7" min="1" max="30"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                        <p class="text-sm text-gray-500 mt-1">Alert when items are about to expire</p>
                    </div>

                    <div>
                        <label for="alert_frequency" class="block text-sm font-medium text-gray-700 mb-2">
                            Alert Frequency
                        </label>
                        <select id="alert_frequency" name="alert_frequency" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                            <option value="immediate">Immediate</option>
                            <option value="daily">Daily Summary</option>
                            <option value="weekly">Weekly Summary</option>
                        </select>
                    </div>
                </div>

                <!-- Alert Recipients -->
                <div>
                    <label for="alert_recipients" class="block text-sm font-medium text-gray-700 mb-2">
                        Alert Recipients (Email addresses, comma-separated)
                    </label>
                    <textarea id="alert_recipients" name="alert_recipients" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500"
                              placeholder="<EMAIL>, <EMAIL>"><EMAIL>, <EMAIL></textarea>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="email_alerts" name="email_alerts" value="1" checked
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="email_alerts" class="ml-2 text-sm text-gray-700">
                            Send email alerts for low stock
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="sms_alerts" name="sms_alerts" value="1"
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="sms_alerts" class="ml-2 text-sm text-gray-700">
                            Send SMS alerts for critical stock levels
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="dashboard_alerts" name="dashboard_alerts" value="1" checked
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="dashboard_alerts" class="ml-2 text-sm text-gray-700">
                            Show alerts on dashboard
                        </label>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-rose-600 border border-transparent rounded-lg hover:bg-rose-700 focus:ring-2 focus:ring-rose-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Alert Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Stock Tracking Tab -->
    <div id="tracking-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Stock Tracking Settings</h3>
                <p class="text-gray-600 mt-1">Configure how stock movements are tracked and recorded</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="stock_adjustment_reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Require Reason for Stock Adjustments
                        </label>
                        <select id="stock_adjustment_reason" name="stock_adjustment_reason" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                            <option value="always">Always Required</option>
                            <option value="negative">Only for Negative Adjustments</option>
                            <option value="optional">Optional</option>
                            <option value="never">Never Required</option>
                        </select>
                    </div>

                    <div>
                        <label for="stock_count_frequency" class="block text-sm font-medium text-gray-700 mb-2">
                            Stock Count Frequency
                        </label>
                        <select id="stock_count_frequency" name="stock_count_frequency" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="quarterly">Quarterly</option>
                        </select>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="track_stock_movements" name="track_stock_movements" value="1" checked
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="track_stock_movements" class="ml-2 text-sm text-gray-700">
                            Track all stock movements (in/out/adjustments)
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="require_approval" name="require_approval" value="1"
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="require_approval" class="ml-2 text-sm text-gray-700">
                            Require manager approval for large stock adjustments
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="barcode_scanning" name="barcode_scanning" value="1" checked
                               class="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300 rounded">
                        <label for="barcode_scanning" class="ml-2 text-sm text-gray-700">
                            Enable barcode scanning for stock operations
                        </label>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-rose-600 border border-transparent rounded-lg hover:bg-rose-700 focus:ring-2 focus:ring-rose-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Tracking Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Suppliers Tab -->
    <div id="suppliers-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Supplier Management</h3>
                        <p class="text-gray-600 mt-1">Manage supplier information and preferences</p>
                    </div>
                    <button class="px-4 py-2 bg-rose-600 text-white rounded-lg hover:bg-rose-700 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add Supplier
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="space-y-4">
                    <!-- Sample Suppliers -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">Fresh Foods Wholesale</h4>
                                <p class="text-sm text-gray-600">Primary supplier for fresh produce and dairy</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                                <button class="text-rose-600 hover:text-rose-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div>
                                <strong>Contact:</strong> John Smith<br>
                                <strong>Phone:</strong> (*************
                            </div>
                            <div>
                                <strong>Email:</strong> <EMAIL><br>
                                <strong>Lead Time:</strong> 2-3 days
                            </div>
                            <div>
                                <strong>Payment Terms:</strong> Net 30<br>
                                <strong>Last Order:</strong> 2 days ago
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">Metro Beverage Supply</h4>
                                <p class="text-sm text-gray-600">Beverages, soft drinks, and bar supplies</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                                <button class="text-rose-600 hover:text-rose-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div>
                                <strong>Contact:</strong> Sarah Johnson<br>
                                <strong>Phone:</strong> (*************
                            </div>
                            <div>
                                <strong>Email:</strong> <EMAIL><br>
                                <strong>Lead Time:</strong> 1-2 days
                            </div>
                            <div>
                                <strong>Payment Terms:</strong> Net 15<br>
                                <strong>Last Order:</strong> 1 week ago
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">Kitchen Essentials Ltd</h4>
                                <p class="text-sm text-gray-600">Kitchen supplies, utensils, and equipment</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Inactive</span>
                                <button class="text-rose-600 hover:text-rose-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div>
                                <strong>Contact:</strong> Mike Wilson<br>
                                <strong>Phone:</strong> (*************
                            </div>
                            <div>
                                <strong>Email:</strong> <EMAIL><br>
                                <strong>Lead Time:</strong> 5-7 days
                            </div>
                            <div>
                                <strong>Payment Terms:</strong> Net 45<br>
                                <strong>Last Order:</strong> 3 months ago
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Supplier Settings -->
                <div class="border-t border-gray-200 pt-6 mt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Supplier Settings</h4>
                    <form class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="default_payment_terms" class="block text-sm font-medium text-gray-700 mb-2">
                                Default Payment Terms
                            </label>
                            <select id="default_payment_terms" name="default_payment_terms" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                                <option value="net15">Net 15</option>
                                <option value="net30">Net 30</option>
                                <option value="net45">Net 45</option>
                                <option value="cod">Cash on Delivery</option>
                            </select>
                        </div>

                        <div>
                            <label for="auto_reorder" class="block text-sm font-medium text-gray-700 mb-2">
                                Auto-Reorder Settings
                            </label>
                            <select id="auto_reorder" name="auto_reorder" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                                <option value="disabled">Disabled</option>
                                <option value="suggest">Suggest Orders</option>
                                <option value="automatic">Automatic Orders</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Tab switching
    $('.settings-tab').on('click', function() {
        const tabName = $(this).data('tab');
        
        // Update tab appearance
        $('.settings-tab').removeClass('border-rose-500 text-rose-600').addClass('border-transparent text-gray-500');
        $(this).removeClass('border-transparent text-gray-500').addClass('border-rose-500 text-rose-600');
        
        // Show/hide tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}-tab`).removeClass('hidden');
    });

    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Add loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });

    // Add supplier handler
    $('button:contains("Add Supplier")').on('click', function() {
        Swal.fire({
            title: 'Add New Supplier',
            text: 'Supplier management feature coming soon!',
            icon: 'info',
            confirmButtonColor: '#f43f5e'
        });
    });
});
</script>
@endpush
