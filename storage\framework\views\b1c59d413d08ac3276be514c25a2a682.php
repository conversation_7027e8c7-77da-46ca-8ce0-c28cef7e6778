<!-- Header -->
<header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
    <div class="flex items-center justify-between px-4 py-3">
        <!-- Left side - Menu toggle and logo -->
        <div class="flex items-center space-x-4">
            <!-- Mobile menu toggle -->
            <button id="mobile-menu-toggle" class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-bars text-lg"></i>
            </button>
            
            <!-- Desktop menu toggle -->
            <button id="desktop-menu-toggle" class="hidden md:block p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-bars text-lg"></i>
            </button>
            
            <!-- Logo/Brand -->
            <div class="flex items-center">
                <h1 class="text-xl font-semibold text-gray-900">Restaurant POS</h1>
            </div>
        </div>
        
        <!-- Center - Search (hidden on mobile) -->
        <div class="hidden md:flex flex-1 max-w-md mx-8">
            <div class="relative w-full">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500" placeholder="Search...">
            </div>
        </div>
        
        <!-- Right side - Notifications and user menu -->
        <div class="flex items-center space-x-4">
            <!-- Search button for mobile -->
            <button class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-search text-lg"></i>
            </button>
            
            <!-- Notifications -->
            <div class="relative">
                <button id="notifications-toggle" class="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 relative">
                    <i class="fas fa-bell text-lg"></i>
                    <span class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
                </button>
                
                <!-- Notifications dropdown -->
                <div id="notifications-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    <div class="px-4 py-2 text-sm font-medium text-gray-900 border-b border-gray-200">
                        Notifications
                    </div>
                    <div class="max-h-64 overflow-y-auto">
                        <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 border-b border-gray-100">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-shopping-cart text-blue-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">New order received</p>
                                    <p class="text-xs text-gray-500">2 minutes ago</p>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 border-b border-gray-100">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-utensils text-green-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">Order completed</p>
                                    <p class="text-xs text-gray-500">5 minutes ago</p>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">Low stock alert</p>
                                    <p class="text-xs text-gray-500">10 minutes ago</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="px-4 py-2 text-center border-t border-gray-200">
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-500">View all notifications</a>
                    </div>
                </div>
            </div>
            
            <!-- User menu -->
            <div class="relative">
                <button id="user-menu-toggle" class="flex items-center space-x-2 p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">
                            <?php echo e(auth()->user() ? strtoupper(substr(auth()->user()->name, 0, 1)) : 'U'); ?>

                        </span>
                    </div>
                    <span class="hidden md:block text-sm font-medium">
                        <?php echo e(auth()->user()->name ?? 'User'); ?>

                    </span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                
                <!-- User dropdown -->
                <div id="user-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-user mr-2"></i>
                        Profile
                    </a>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-cog mr-2"></i>
                        Settings
                    </a>
                    <div class="border-t border-gray-100"></div>
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
$(document).ready(function() {
    // Toggle notifications dropdown
    $('#notifications-toggle').on('click', function(e) {
        e.stopPropagation();
        $('#notifications-dropdown').toggleClass('hidden');
        $('#user-dropdown').addClass('hidden');
    });
    
    // Toggle user dropdown
    $('#user-menu-toggle').on('click', function(e) {
        e.stopPropagation();
        $('#user-dropdown').toggleClass('hidden');
        $('#notifications-dropdown').addClass('hidden');
    });
    
    // Close dropdowns when clicking outside
    $(document).on('click', function() {
        $('#notifications-dropdown').addClass('hidden');
        $('#user-dropdown').addClass('hidden');
    });
    
    // Prevent dropdown from closing when clicking inside
    $('#notifications-dropdown, #user-dropdown').on('click', function(e) {
        e.stopPropagation();
    });
});
</script><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\resources\views/layouts/main-header.blade.php ENDPATH**/ ?>