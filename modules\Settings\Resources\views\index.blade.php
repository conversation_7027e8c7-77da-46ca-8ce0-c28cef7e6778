@extends('layouts.master')

@section('title', 'إعدادات النظام')

@push('styles')
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">

<style>
.settings-card {
    transition: all 0.3s ease;
    cursor: pointer;
}
.settings-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}
.settings-icon {
    font-size: 3rem;
}
.stats-card {
    transition: all 0.3s ease;
}
.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50" dir="rtl">
    <!-- Page Header with Gradient -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cog text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">إعدادات النظام</h1>
                        <p class="text-indigo-100">إدارة وتكوين إعدادات النظام</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-indigo-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home ml-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-left text-indigo-200 mx-2"></i>
                                <span class="text-white font-medium">الإعدادات</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Settings Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stats-card bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-print text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <h5 class="text-sm font-medium text-gray-500 uppercase tracking-wide">الطابعات</h5>
                    <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ $overview['printer_count'] ?? 0 }}</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        <span class="text-green-600 font-medium">
                            <i class="fas fa-arrow-up ml-1"></i>
                            طابعة مكونة
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-credit-card text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <h5 class="text-sm font-medium text-gray-500 uppercase tracking-wide">طرق الدفع</h5>
                    <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ $overview['payment_methods'] ?? 0 }}</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        <span class="text-green-600 font-medium">
                            <i class="fas fa-arrow-up ml-1"></i>
                            طريقة مفعلة
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 {{ isset($overview['security_enabled']) && $overview['security_enabled'] ? 'bg-green-100' : 'bg-yellow-100' }} rounded-lg flex items-center justify-center">
                        @if(isset($overview['security_enabled']) && $overview['security_enabled'])
                            <i class="fas fa-shield-alt text-green-600 text-xl"></i>
                        @else
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                        @endif
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <h5 class="text-sm font-medium text-gray-500 uppercase tracking-wide">الأمان</h5>
                    <h3 class="text-2xl font-bold text-gray-900 mt-1">
                        @if(isset($overview['security_enabled']) && $overview['security_enabled'])
                            <i class="fas fa-check text-green-600"></i>
                        @else
                            <i class="fas fa-times text-yellow-600"></i>
                        @endif
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">
                        {{ isset($overview['security_enabled']) && $overview['security_enabled'] ? 'مفعل' : 'غير مفعل' }}
                    </p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-store text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <h5 class="text-sm font-medium text-gray-500 uppercase tracking-wide">الفروع</h5>
                    <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ $overview['branches_configured'] ?? 0 }}</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        <span class="text-cyan-600 font-medium">
                            <i class="fas fa-arrow-up ml-1"></i>
                            فرع مكون
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Categories -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="mb-6">
            <h4 class="text-xl font-bold text-gray-800 mb-2">فئات الإعدادات</h4>
            <p class="text-gray-600">اختر الفئة المناسبة لإدارة الإعدادات</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Printer Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.printer.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-print text-blue-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات الطابعات</h5>
                    <p class="text-gray-600 text-sm mb-4">إدارة الطابعات وإعدادات الطباعة</p>
                    <a href="{{ route('settings.printer.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- Payment Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.payment.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-credit-card text-green-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات الدفع</h5>
                    <p class="text-gray-600 text-sm mb-4">إدارة طرق الدفع والبوابات</p>
                    <a href="{{ route('settings.payment.index') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.security.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-shield-alt text-red-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات الأمان</h5>
                    <p class="text-gray-600 text-sm mb-4">إدارة أمان النظام والمصادقة</p>
                    <a href="{{ route('settings.security.index') }}" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- Branch Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.branch.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-store text-yellow-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات الفروع</h5>
                    <p class="text-gray-600 text-sm mb-4">إدارة إعدادات الفروع المختلفة</p>
                    <a href="{{ route('settings.branch.index') }}" class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- System Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.system.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-cyan-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-cogs text-cyan-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات النظام</h5>
                    <p class="text-gray-600 text-sm mb-4">إعدادات النظام العامة</p>
                    <a href="{{ route('settings.system.index') }}" class="inline-flex items-center px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- Restaurant Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.restaurant.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-utensils text-gray-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات المطعم</h5>
                    <p class="text-gray-600 text-sm mb-4">إعدادات خاصة بالمطعم والطاولات</p>
                    <a href="{{ route('settings.restaurant.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- User/Role Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.user.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-users text-purple-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات المستخدمين</h5>
                    <p class="text-gray-600 text-sm mb-4">إدارة المستخدمين والأدوار</p>
                    <a href="{{ route('settings.user.index') }}" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- Report Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.report.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-chart-line text-emerald-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات التقارير</h5>
                    <p class="text-gray-600 text-sm mb-4">إعدادات التقارير والتصدير</p>
                    <a href="{{ route('settings.report.index') }}" class="inline-flex items-center px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>

            <!-- Inventory Settings -->
            <div class="settings-card bg-white border border-gray-200 rounded-lg shadow-md p-6 hover:shadow-lg" data-url="{{ route('settings.inventory.index') }}">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="w-16 h-16 bg-rose-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-boxes text-rose-600 text-2xl"></i>
                        </div>
                    </div>
                    <h5 class="text-lg font-semibold text-gray-800 mb-2">إعدادات المخزون</h5>
                    <p class="text-gray-600 text-sm mb-4">إعدادات إدارة المخزون</p>
                    <a href="{{ route('settings.inventory.index') }}" class="inline-flex items-center px-4 py-2 bg-rose-600 hover:bg-rose-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

<script>
$(document).ready(function() {
    // Add click handlers for settings cards
    $('.settings-card').click(function(e) {
        // Don't trigger if clicking on the link directly
        if (e.target.tagName.toLowerCase() === 'a' || e.target.closest('a')) {
            return;
        }

        const url = $(this).data('url');
        if (url) {
            window.location.href = url;
        }
    });

    // Add hover effects
    $('.settings-card').hover(
        function() {
            $(this).addClass('transform scale-105');
        },
        function() {
            $(this).removeClass('transform scale-105');
        }
    );

    // Add hover effects for stats cards
    $('.stats-card').hover(
        function() {
            $(this).addClass('transform scale-105');
        },
        function() {
            $(this).removeClass('transform scale-105');
        }
    );

    // Show success message if redirected from settings update
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('updated') === 'true') {
        Swal.fire({
            title: 'تم تحديث الإعدادات بنجاح!',
            text: 'تم حفظ التغييرات بنجاح',
            icon: 'success',
            confirmButtonText: 'حسناً',
            confirmButtonColor: '#10b981'
        });
    }

    console.log('Settings dashboard loaded successfully');
});
</script>
@endpush
