@extends('layouts.master')

@section('title', 'Kitchen Management')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Workload indicator styles */
    .workload-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
    }
    
    .workload-low {
        background-color: #10b981; /* green */
    }
    
    .workload-medium {
        background-color: #f59e0b; /* yellow */
    }
    
    .workload-high {
        background-color: #ef4444; /* red */
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-900">Kitchen Management</h1>
        <nav class="flex mt-2" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="#" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <i class="fas fa-home mr-2"></i>
                        Dashboard
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-sm font-medium text-gray-500">Kitchen Management</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
    <div class="mt-4 sm:mt-0">
        <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200" id="add-kitchen-btn">
            <i class="fas fa-plus mr-2"></i>
            Add Kitchen
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <div class="bg-blue-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium">Total Kitchens</p>
                    <p class="text-white text-3xl font-bold" id="total-kitchens">0</p>
                </div>
                <div class="bg-blue-500 rounded-full p-3">
                    <i class="fas fa-utensils text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-green-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium">Active Kitchens</p>
                    <p class="text-white text-3xl font-bold" id="active-kitchens">0</p>
                </div>
                <div class="bg-green-500 rounded-full p-3">
                    <i class="fas fa-check-circle text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-yellow-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-yellow-100 text-sm font-medium">Operating Now</p>
                    <p class="text-white text-3xl font-bold" id="operating-kitchens">0</p>
                </div>
                <div class="bg-yellow-500 rounded-full p-3">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-purple-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium">Active KOTs</p>
                    <p class="text-white text-3xl font-bold" id="active-kots">0</p>
                </div>
                <div class="bg-purple-500 rounded-full p-3">
                    <i class="fas fa-receipt text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label for="filter_station_type" class="block text-sm font-medium text-gray-700 mb-2">Station Type</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filter_station_type">
                    <option value="">All Station Types</option>
                    <option value="hot">Hot Station</option>
                    <option value="cold">Cold Station</option>
                    <option value="grill">Grill Station</option>
                    <option value="fryer">Fryer Station</option>
                    <option value="salad">Salad Station</option>
                    <option value="dessert">Dessert Station</option>
                    <option value="beverage">Beverage Station</option>
                    <option value="prep">Prep Station</option>
                    <option value="main">Main Kitchen</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div>
                <label for="filter_status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filter_status">
                    <option value="">All Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>
            </div>
            <div>
                <label for="filter_manager" class="block text-sm font-medium text-gray-700 mb-2">Manager</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filter_manager">
                    <option value="">All Managers</option>
                    @foreach($managers as $manager)
                        <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="flex justify-end mt-4">
            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="reset-filters">
                <i class="fas fa-undo mr-2"></i>Reset Filters
            </button>
        </div>
    </div>
</div>

<!-- Kitchens Table -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Kitchen Management</h3>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full" id="kitchens-table" data-page-length='25'>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Station Type</th>
                        <th>Branch</th>
                        <th>Manager</th>
                        <th>Status</th>
                        <th>Operating</th>
                        <th>Workload</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Kitchen Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden" id="addKitchenModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-lg bg-white">
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Add New Kitchen</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" id="closeAddModal">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="addKitchenForm" class="mt-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Kitchen Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="name" name="name" required>
                </div>

                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">Kitchen Code</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="code" name="code">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="station_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Station Type <span class="text-red-500">*</span>
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="station_type" name="station_type" required>
                        <option value="">Select Station Type</option>
                        <option value="hot">Hot Station</option>
                        <option value="cold">Cold Station</option>
                        <option value="grill">Grill Station</option>
                        <option value="fryer">Fryer Station</option>
                        <option value="salad">Salad Station</option>
                        <option value="dessert">Dessert Station</option>
                        <option value="beverage">Beverage Station</option>
                        <option value="prep">Prep Station</option>
                        <option value="main">Main Kitchen</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div>
                    <label for="manager_id" class="block text-sm font-medium text-gray-700 mb-2">Manager</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="manager_id" name="manager_id">
                        <option value="">Select Manager</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="description" name="description" rows="3"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="max_concurrent_orders" class="block text-sm font-medium text-gray-700 mb-2">Max Concurrent Orders</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="max_concurrent_orders" name="max_concurrent_orders" min="1" max="100">
                </div>

                <div>
                    <label for="average_prep_time_minutes" class="block text-sm font-medium text-gray-700 mb-2">Avg Prep Time (minutes)</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="average_prep_time_minutes" name="average_prep_time_minutes" min="1" max="300">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="display_order" class="block text-sm font-medium text-gray-700 mb-2">Display Order</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="display_order" name="display_order" min="0">
                </div>

                <div class="flex items-center mt-8">
                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="is_active" name="is_active" checked>
                    <label for="is_active" class="ml-2 block text-sm text-gray-700">Active</label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="cancelAddModal">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    Create Kitchen
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Kitchen Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden" id="editKitchenModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-lg bg-white">
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Edit Kitchen</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" id="closeEditModal">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="editKitchenForm" class="mt-6">
            <input type="hidden" id="edit_kitchen_id" name="kitchen_id">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Kitchen Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_name" name="name" required>
                </div>

                <div>
                    <label for="edit_code" class="block text-sm font-medium text-gray-700 mb-2">Kitchen Code</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_code" name="code">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="edit_station_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Station Type <span class="text-red-500">*</span>
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_station_type" name="station_type" required>
                        <option value="">Select Station Type</option>
                        <option value="hot">Hot Station</option>
                        <option value="cold">Cold Station</option>
                        <option value="grill">Grill Station</option>
                        <option value="fryer">Fryer Station</option>
                        <option value="salad">Salad Station</option>
                        <option value="dessert">Dessert Station</option>
                        <option value="beverage">Beverage Station</option>
                        <option value="prep">Prep Station</option>
                        <option value="main">Main Kitchen</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div>
                    <label for="edit_manager_id" class="block text-sm font-medium text-gray-700 mb-2">Manager</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_manager_id" name="manager_id">
                        <option value="">Select Manager</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="mt-6">
                <label for="edit_description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_description" name="description" rows="3"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="edit_max_concurrent_orders" class="block text-sm font-medium text-gray-700 mb-2">Max Concurrent Orders</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_max_concurrent_orders" name="max_concurrent_orders" min="1" max="100">
                </div>

                <div>
                    <label for="edit_average_prep_time_minutes" class="block text-sm font-medium text-gray-700 mb-2">Avg Prep Time (minutes)</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_average_prep_time_minutes" name="average_prep_time_minutes" min="1" max="300">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="edit_display_order" class="block text-sm font-medium text-gray-700 mb-2">Display Order</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_display_order" name="display_order" min="0">
                </div>

                <div class="flex items-center mt-8">
                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="edit_is_active" name="is_active">
                    <label for="edit_is_active" class="ml-2 block text-sm text-gray-700">Active</label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="cancelEditModal">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    Update Kitchen
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Menu Items Assignment Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden" id="menuItemsModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-lg bg-white">
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900" id="menuItemsModalTitle">Assign Menu Items to Kitchen</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" id="closeMenuItemsModal">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="mt-6">
            <input type="hidden" id="menu_items_kitchen_id">

            <!-- Available Menu Items Section -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-md font-semibold text-gray-900">Available Menu Items</h4>
                    <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="assignSelectedItems">
                        <i class="fas fa-plus mr-2"></i>Assign Selected
                    </button>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full" id="available-menu-items-table">
                        <thead>
                            <tr>
                                <th class="px-4 py-2">
                                    <input type="checkbox" id="selectAllAvailable" class="rounded">
                                </th>
                                <th class="px-4 py-2 text-left">Name</th>
                                <th class="px-4 py-2 text-left">Category</th>
                                <th class="px-4 py-2 text-left">Price</th>
                                <th class="px-4 py-2 text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Assigned Menu Items Section -->
            <div>
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-md font-semibold text-gray-900">Assigned Menu Items</h4>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full" id="assigned-menu-items-table">
                        <thead>
                            <tr>
                                <th class="px-4 py-2 text-left">Name</th>
                                <th class="px-4 py-2 text-left">Category</th>
                                <th class="px-4 py-2 text-left">Prep Time (min)</th>
                                <th class="px-4 py-2 text-left">Priority</th>
                                <th class="px-4 py-2 text-left">Status</th>
                                <th class="px-4 py-2 text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="closeMenuItemsModalBtn">
                Close
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>



<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let table;

    // Initialize DataTable with server-side processing
    function initializeDataTable() {
        console.log('Initializing DataTable...');

        table = $('#kitchens-table').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: '{{ route("kitchens.index") }}',
                data: function(d) {
                    d.station_type = $('#filter_station_type').val();
                    d.status = $('#filter_status').val();
                    d.manager_id = $('#filter_manager').val();
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                error: function(xhr, error, code) {
                    console.error('DataTable AJAX Error:', error, code, xhr);
                    if (xhr.responseJSON) {
                        console.error('Response:', xhr.responseJSON);
                    }
                },
                dataSrc: function(json) {
                    // Debug the response structure
                    console.log('DataTables Response:', json);
                    
                    // Ensure each row has the correct number of columns
                    if (json.data && Array.isArray(json.data)) {
                        json.data.forEach(function(row, index) {
                            // Ensure all expected properties exist
                            if (!row.hasOwnProperty('DT_RowIndex')) {
                                row.DT_RowIndex = index + 1;
                            }
                            if (!row.hasOwnProperty('name')) row.name = '';
                            if (!row.hasOwnProperty('station_type')) row.station_type = '';
                            if (!row.hasOwnProperty('branch_name')) row.branch_name = '';
                            if (!row.hasOwnProperty('manager_name')) row.manager_name = '';
                            if (!row.hasOwnProperty('status')) row.status = '';
                            if (!row.hasOwnProperty('operating_status')) row.operating_status = '';
                            if (!row.hasOwnProperty('workload')) row.workload = '';
                            if (!row.hasOwnProperty('actions')) row.actions = '';
                        });
                    }
                    
                    return json.data;
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false, defaultContent: '' },
                { data: 'name', name: 'name', defaultContent: '' },
                { data: 'station_type', name: 'station_type', defaultContent: '' },
                { data: 'branch_name', name: 'branch.name', defaultContent: '' },
                { data: 'manager_name', name: 'manager.name', defaultContent: '' },
                { data: 'status', name: 'is_active', defaultContent: '' },
                { data: 'operating_status', name: 'operating_status', defaultContent: '' },
                { data: 'workload', name: 'workload', defaultContent: '' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false, defaultContent: '' }
            ],

            order: [[1, 'asc']], // Order by name ascending
            
            columnDefs: [
                {
                    targets: [5, 6, 7, 8], // status, operating_status, workload, actions columns
                    className: 'text-center'
                }
            ],
         
            initComplete: function() {
                console.log('DataTable initialized successfully');
                // Apply custom styling after DataTable initialization
                $('.dataTables_wrapper').addClass('bg-white rounded-lg shadow-sm');
            },
            
            drawCallback: function(settings) {
                console.log('DataTable draw completed');
            }
        });
    }

    // Initialize table
    initializeDataTable();

    // Filter change events
    $('#filter_station_type, #filter_status, #filter_manager').change(function() {
        table.ajax.reload();
    });

    // Reset filters
    $('#reset-filters').click(function() {
        $('#filter_station_type').val('');
        $('#filter_status').val('');
        $('#filter_manager').val('');
        table.ajax.reload();
    });

    // Load initial statistics
    updateStatistics();

    // Update statistics function
    function updateStatistics() {
        $.ajax({
            url: '{{ route("kitchens.index") }}',
            method: 'GET',
            data: { view: 'cards' },
            success: function(response) {
                if (response.success && response.data) {
                    const kitchens = response.data;
                    const totalKitchens = kitchens.length;
                    const activeKitchens = kitchens.filter(k => k.is_active).length;
                    const operatingKitchens = kitchens.filter(k => k.is_operating).length;
                    const totalWorkload = kitchens.reduce((sum, k) => sum + (k.current_workload || 0), 0);

                    $('#total-kitchens').text(totalKitchens);
                    $('#active-kitchens').text(activeKitchens);
                    $('#operating-kitchens').text(operatingKitchens);
                    $('#active-kots').text(totalWorkload);
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
            }
        });
    }

    // Modal functionality
    function showModal(modalId) {
        $('#' + modalId).removeClass('hidden');
    }

    function hideModal(modalId) {
        $('#' + modalId).addClass('hidden');
    }

    // Global openModal function for compatibility
    window.openModal = function(modalId) {
        showModal(modalId);
    };

    // Clear form errors
    function clearErrors() {
        $('.text-red-500').remove();
        $('.border-red-500').removeClass('border-red-500');
    }

    // Add Kitchen Modal functionality
    $('#add-kitchen-btn').on('click', function() {
        $('#addKitchenForm')[0].reset();
        clearErrors();
        showModal('addKitchenModal');
    });

    // Close modal handlers
    $('#closeAddModal, #cancelAddModal').on('click', function() {
        hideModal('addKitchenModal');
        resetAddForm();
    });

    $('#closeEditModal, #cancelEditModal').on('click', function() {
        hideModal('editKitchenModal');
        resetEditForm();
    });

    // Close modal when clicking outside
    $('#addKitchenModal, #editKitchenModal').on('click', function(e) {
        if (e.target === this) {
            hideModal(this.id);
            if (this.id === 'addKitchenModal') {
                resetAddForm();
            } else {
                resetEditForm();
            }
        }
    });

    // Handle Add Kitchen Form Submission
    $('#addKitchenForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Creating...');

        const formData = new FormData(this);
        
        // Convert checkbox value to boolean
        formData.set('is_active', $('#is_active').is(':checked') ? '1' : '0');

        $.ajax({
            url: '{{ route("kitchens.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Kitchen created successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6'
                    });

                    // Reset form and close modal
                    $('#addKitchenForm')[0].reset();
                    hideModal('addKitchenModal');

                    // Refresh table and statistics
                    table.draw();
                    updateStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to create kitchen',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error creating kitchen:', xhr);
                let errorMessage = "Failed to create kitchen";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Edit Kitchen functionality
    $(document).on('click', '.edit-kitchen', function() {
        const kitchenId = $(this).data('id');

        // Fetch kitchen data
        $.ajax({
            url: '{{ route("kitchens.show", ":id") }}'.replace(':id', kitchenId),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const kitchen = response.data;

                    // Populate edit form
                    $('#edit_kitchen_id').val(kitchen.id);
                    $('#edit_name').val(kitchen.name);
                    $('#edit_code').val(kitchen.code);
                    $('#edit_station_type').val(kitchen.station_type);
                    $('#edit_description').val(kitchen.description);
                    $('#edit_max_concurrent_orders').val(kitchen.max_concurrent_orders);
                    $('#edit_average_prep_time_minutes').val(kitchen.average_prep_time_minutes);
                    $('#edit_manager_id').val(kitchen.manager_id);
                    $('#edit_display_order').val(kitchen.display_order);
                    $('#edit_is_active').prop('checked', kitchen.is_active == 1);

                    // Show modal
                    showModal('editKitchenModal');
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to fetch kitchen data',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error fetching kitchen:', xhr);
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to fetch kitchen data',
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            }
        });
    });

    // Handle Edit Kitchen Form Submission
    $('#editKitchenForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Updating...');

        const formData = new FormData(this);
        formData.append('_method', 'PUT');
        
        // Convert checkbox value to boolean
        formData.set('is_active', $('#edit_is_active').is(':checked') ? '1' : '0');
        
        const kitchenId = $('#edit_kitchen_id').val();

        $.ajax({
            url: '{{ route("kitchens.update", ":id") }}'.replace(':id', kitchenId),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Kitchen updated successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6'
                    });

                    // Reset form and close modal
                    $('#editKitchenForm')[0].reset();
                    hideModal('editKitchenModal');

                    // Refresh table and statistics
                    table.draw();
                    updateStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to update kitchen',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error updating kitchen:', xhr);
                let errorMessage = "Failed to update kitchen";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Delete Kitchen functionality
    $(document).on('click', '.delete-kitchen', function() {
        const kitchenId = $(this).data('id');
        const kitchenName = $(this).data('name');

        Swal.fire({
            title: 'Are you sure?',
            text: `You want to delete kitchen "${kitchenName}"? This action cannot be undone!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("kitchens.destroy", ":id") }}'.replace(':id', kitchenId),
                    method: 'POST',
                    data: {
                        _method: 'DELETE'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Deleted!',
                                text: 'Kitchen has been deleted successfully',
                                icon: 'success',
                                confirmButtonColor: '#3b82f6'
                            });
                            table.draw();
                            updateStatistics();
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Failed to delete kitchen',
                                icon: 'error',
                                confirmButtonColor: '#3b82f6'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Error deleting kitchen:', xhr);
                        let errorMessage = "Failed to delete kitchen";

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonColor: '#3b82f6'
                        });
                    }
                });
            }
        });
    });

    // Menu Items Assignment functionality
    $(document).on('click', '.assign-menu-items', function() {
        const kitchenId = $(this).data('id');
        const kitchenName = $(this).data('name');

        $('#menu_items_kitchen_id').val(kitchenId);
        $('#menuItemsModalTitle').text(`Assign Menu Items to ${kitchenName}`);

        // Load available and assigned menu items
        loadAvailableMenuItems(kitchenId);
        loadAssignedMenuItems(kitchenId);

        showModal('menuItemsModal');
    });

    // Close menu items modal
    $('#closeMenuItemsModal, #closeMenuItemsModalBtn').on('click', function() {
        hideModal('menuItemsModal');
    });

    // Load available menu items
    function loadAvailableMenuItems(kitchenId) {
        $.ajax({
            url: `{{ route("kitchens.available-menu-items", ":id") }}`.replace(':id', kitchenId),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    displayAvailableMenuItems(response.data);
                }
            },
            error: function(xhr) {
                console.error('Error loading available menu items:', xhr);
            }
        });
    }

    // Load assigned menu items
    function loadAssignedMenuItems(kitchenId) {
        if (!window.assignedMenuItemsTable) {
            window.assignedMenuItemsTable = $('#assigned-menu-items-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: `{{ route("kitchens.menu-items", ":id") }}`.replace(':id', kitchenId),
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    { data: 'menu_item_name', name: 'menuItem.name' },
                    { data: 'category_name', name: 'menuItem.category.name' },
                    { data: 'prep_time_minutes', name: 'prep_time_minutes' },
                    { data: 'priority_level', name: 'priority_level' },
                    { data: 'status', name: 'is_active' },
                    { data: 'actions', name: 'actions', orderable: false, searchable: false }
                ],
                pageLength: 10,
                searching: false,
                lengthChange: false
            });
        } else {
            window.assignedMenuItemsTable.ajax.url(`{{ route("kitchens.menu-items", ":id") }}`.replace(':id', kitchenId)).load();
        }
    }

    // Display available menu items
    function displayAvailableMenuItems(menuItems) {
        const tbody = $('#available-menu-items-table tbody');
        tbody.empty();

        menuItems.forEach(function(item) {
            const row = `
                <tr>
                    <td class="px-4 py-2">
                        <input type="checkbox" class="available-item-checkbox rounded" value="${item.id}">
                    </td>
                    <td class="px-4 py-2">${item.name}</td>
                    <td class="px-4 py-2">${item.category}</td>
                    <td class="px-4 py-2">$${item.price}</td>
                    <td class="px-4 py-2">
                        <button type="button" class="px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors assign-single-item" data-id="${item.id}">
                            <i class="fas fa-plus mr-1"></i>Assign
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // Select all available items
    $('#selectAllAvailable').on('change', function() {
        $('.available-item-checkbox').prop('checked', this.checked);
    });

    // Assign selected items
    $('#assignSelectedItems').on('click', function() {
        const selectedItems = $('.available-item-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedItems.length === 0) {
            Swal.fire({
                title: 'No Items Selected',
                text: 'Please select at least one menu item to assign.',
                icon: 'warning',
                confirmButtonColor: '#3b82f6'
            });
            return;
        }

        assignMenuItems(selectedItems);
    });

    // Assign single item
    $(document).on('click', '.assign-single-item', function() {
        const itemId = $(this).data('id');
        assignMenuItems([itemId]);
    });

    // Assign menu items function
    function assignMenuItems(menuItemIds) {
        const kitchenId = $('#menu_items_kitchen_id').val();

        $.ajax({
            url: `{{ route("kitchens.assign-menu-item", ":id") }}`.replace(':id', kitchenId),
            method: 'POST',
            data: {
                menu_item_ids: menuItemIds,
                prep_time_minutes: 15, // Default prep time
                priority_level: 'medium' // Default priority
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Menu items assigned successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6'
                    });

                    // Reload both tables
                    loadAvailableMenuItems(kitchenId);
                    if (window.assignedMenuItemsTable) {
                        window.assignedMenuItemsTable.ajax.reload();
                    }
                }
            },
            error: function(xhr) {
                console.error('Error assigning menu items:', xhr);
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to assign menu items',
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            }
        });
    }

    // Remove menu item from kitchen
    $(document).on('click', '.remove-menu-item', function() {
        const kitchenId = $(this).data('kitchen-id');
        const menuItemId = $(this).data('menu-item-id');
        const menuItemName = $(this).data('menu-item-name');

        Swal.fire({
            title: 'Are you sure?',
            text: `You want to remove "${menuItemName}" from this kitchen?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, remove it!',
            cancelButtonText: 'No, cancel!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/kitchens/${kitchenId}/menu-items/${menuItemId}`,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Removed!',
                                text: 'Menu item has been removed from kitchen successfully',
                                icon: 'success',
                                confirmButtonColor: '#3b82f6'
                            });

                            // Reload both tables if in modal
                            if ($('#menuItemsModal').is(':visible')) {
                                loadAvailableMenuItems(kitchenId);
                                if (window.assignedMenuItemsTable) {
                                    window.assignedMenuItemsTable.ajax.reload();
                                }
                            }
                            
                            // Reload main table if on show page
                            if (window.location.pathname.includes('/kitchens/')) {
                                location.reload();
                            }
                            
                            // Reload main kitchens table
                            if (window.kitchensTable) {
                                window.kitchensTable.ajax.reload();
                            }
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Failed to remove menu item',
                                icon: 'error',
                                confirmButtonColor: '#3b82f6'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Error removing menu item:', xhr);
                        let errorMessage = "Failed to remove menu item";

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonColor: '#3b82f6'
                        });
                    }
                });
            }
        });
    });

    // Reset forms when modals are closed
    function resetAddForm() {
        $('#addKitchenForm')[0].reset();
    }

    function resetEditForm() {
        $('#editKitchenForm')[0].reset();
    }

    // Reset forms when modals are hidden
    $('#addKitchenModal').on('click', function(e) {
        if (e.target === this) {
            resetAddForm();
        }
    });

    $('#editKitchenModal').on('click', function(e) {
        if (e.target === this) {
            resetEditForm();
        }
    });

    $('#menuItemsModal').on('click', function(e) {
        if (e.target === this) {
            hideModal('menuItemsModal');
        }
    });
});
</script>
@endpush
