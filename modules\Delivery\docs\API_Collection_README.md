# Delivery Module API Collection

This document provides comprehensive documentation for all API endpoints in the Delivery module. The module manages delivery operations, personnel, zones, tracking, and analytics for restaurant orders.

## Table of Contents

1. [Authentication](#authentication)
2. [Delivery Assignments](#delivery-assignments)
3. [Delivery Personnel](#delivery-personnel)
4. [Delivery Zones](#delivery-zones)
5. [Delivery Tracking](#delivery-tracking)
6. [Delivery Analytics](#delivery-analytics)
7. [Response Formats](#response-formats)
8. [Error Handling](#error-handling)

## Authentication

All API endpoints require authentication using Bearer tokens. Include the token in the Authorization header:

```
Authorization: Bearer {your-token}
```

## Delivery Assignments

### List Delivery Assignments

**GET** `/api/delivery/assignments`

Retrieve a paginated list of delivery assignments with optional filtering.

**Query Parameters:**
- `status` (string): Filter by delivery status (pending, assigned, picked_up, in_transit, delivered, cancelled, failed)
- `personnel_id` (integer): Filter by delivery personnel ID
- `zone_id` (integer): Filter by delivery zone ID
- `date_from` (date): Filter assignments from this date
- `date_to` (date): Filter assignments to this date
- `priority` (string): Filter by priority (low, normal, high, urgent)
- `search` (string): Search in delivery address or tracking code
- `per_page` (integer): Number of items per page (default: 15)
- `page` (integer): Page number

**Example Request:**
```bash
GET /api/delivery/assignments?status=in_transit&per_page=20&page=1
```

**Example Response:**
```json
{
  "data": [
    {
      "id": 1,
      "order_id": 123,
      "delivery_personnel_id": 5,
      "delivery_zone_id": 2,
      "status": "in_transit",
      "tracking_code": "DEL-2024-001",
      "delivery_address": "123 Main St, City",
      "delivery_fee": 5.99,
      "estimated_delivery_time": 30,
      "status_label": "In Transit",
      "is_overdue": false,
      "can_be_cancelled": false,
      "delivery_personnel": {
        "id": 5,
        "user": {
          "name": "John Doe",
          "phone": "+**********"
        },
        "vehicle_type": "motorcycle"
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total": 45
  }
}
```

### Create Delivery Assignment

**POST** `/api/delivery/assignments`

Create a new delivery assignment for an order.

**Request Body:**
```json
{
  "order_id": 123,
  "delivery_personnel_id": 5,
  "delivery_zone_id": 2,
  "pickup_time": "2024-01-15 14:30:00",
  "delivery_time": "2024-01-15 15:00:00",
  "delivery_address": "123 Main St, City",
  "delivery_latitude": 40.7128,
  "delivery_longitude": -74.0060,
  "delivery_fee": 5.99,
  "estimated_delivery_time": 30,
  "special_instructions": "Ring doorbell twice",
  "priority": "normal",
  "auto_assign": false
}
```

### Get Delivery Assignment

**GET** `/api/delivery/assignments/{id}`

Retrieve a specific delivery assignment by ID.

**Example Response:**
```json
{
  "data": {
    "id": 1,
    "order_id": 123,
    "status": "in_transit",
    "tracking_code": "DEL-2024-001",
    "delivery_address": "123 Main St, City",
    "delivery_personnel": {
      "id": 5,
      "user": {
        "name": "John Doe"
      }
    },
    "tracking": [
      {
        "latitude": 40.7128,
        "longitude": -74.0060,
        "recorded_at": "2024-01-15 14:45:00"
      }
    ]
  }
}
```

### Update Delivery Status

**PUT** `/api/delivery/assignments/{id}/status`

Update the status of a delivery assignment.

**Request Body:**
```json
{
  "status": "delivered",
  "notes": "Delivered successfully",
  "delivery_time": "2024-01-15 15:15:00",
  "customer_signature": "base64-encoded-signature",
  "delivery_photo": "base64-encoded-photo",
  "customer_rating": 5,
  "customer_feedback": "Great service!",
  "tip_amount": 2.50
}
```

### Auto-Assign Delivery

**POST** `/api/delivery/assignments/{id}/auto-assign`

Automatically assign the delivery to the best available personnel.

**Request Body:**
```json
{
  "criteria": "distance",
  "max_distance": 10
}
```

### Calculate Delivery Fee

**POST** `/api/delivery/assignments/calculate-fee`

Calculate delivery fee for given coordinates and order details.

**Request Body:**
```json
{
  "latitude": 40.7128,
  "longitude": -74.0060,
  "order_amount": 25.99,
  "branch_id": 1
}
```

### Get Delivery Statistics

**GET** `/api/delivery/assignments/statistics`

Get delivery statistics with optional filtering.

**Query Parameters:**
- `period` (string): today, week, month, year
- `personnel_id` (integer): Filter by personnel
- `zone_id` (integer): Filter by zone

## Delivery Personnel

### List Delivery Personnel

**GET** `/api/delivery/personnel`

Retrieve a list of delivery personnel with optional filtering.

**Query Parameters:**
- `status` (string): Filter by status (active, inactive, busy, offline, on_break, suspended)
- `branch_id` (integer): Filter by branch
- `available_only` (boolean): Show only available personnel
- `search` (string): Search by name or phone
- `per_page` (integer): Items per page

### Create Delivery Personnel

**POST** `/api/delivery/personnel`

Create a new delivery personnel record.

**Request Body:**
```json
{
  "user_id": 10,
  "branch_id": 1,
  "vehicle_type": "motorcycle",
  "vehicle_model": "Honda CBR",
  "vehicle_plate_number": "ABC-123",
  "license_number": "DL123456789",
  "phone_number": "+**********",
  "emergency_contact": "Jane Doe",
  "emergency_phone": "+**********",
  "max_concurrent_deliveries": 3,
  "delivery_radius_km": 15,
  "hourly_rate": 15.00,
  "commission_rate": 10.0,
  "working_hours": {
    "monday": {"enabled": true, "start": "09:00", "end": "18:00"},
    "tuesday": {"enabled": true, "start": "09:00", "end": "18:00"}
  }
}
```

### Update Personnel Location

**PUT** `/api/delivery/personnel/{id}/location`

Update the current location of delivery personnel.

**Request Body:**
```json
{
  "latitude": 40.7128,
  "longitude": -74.0060,
  "accuracy": 5.0,
  "speed": 25.5,
  "heading": 180.0,
  "battery_level": 85
}
```

### Get Personnel Performance

**GET** `/api/delivery/personnel/{id}/performance`

Get performance metrics for a specific personnel.

**Query Parameters:**
- `period` (string): today, week, month, year

## Delivery Zones

### List Delivery Zones

**GET** `/api/delivery/zones`

Retrieve a list of delivery zones.

**Query Parameters:**
- `branch_id` (integer): Filter by branch
- `active_only` (boolean): Show only active zones
- `search` (string): Search by name

### Create Delivery Zone

**POST** `/api/delivery/zones`

Create a new delivery zone.

**Request Body:**
```json
{
  "name": "Downtown Area",
  "branch_id": 1,
  "coordinates": [
    {"latitude": 40.7128, "longitude": -74.0060},
    {"latitude": 40.7589, "longitude": -73.9851},
    {"latitude": 40.7505, "longitude": -73.9934}
  ],
  "delivery_fee": 5.99,
  "minimum_order_amount": 15.00,
  "estimated_delivery_time": 30,
  "description": "Downtown delivery zone",
  "color": "#FF5733"
}
```

### Find Zone by Coordinates

**POST** `/api/delivery/zones/find-by-coordinates`

Find which delivery zone contains the given coordinates.

**Request Body:**
```json
{
  "latitude": 40.7128,
  "longitude": -74.0060
}
```

### Check Delivery Availability

**POST** `/api/delivery/zones/check-availability`

Check if delivery is available for given coordinates.

**Request Body:**
```json
{
  "latitude": 40.7128,
  "longitude": -74.0060,
  "branch_id": 1
}
```

## Delivery Tracking

### Update Tracking Location

**POST** `/api/delivery/tracking/location`

Record a new location update for delivery tracking.

**Request Body:**
```json
{
  "assignment_id": 1,
  "latitude": 40.7128,
  "longitude": -74.0060,
  "accuracy": 5.0,
  "speed": 25.5,
  "heading": 180.0,
  "altitude": 10.0,
  "battery_level": 85,
  "notes": "Approaching destination"
}
```

### Get Tracking History

**GET** `/api/delivery/tracking/{assignment_id}/history`

Get tracking history for a delivery assignment.

**Query Parameters:**
- `limit` (integer): Number of records to return
- `from` (datetime): Start time filter
- `to` (datetime): End time filter

### Get Real-time Tracking

**GET** `/api/delivery/tracking/{assignment_id}/realtime`

Get real-time tracking data for customers.

**Example Response:**
```json
{
  "data": {
    "assignment_id": 1,
    "status": "in_transit",
    "current_location": {
      "latitude": 40.7128,
      "longitude": -74.0060,
      "accuracy": 5.0,
      "updated_at": "2024-01-15 14:45:00"
    },
    "estimated_arrival": "2024-01-15 15:10:00",
    "distance_remaining": 2.5,
    "personnel": {
      "name": "John Doe",
      "phone": "+**********",
      "vehicle_type": "motorcycle"
    }
  }
}
```

### Calculate ETA

**GET** `/api/delivery/tracking/{assignment_id}/eta`

Calculate estimated time of arrival for a delivery.

### Optimize Route

**POST** `/api/delivery/tracking/optimize-route`

Optimize delivery route for multiple assignments.

**Request Body:**
```json
{
  "personnel_id": 5,
  "assignment_ids": [1, 2, 3, 4],
  "start_location": {
    "latitude": 40.7128,
    "longitude": -74.0060
  }
}
```

## Delivery Analytics

### Get Overview Analytics

**GET** `/api/delivery/analytics/overview`

Get overview analytics for deliveries.

**Query Parameters:**
- `period` (string): today, week, month, year, custom
- `date_from` (date): Start date for custom period
- `date_to` (date): End date for custom period
- `branch_id` (integer): Filter by branch

### Get Performance Analytics

**GET** `/api/delivery/analytics/performance`

Get performance metrics and KPIs.

### Get Personnel Analytics

**GET** `/api/delivery/analytics/personnel`

Get personnel performance analytics.

### Get Zone Analytics

**GET** `/api/delivery/analytics/zones`

Get zone performance analytics.

### Get Trend Analytics

**GET** `/api/delivery/analytics/trends`

Get trend analysis data.

**Query Parameters:**
- `type` (string): daily, hourly, weekly
- `period` (string): last_7_days, last_30_days, last_3_months

### Get Real-time Dashboard

**GET** `/api/delivery/analytics/dashboard`

Get real-time dashboard data.

### Export Analytics Data

**GET** `/api/delivery/analytics/export`

Export analytics data in various formats.

**Query Parameters:**
- `format` (string): csv, excel, pdf
- `type` (string): overview, performance, personnel, zones
- `period` (string): Date range

## Response Formats

### Success Response

```json
{
  "data": {
    // Resource data
  },
  "message": "Success message",
  "status": "success"
}
```

### Paginated Response

```json
{
  "data": [
    // Array of resources
  ],
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "last_page": 7
  },
  "links": {
    "first": "http://example.com/api/resource?page=1",
    "last": "http://example.com/api/resource?page=7",
    "prev": null,
    "next": "http://example.com/api/resource?page=2"
  }
}
```

## Error Handling

### Validation Error (422)

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "field_name": [
      "The field is required."
    ]
  },
  "status": "error"
}
```

### Not Found Error (404)

```json
{
  "message": "Resource not found.",
  "status": "error"
}
```

### Unauthorized Error (401)

```json
{
  "message": "Unauthenticated.",
  "status": "error"
}
```

### Server Error (500)

```json
{
  "message": "Internal server error.",
  "status": "error"
}
```

## Status Codes

The API uses standard HTTP status codes:

- `200` - OK: Request successful
- `201` - Created: Resource created successfully
- `400` - Bad Request: Invalid request data
- `401` - Unauthorized: Authentication required
- `403` - Forbidden: Access denied
- `404` - Not Found: Resource not found
- `422` - Unprocessable Entity: Validation failed
- `500` - Internal Server Error: Server error

## Rate Limiting

API requests are rate limited to prevent abuse:

- **Standard endpoints**: 60 requests per minute
- **Tracking endpoints**: 120 requests per minute
- **Analytics endpoints**: 30 requests per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## Webhooks

The delivery module supports webhooks for real-time notifications:

### Delivery Status Changed

Triggered when a delivery status changes.

**Payload:**
```json
{
  "event": "delivery.status_changed",
  "data": {
    "assignment_id": 1,
    "old_status": "picked_up",
    "new_status": "in_transit",
    "timestamp": "2024-01-15 14:45:00"
  }
}
```

### Personnel Location Updated

Triggered when personnel location is updated.

**Payload:**
```json
{
  "event": "personnel.location_updated",
  "data": {
    "personnel_id": 5,
    "latitude": 40.7128,
    "longitude": -74.0060,
    "timestamp": "2024-01-15 14:45:00"
  }
}
```

## SDK Examples

### JavaScript/Node.js

```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'https://your-api.com/api',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json'
  }
});

// Get delivery assignments
const assignments = await api.get('/delivery/assignments', {
  params: { status: 'in_transit', per_page: 20 }
});

// Update delivery status
await api.put('/delivery/assignments/1/status', {
  status: 'delivered',
  notes: 'Delivered successfully'
});
```

### PHP

```php
use GuzzleHttp\Client;

$client = new Client([
    'base_uri' => 'https://your-api.com/api/',
    'headers' => [
        'Authorization' => 'Bearer your-token',
        'Content-Type' => 'application/json'
    ]
]);

// Get delivery assignments
$response = $client->get('delivery/assignments', [
    'query' => ['status' => 'in_transit', 'per_page' => 20]
]);

// Update delivery status
$client->put('delivery/assignments/1/status', [
    'json' => [
        'status' => 'delivered',
        'notes' => 'Delivered successfully'
    ]
]);
```

## Testing

Use the provided Postman collection or test with curl:

```bash
# Get delivery assignments
curl -X GET "https://your-api.com/api/delivery/assignments" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"

# Create delivery assignment
curl -X POST "https://your-api.com/api/delivery/assignments" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": 123,
    "delivery_address": "123 Main St",
    "delivery_latitude": 40.7128,
    "delivery_longitude": -74.0060
  }'
```

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.yourapi.com
- Status Page: https://status.yourapi.com

---

**Note**: This API documentation is for the Delivery module which uses order status management instead of separate assignment statuses. All delivery operations are tied to order status updates to maintain consistency across the system.