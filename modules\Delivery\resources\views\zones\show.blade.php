@extends('layouts.master')

@section('content')
<!-- <PERSON> Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900">إدارة مناطق التوصيل</h1>
            <p class="text-sm text-gray-600 mt-1">تفاصيل منطقة: {{ $zone->name }}</p>
        </div>
        <div class="flex space-x-2">
            <a href="{{ route('delivery.zones.edit', $zone->id) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-edit mr-2"></i>
                تعديل
            </a>
            <a href="{{ route('delivery.zones.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Zone Details -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">تفاصيل المنطقة</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اسم المنطقة</label>
                        <p class="text-sm text-gray-900">{{ $zone->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
                        <p class="text-sm text-gray-900">{{ $zone->branch->name ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">رسوم التوصيل</label>
                        <p class="text-sm text-gray-900">{{ number_format($zone->delivery_fee, 2) }} ر.س</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحد الأدنى للطلب</label>
                        <p class="text-sm text-gray-900">{{ number_format($zone->minimum_order_amount, 2) }} ر.س</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">وقت التوصيل المتوقع</label>
                        <p class="text-sm text-gray-900">{{ $zone->estimated_delivery_time_minutes }} دقيقة</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الأولوية</label>
                        <p class="text-sm text-gray-900">{{ $zone->priority }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $zone->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $zone->is_active ? 'نشط' : 'غير نشط' }}
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء</label>
                        <p class="text-sm text-gray-900">{{ $zone->created_at->format('Y-m-d H:i') }}</p>
                    </div>

                    @if($zone->description)
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                        <p class="text-sm text-gray-900">{{ $zone->description }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Map Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">خريطة المنطقة</h2>
            </div>
            <div class="p-6">
                <div class="border border-gray-300 rounded-md">
                    <div id="map" style="height: 400px; width: 100%;"></div>
                </div>
                @if($zone->coordinates && count($zone->coordinates) > 0)
                <div class="mt-4">
                    <h4 class="text-md font-medium text-gray-900 mb-2">إحداثيات المنطقة:</h4>
                    <div class="bg-gray-50 p-4 rounded-md">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            @foreach($zone->coordinates as $index => $coordinate)
                            <div class="text-sm text-gray-700">
                                النقطة {{ $index + 1 }}: {{ number_format($coordinate['latitude'], 6) }}, {{ number_format($coordinate['longitude'], 6) }}
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="space-y-6">
        <!-- Zone Statistics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">إحصائيات المنطقة</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">إجمالي الطلبات</span>
                        <span class="text-lg font-semibold text-gray-900">{{ $zone->deliveryAssignments()->count() }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">الطلبات المكتملة</span>
                        <span class="text-lg font-semibold text-green-600">{{ $zone->deliveryAssignments()->where('status', 'delivered')->count() }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">الطلبات قيد التوصيل</span>
                        <span class="text-lg font-semibold text-blue-600">{{ $zone->deliveryAssignments()->whereIn('status', ['assigned', 'picked_up', 'in_transit'])->count() }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">الطلبات الملغية</span>
                        <span class="text-lg font-semibold text-red-600">{{ $zone->deliveryAssignments()->where('status', 'cancelled')->count() }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Zone Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">معلومات إضافية</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @if($zone->coordinates && count($zone->coordinates) > 2)
                    <div>
                        <span class="text-sm text-gray-600">المساحة التقريبية</span>
                        <p class="text-sm font-medium text-gray-900">{{ number_format($zone->getApproximateArea(), 2) }} كم²</p>
                    </div>
                    @endif
                    
                    @if($zone->coordinates && count($zone->coordinates) > 0)
                    <div>
                        <span class="text-sm text-gray-600">نقطة المركز</span>
                        @php $center = $zone->getCenterPoint(); @endphp
                        <p class="text-sm font-medium text-gray-900">{{ number_format($center['latitude'], 6) }}, {{ number_format($center['longitude'], 6) }}</p>
                    </div>
                    @endif

                    <div>
                        <span class="text-sm text-gray-600">عدد النقاط</span>
                        <p class="text-sm font-medium text-gray-900">{{ count($zone->coordinates ?? []) }} نقطة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">الإجراءات</h2>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <form action="{{ route('delivery.zones.toggle-status', $zone->id) }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 {{ $zone->is_active ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} text-white text-sm font-medium rounded-md transition-colors duration-200">
                            <i class="fas {{ $zone->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                            {{ $zone->is_active ? 'إيقاف المنطقة' : 'تفعيل المنطقة' }}
                        </button>
                    </form>

                    <form action="{{ route('delivery.zones.destroy', $zone->id) }}" method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه المنطقة؟')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            حذف المنطقة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.maps_api_key', 'YOUR_API_KEY') }}&libraries=geometry"></script>
<script>
let map;
let polygon;

$(document).ready(function() {
    initMap();
});

function initMap() {
    // Get zone coordinates
    const coordinates = @json($zone->coordinates ?? []);
    
    // Default to Riyadh, Saudi Arabia or center of zone coordinates
    let defaultCenter = { lat: 24.7136, lng: 46.6753 };
    
    if (coordinates.length > 0) {
        // Calculate center of coordinates
        const latSum = coordinates.reduce((sum, coord) => sum + coord.latitude, 0);
        const lngSum = coordinates.reduce((sum, coord) => sum + coord.longitude, 0);
        defaultCenter = {
            lat: latSum / coordinates.length,
            lng: lngSum / coordinates.length
        };
    }
    
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 13,
        center: defaultCenter,
        mapTypeId: 'roadmap'
    });

    // Create polygon if coordinates exist
    if (coordinates.length > 2) {
        const polygonCoords = coordinates.map(coord => ({
            lat: coord.latitude,
            lng: coord.longitude
        }));

        polygon = new google.maps.Polygon({
            paths: polygonCoords,
            strokeColor: '#FF0000',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: '#FF0000',
            fillOpacity: 0.35
        });
        
        polygon.setMap(map);

        // Fit map to polygon bounds
        const bounds = new google.maps.LatLngBounds();
        polygonCoords.forEach(coord => {
            bounds.extend(new google.maps.LatLng(coord.lat, coord.lng));
        });
        map.fitBounds(bounds);

        // Add markers for each point
        coordinates.forEach((coord, index) => {
            new google.maps.Marker({
                position: { lat: coord.latitude, lng: coord.longitude },
                map: map,
                title: `النقطة ${index + 1}`,
                label: (index + 1).toString()
            });
        });
    }
}
</script>
@endpush