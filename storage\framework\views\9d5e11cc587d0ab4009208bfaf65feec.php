<!-- Loyalty Modal -->
<div class="modal fade" id="loyaltyModal" tabindex="-1" aria-labelledby="loyaltyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="loyaltyModalLabel">
                    <i class="fas fa-star me-2"></i>Manage Loyalty Points
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <div class="modal-body">
                <!-- Customer Info Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h4 class="mb-2" id="loyalty-customer-name">Customer Name</h4>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="stat-box">
                                            <h3 class="text-warning mb-0" id="current-points">0</h3>
                                            <small class="text-muted">Current Points</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-box">
                                            <h3 class="text-success mb-0" id="total-earned">0</h3>
                                            <small class="text-muted">Total Earned</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-box">
                                            <h3 class="text-danger mb-0" id="total-redeemed">0</h3>
                                            <small class="text-muted">Total Redeemed</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Tabs -->
                <ul class="nav nav-tabs mb-4" id="loyaltyTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="add-points-tab" data-bs-toggle="tab" data-bs-target="#add-points" type="button" role="tab">
                            <i class="fas fa-plus-circle me-2"></i>Add Points
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="redeem-points-tab" data-bs-toggle="tab" data-bs-target="#redeem-points" type="button" role="tab">
                            <i class="fas fa-minus-circle me-2"></i>Redeem Points
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="points-calculator-tab" data-bs-toggle="tab" data-bs-target="#points-calculator" type="button" role="tab">
                            <i class="fas fa-calculator me-2"></i>Calculator
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                            <i class="fas fa-history me-2"></i>History
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="loyaltyTabContent">
                    <!-- Add Points Tab -->
                    <div class="tab-pane fade show active" id="add-points" role="tabpanel">
                        <form id="addPointsForm">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" id="add-customer-id" name="customer_id">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="points-to-add" class="form-label">
                                        <i class="fas fa-coins me-1"></i>Points to Add <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="points-to-add" name="points" min="1" step="0.01" required>
                                    <div class="form-text">Enter the number of points to add</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="add-reason" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Reason <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="add-reason" name="reason" required>
                                        <option value="">Select Reason</option>
                                        <option value="purchase">Purchase Reward</option>
                                        <option value="bonus">Bonus Points</option>
                                        <option value="promotion">Promotion</option>
                                        <option value="referral">Referral Bonus</option>
                                        <option value="birthday">Birthday Bonus</option>
                                        <option value="manual">Manual Adjustment</option>
                                    </select>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label for="add-notes" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>Notes
                                    </label>
                                    <textarea class="form-control" id="add-notes" name="notes" rows="3" placeholder="Optional notes about this transaction"></textarea>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Add Points
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Redeem Points Tab -->
                    <div class="tab-pane fade" id="redeem-points" role="tabpanel">
                        <form id="redeemPointsForm">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" id="redeem-customer-id" name="customer_id">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="points-to-redeem" class="form-label">
                                        <i class="fas fa-coins me-1"></i>Points to Redeem <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="points-to-redeem" name="points" min="1" step="0.01" required>
                                    <div class="form-text">Available: <span id="available-points" class="fw-bold text-warning">0</span> points</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="redeem-reason" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Reason <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="redeem-reason" name="reason" required>
                                        <option value="">Select Reason</option>
                                        <option value="discount">Discount Applied</option>
                                        <option value="free_item">Free Item</option>
                                        <option value="cash_back">Cash Back</option>
                                        <option value="gift">Gift Redemption</option>
                                        <option value="manual">Manual Adjustment</option>
                                    </select>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label for="redeem-notes" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>Notes
                                    </label>
                                    <textarea class="form-control" id="redeem-notes" name="notes" rows="3" placeholder="Optional notes about this redemption"></textarea>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-minus me-2"></i>Redeem Points
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Points Calculator Tab -->
                    <div class="tab-pane fade" id="points-calculator" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>Points Calculator</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="purchase-amount" class="form-label">Purchase Amount ($)</label>
                                        <input type="number" class="form-control" id="purchase-amount" min="0" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="points-rate" class="form-label">Points Rate</label>
                                        <select class="form-select" id="points-rate">
                                            <option value="1">1 point per $1</option>
                                            <option value="2">2 points per $1</option>
                                            <option value="0.5">1 point per $2</option>
                                            <option value="5">5 points per $1 (Promotion)</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <h5 class="mb-2">Calculation Result:</h5>
                                            <p class="mb-0">
                                                Purchase Amount: $<span id="calc-amount">0.00</span><br>
                                                Points to Earn: <span id="calc-points" class="fw-bold text-primary">0</span> points
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- History Tab -->
                    <div class="tab-pane fade" id="history" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-striped" id="loyaltyHistoryTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Points</th>
                                        <th>Reason</th>
                                        <th>Notes</th>
                                        <th>Balance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- History will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Points Calculator
$(document).ready(function() {
    $('#purchase-amount, #points-rate').on('input change', function() {
        const amount = parseFloat($('#purchase-amount').val()) || 0;
        const rate = parseFloat($('#points-rate').val()) || 1;
        const points = Math.floor(amount * rate);
        
        $('#calc-amount').text(amount.toFixed(2));
        $('#calc-points').text(points);
    });
});

// Add Points Form
$('#addPointsForm').on('submit', function(e) {
    e.preventDefault();

    const customerId = $('#add-customer-id').val();
    const formData = new FormData(this);

    $.ajax({
        url: '<?php echo e(route("customers.loyalty.add", ":id")); ?>'.replace(':id', customerId),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire('Success!', response.message, 'success');
                $('#loyaltyModal').modal('hide');
                // Refresh customer data
                if (typeof customersTable !== 'undefined') {
                    customersTable.ajax.reload();
                }
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMessage = '';
                Object.values(errors).forEach(error => {
                    errorMessage += error[0] + '\n';
                });
                Swal.fire('Error!', errorMessage, 'error');
            } else {
                Swal.fire('Error!', 'Failed to add points', 'error');
            }
        }
    });
});

// Redeem Points Form
$('#redeemPointsForm').on('submit', function(e) {
    e.preventDefault();
    
    const pointsToRedeem = parseFloat($('#points-to-redeem').val());
    const availablePoints = parseFloat($('#available-points').text());
    
    if (pointsToRedeem > availablePoints) {
        Swal.fire('Error!', 'Cannot redeem more points than available', 'error');
        return;
    }
    
    const customerId = $('#redeem-customer-id').val();
    const formData = new FormData(this);

    $.ajax({
        url: '<?php echo e(route("customers.loyalty.redeem", ":id")); ?>'.replace(':id', customerId),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire('Success!', response.message, 'success');
                $('#loyaltyModal').modal('hide');
                // Refresh customer data
                if (typeof customersTable !== 'undefined') {
                    customersTable.ajax.reload();
                }
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMessage = '';
                Object.values(errors).forEach(error => {
                    errorMessage += error[0] + '\n';
                });
                Swal.fire('Error!', errorMessage, 'error');
            } else {
                Swal.fire('Error!', 'Failed to redeem points', 'error');
            }
        }
    });
});

// Load loyalty history
function loadLoyaltyHistory(customerId) {
    if ($.fn.DataTable.isDataTable('#loyaltyHistoryTable')) {
        $('#loyaltyHistoryTable').DataTable().destroy();
    }
    
    $('#loyaltyHistoryTable').DataTable({
        ajax: {
            url: '<?php echo e(route("customers.loyalty.history", ":id")); ?>'.replace(':id', customerId),
            dataSrc: 'data'
        },
        columns: [
            { data: 'created_at', render: function(data) {
                return new Date(data).toLocaleDateString();
            }},
            { data: 'type', render: function(data) {
                return data === 'add' ? 
                    '<span class="badge bg-success">Added</span>' : 
                    '<span class="badge bg-danger">Redeemed</span>';
            }},
            { data: 'points', render: function(data, type, row) {
                return row.type === 'add' ? '+' + data : '-' + data;
            }},
            { data: 'reason' },
            { data: 'notes' },
            { data: 'balance_after' }
        ],
        order: [[0, 'desc']],
        pageLength: 10,
        responsive: true
    });
}
</script>

<style>
.stat-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: white;
    border: 1px solid #e5e7eb;
}

.nav-tabs .nav-link {
    color: #6b7280;
    border: none;
    border-bottom: 2px solid transparent;
}

.nav-tabs .nav-link.active {
    color: #f59e0b;
    border-bottom-color: #f59e0b;
    background-color: transparent;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #f59e0b;
    background-color: transparent;
}

#loyaltyHistoryTable {
    font-size: 0.875rem;
}

.alert-info {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}
</style><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Customer\Providers/../resources/views/modals/loyalty-modal.blade.php ENDPATH**/ ?>