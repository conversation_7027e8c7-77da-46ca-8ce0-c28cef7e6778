<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الحجوزات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: #4f46e5;
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-custom {
            background: #4f46e5;
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            background: #4338ca;
            box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
            color: white;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-5">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4 fw-bold text-primary">
                        <i class="fas fa-calendar-check me-3"></i>
                        نظام إدارة الحجوزات
                    </h1>
                    <p class="lead text-muted">إدارة شاملة للحجوزات والمناطق وطلبات النادل</p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Reservations Card -->
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    <div class="card-header text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            إدارة الحجوزات
                        </h4>
                    </div>
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h5 class="card-title">الحجوزات</h5>
                        <p class="card-text">إدارة جميع حجوزات المطعم، تأكيد الحجوزات، وتتبع حالة العملاء</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>إضافة حجوزات جديدة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تأكيد وإلغاء الحجوزات</li>
                            <li><i class="fas fa-check text-success me-2"></i>تتبع حالة الحجز</li>
                            <li><i class="fas fa-check text-success me-2"></i>البحث والفلترة المتقدمة</li>
                        </ul>
                    </div>
                    <div class="card-footer text-center">
                        <a href="{{ url('/reservation/reservations') }}" class="btn btn-custom">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول إلى الحجوزات
                        </a>
                    </div>
                </div>
            </div>

            <!-- Areas Card -->
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    <div class="card-header text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-map-marked-alt me-2"></i>
                            إدارة المناطق
                        </h4>
                    </div>
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h5 class="card-title">المناطق</h5>
                        <p class="card-text">إدارة مناطق المطعم والطاولات المختلفة مع إحصائيات مفصلة</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>إضافة وتعديل المناطق</li>
                            <li><i class="fas fa-check text-success me-2"></i>عرض الطاولات في كل منطقة</li>
                            <li><i class="fas fa-check text-success me-2"></i>إحصائيات السعة والإشغال</li>
                            <li><i class="fas fa-check text-success me-2"></i>إدارة حالة الطاولات</li>
                        </ul>
                    </div>
                    <div class="card-footer text-center">
                        <a href="{{ url('/reservation/areas') }}" class="btn btn-custom">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول إلى المناطق
                        </a>
                    </div>
                </div>
            </div>

            <!-- Waiter Requests Card -->
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    <div class="card-header text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            طلبات النادل
                        </h4>
                    </div>
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-concierge-bell"></i>
                        </div>
                        <h5 class="card-title">طلبات الخدمة</h5>
                        <p class="card-text">إدارة طلبات العملاء للنادل مع نظام تحديث فوري</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>استقبال طلبات العملاء</li>
                            <li><i class="fas fa-check text-success me-2"></i>تعيين النادل المناسب</li>
                            <li><i class="fas fa-check text-success me-2"></i>تتبع حالة الطلبات</li>
                            <li><i class="fas fa-check text-success me-2"></i>تحديث فوري كل 30 ثانية</li>
                        </ul>
                    </div>
                    <div class="card-footer text-center">
                        <a href="{{ url('/reservation/waiter-requests') }}" class="btn btn-custom">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول إلى طلبات النادل
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Tools Row -->
        <div class="row mt-5 g-4">
            <!-- Tables Management -->
            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-table text-info"></i>
                        </div>
                        <h5 class="card-title">إدارة الطاولات</h5>
                        <p class="card-text">إدارة الطاولات وحالتها</p>
                        <a href="{{ url('/reservation/tables') }}" class="btn btn-outline-info">
                            <i class="fas fa-arrow-left me-2"></i>
                            الطاولات
                        </a>
                    </div>
                </div>
            </div>

            <!-- Reports -->
            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar text-success"></i>
                        </div>
                        <h5 class="card-title">التقارير والإحصائيات</h5>
                        <p class="card-text">تقارير مفصلة وإحصائيات</p>
                        <a href="{{ route('reservation.reports') }}" class="btn btn-outline-success">
                            <i class="fas fa-arrow-left me-2"></i>
                            التقارير
                        </a>
                    </div>
                </div>
            </div>

            <!-- QR Code Testing -->
            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-qrcode text-purple"></i>
                        </div>
                        <h5 class="card-title">اختبار رموز QR</h5>
                        <p class="card-text">إنشاء واختبار رموز QR</p>
                        <a href="{{ route('reservation.qr.test') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            QR Test
                        </a>
                    </div>
                </div>
            </div>

            <!-- API Documentation -->
            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-code text-warning"></i>
                        </div>
                        <h5 class="card-title">API Documentation</h5>
                        <p class="card-text">وثائق واختبار API</p>
                        <div class="d-grid gap-2">
                            <a href="{{ route('reservation.api.docs') }}" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-book me-1"></i>
                                الوثائق
                            </a>
                            <a href="{{ route('reservation.api.tester') }}" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-flask me-1"></i>
                                اختبار API
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            إحصائيات سريعة
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="p-3">
                                    <i class="fas fa-calendar-check text-primary" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">الحجوزات اليوم</h5>
                                    <h3 class="text-primary">--</h3>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <i class="fas fa-users text-success" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">العملاء الحاليين</h5>
                                    <h3 class="text-success">--</h3>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <i class="fas fa-table text-warning" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">الطاولات المتاحة</h5>
                                    <h3 class="text-warning">--</h3>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <i class="fas fa-bell text-danger" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">طلبات النادل</h5>
                                    <h3 class="text-danger">--</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>