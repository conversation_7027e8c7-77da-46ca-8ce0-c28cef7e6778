<?php

use Illuminate\Support\Facades\Route;
use Modu<PERSON>\Customer\Http\Controllers\Api\CustomerController;
use Modules\Customer\Http\Controllers\Api\LoyaltyController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum'])->prefix('customers')->name('api.customers.')->group(function () {
    // Customer CRUD Operations
    Route::get('/', [CustomerController::class, 'index'])->name('index');
    Route::post('/', [CustomerController::class, 'store'])->name('store');
    Route::get('/{customer}', [CustomerController::class, 'show'])->name('show');
    Route::put('/{customer}', [CustomerController::class, 'update'])->name('update');
    Route::delete('/{customer}', [CustomerController::class, 'destroy'])->name('destroy');
    
    // Customer Search and Filtering
    Route::get('/search/{query}', [CustomerController::class, 'search'])->name('search');
    Route::get('/active/list', [CustomerController::class, 'getActiveCustomers'])->name('active');
    Route::get('/statistics/overview', [CustomerController::class, 'getStatistics'])->name('statistics');
    
    // Customer Status Management
    Route::patch('/{customer}/activate', [CustomerController::class, 'activate'])->name('activate');
    Route::patch('/{customer}/deactivate', [CustomerController::class, 'deactivate'])->name('deactivate');
    Route::patch('/{customer}/update-last-visit', [CustomerController::class, 'updateLastVisit'])->name('update-last-visit');
    
    // Loyalty Management
    Route::prefix('{customer}/loyalty')->name('loyalty.')->group(function () {
        Route::get('/points', [LoyaltyController::class, 'getCurrentPoints'])->name('points');
        Route::post('/add-points', [LoyaltyController::class, 'addPoints'])->name('add-points');
        Route::post('/redeem-points', [LoyaltyController::class, 'redeemPoints'])->name('redeem-points');
        Route::get('/history', [LoyaltyController::class, 'getHistory'])->name('history');
    });
    
    // Loyalty Calculations
    Route::post('/loyalty/calculate-points', [LoyaltyController::class, 'calculatePointsForOrder'])->name('loyalty.calculate-points');
    Route::post('/loyalty/calculate-discount', [LoyaltyController::class, 'calculateDiscountForPoints'])->name('loyalty.calculate-discount');
    Route::get('/loyalty/top-customers', [LoyaltyController::class, 'getTopLoyaltyCustomers'])->name('loyalty.top-customers');
});

// Public API routes (for mobile app, kiosk, etc.)
Route::middleware(['tenant.check'])->prefix('public/customers')->name('api.public.customers.')->group(function () {
    // Customer lookup by phone/email for orders
    Route::post('/lookup', [CustomerController::class, 'lookup'])->name('lookup');
    Route::post('/quick-register', [CustomerController::class, 'quickRegister'])->name('quick-register');
    
    // Loyalty points check
    Route::get('/{customer}/loyalty/points', [LoyaltyController::class, 'getCurrentPoints'])->name('loyalty.points');
});