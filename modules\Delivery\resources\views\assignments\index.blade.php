@extends('layouts.master')

@section('content')
<!-- <PERSON> Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-truck mr-3"></i>
                تعيينات التوصيل
            </h1>
            <p class="text-sm text-gray-600 mt-1">إدارة تعيينات الطلبات لموظفي التوصيل</p>
        </div>
        <div class="flex space-x-2">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="refreshStats">
                <i class="fas fa-sync-alt mr-2"></i>
                تحديث
            </button>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200" onclick="openModal('createAssignmentModal')">
                <i class="fas fa-plus mr-2"></i>
                تعيين جديد
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6" id="statisticsCards">
    <div class="bg-blue-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="totalAssignments">0</h3>
                <p class="text-blue-100">إجمالي التعيينات</p>
            </div>
            <div class="text-blue-200">
                <i class="fas fa-clipboard-list text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-yellow-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="pendingAssignments">0</h3>
                <p class="text-yellow-100">في الانتظار</p>
            </div>
            <div class="text-yellow-200">
                <i class="fas fa-clock text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-indigo-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="inTransitAssignments">0</h3>
                <p class="text-indigo-100">في الطريق</p>
            </div>
            <div class="text-indigo-200">
                <i class="fas fa-shipping-fast text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-green-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="deliveredAssignments">0</h3>
                <p class="text-green-100">تم التوصيل</p>
            </div>
            <div class="text-green-200">
                <i class="fas fa-check-circle text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-red-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="cancelledAssignments">0</h3>
                <p class="text-red-100">ملغية/فاشلة</p>
            </div>
            <div class="text-red-200">
                <i class="fas fa-times-circle text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-gray-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="completionRate">0%</h3>
                <p class="text-gray-100">معدل الإنجاز</p>
            </div>
            <div class="text-gray-200">
                <i class="fas fa-chart-pie text-3xl"></i>
            </div>
        </div>
    </div>
</div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">فلاتر البحث</h3>
        </div>
        <div class="p-6">
            <form id="filtersForm">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
                    <div>
                        <label for="filterBranch" class="block text-sm font-medium text-gray-700 mb-2">الفرع</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filterBranch" name="branch_id">
                            <option value="">جميع الفروع</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="filterPersonnel" class="block text-sm font-medium text-gray-700 mb-2">موظف التوصيل</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filterPersonnel" name="personnel_id">
                            <option value="">جميع موظفي التوصيل</option>
                            @foreach($personnel as $person)
                                <option value="{{ $person->id }}">{{ $person->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="filterStatus" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filterStatus" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="assigned">مُعيّن</option>
                            <option value="picked_up">تم الاستلام</option>
                            <option value="in_transit">في الطريق</option>
                            <option value="delivered">تم التوصيل</option>
                            <option value="cancelled">ملغي</option>
                            <option value="failed">فشل</option>
                        </select>
                    </div>
                    <div>
                        <label for="filterDateFrom" class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filterDateFrom" name="date_from">
                    </div>
                    <div>
                        <label for="filterDateTo" class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="filterDateTo" name="date_to">
                    </div>
                    <div class="flex items-end">
                        <button type="button" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="applyFilters">
                            <i class="fas fa-filter mr-2"></i> تطبيق
                        </button>
                    </div>
                </div>
                <div class="flex flex-wrap gap-2">
                    <button type="button" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="clearFilters">
                        <i class="fas fa-times mr-2"></i> مسح الفلاتر
                    </button>
                    <button type="button" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="exportData">
                        <i class="fas fa-download mr-2"></i> تصدير
                    </button>
                    <button type="button" class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" id="bulkActions" disabled>
                        <i class="fas fa-tasks mr-2"></i> إجراءات مجمعة (<span id="selectedCount">0</span>)
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">مهام التوصيل</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="assignmentsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الطلب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">موظف التوصيل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التعيين</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- DataTable will populate this -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Include Modals -->
@include('delivery::assignments.partials.modals')

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.dataTables.min.css">
<style>
/* Custom DataTables styling for Tailwind */
.dataTables_wrapper {
    font-family: inherit;
}

.dataTables_length select,
.dataTables_filter input {
    @apply px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.dataTables_length label,
.dataTables_filter label {
    @apply text-sm font-medium text-gray-700;
}

.dataTables_info {
    @apply text-sm text-gray-600;
}

.dataTables_paginate .paginate_button {
    @apply px-3 py-2 ml-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700;
}

.dataTables_paginate .paginate_button.current {
    @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
}

.dataTables_paginate .paginate_button.disabled {
    @apply opacity-50 cursor-not-allowed;
}

table.dataTable tbody tr {
    @apply hover:bg-gray-50;
}

table.dataTable tbody td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.dataTables_processing {
    @apply bg-white border border-gray-300 rounded-md shadow-sm;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/select/1.3.4/js/dataTables.select.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    let table = $('#assignmentsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("delivery.assignments.data") }}',
            data: function(d) {
                d.branch_id = $('#filterBranch').val();
                d.personnel_id = $('#filterPersonnel').val();
                d.status = $('#filterStatus').val();
                d.date_from = $('#filterDateFrom').val();
                d.date_to = $('#filterDateTo').val();
            }
        },
        columns: [
            { data: 'id', name: 'id', orderable: false, searchable: false, render: function(data) {
                return '<input type="checkbox" class="row-select" value="' + data + '">';
            }},
            { data: 'id', name: 'id' },
            { data: 'order_number', name: 'order_number' },
            { data: 'customer_name', name: 'customer_name' },
            { data: 'customer_phone', name: 'customer_phone' },
            { data: 'personnel_name', name: 'personnel_name' },
            { data: 'branch_name', name: 'branch_name' },
            { data: 'delivery_address', name: 'delivery_address' },
            { data: 'status_badge', name: 'status', orderable: false },
            { data: 'estimated_delivery', name: 'estimated_delivery_time' },
            { data: 'actual_delivery', name: 'delivered_at' },
            { data: 'delivery_fee', name: 'delivery_fee' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Load statistics
    function loadStatistics() {
        $.get('{{ route("delivery.assignments.statistics") }}', {
            date_from: $('#filterDateFrom').val(),
            date_to: $('#filterDateTo').val()
        })
        .done(function(data) {
            $('#totalAssignments').text(data.total_assignments);
            $('#pendingAssignments').text(data.pending_assignments);
            $('#inTransitAssignments').text(data.in_transit_assignments);
            $('#deliveredAssignments').text(data.delivered_assignments);
            $('#cancelledAssignments').text(data.cancelled_assignments);
            $('#completionRate').text(data.completion_rate + '%');
        });
    }

    // Initial load
    loadStatistics();

    // Apply filters
    $('#applyFilters').click(function() {
        table.ajax.reload();
        loadStatistics();
    });

    // Clear filters
    $('#clearFilters').click(function() {
        $('#filtersForm')[0].reset();
        table.ajax.reload();
        loadStatistics();
    });

    // Refresh stats
    $('#refreshStats').click(function() {
        loadStatistics();
        table.ajax.reload();
    });

    // Select all checkbox
    $('#selectAll').change(function() {
        $('.row-select').prop('checked', this.checked);
        updateBulkActionsButton();
    });

    // Individual row selection
    $(document).on('change', '.row-select', function() {
        updateBulkActionsButton();
    });

    function updateBulkActionsButton() {
        let selectedCount = $('.row-select:checked').length;
        $('#selectedCount').text(selectedCount);
        $('#bulkActions').prop('disabled', selectedCount === 0);
    }

    // Bulk actions
    $('#bulkActions').click(function() {
        let selectedIds = $('.row-select:checked').map(function() {
            return this.value;
        }).get();
        
        if (selectedIds.length > 0) {
            $('#bulkActionsModal').modal('show');
        }
    });

    // Assign personnel modal
    $(document).on('click', '.assign-personnel', function() {
        let assignmentId = $(this).data('assignment-id');
        $('#assignmentId').val(assignmentId);
        loadAvailablePersonnel('#personnelSelect');
        $('#assignPersonnelModal').modal('show');
    });

    // Update status modal
    $(document).on('click', '.update-status', function() {
        let assignmentId = $(this).data('assignment-id');
        $('#statusAssignmentId').val(assignmentId);
        $('#updateStatusModal').modal('show');
    });

    // Track assignment modal
    $(document).on('click', '.track-assignment', function() {
        let assignmentId = $(this).data('assignment-id');
        loadTrackingData(assignmentId);
        $('#trackAssignmentModal').modal('show');
    });

    // Confirm assign personnel
    $('#confirmAssign').click(function() {
        let formData = $('#assignPersonnelForm').serialize();
        let assignmentId = $('#assignmentId').val();
        
        $.post('{{ route("delivery.assignments.assign-personnel", ":id") }}'.replace(':id', assignmentId), formData)
        .done(function(response) {
            if (response.success) {
                $('#assignPersonnelModal').modal('hide');
                table.ajax.reload();
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function() {
            showAlert('error', 'Failed to assign personnel');
        });
    });

    // Confirm status update
    $('#confirmStatusUpdate').click(function() {
        let formData = $('#updateStatusForm').serialize();
        let assignmentId = $('#statusAssignmentId').val();
        
        $.post('{{ route("delivery.assignments.update-status", ":id") }}'.replace(':id', assignmentId), formData)
        .done(function(response) {
            if (response.success) {
                $('#updateStatusModal').modal('hide');
                table.ajax.reload();
                loadStatistics();
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function() {
            showAlert('error', 'Failed to update status');
        });
    });

    // Load available personnel
    function loadAvailablePersonnel(selector) {
        $.get('{{ route("delivery.assignments.available-personnel") }}')
        .done(function(data) {
            let options = '<option value="">Select personnel...</option>';
            data.forEach(function(person) {
                options += `<option value="${person.id}">${person.name} (${person.vehicle_type})</option>`;
            });
            $(selector).html(options);
        });
    }

    // Load tracking data
    function loadTrackingData(assignmentId) {
        // This would load real tracking data from the server
        $('#trackingStatus').text('Loading...');
        $('#trackingPersonnel').text('Loading...');
        $('#trackingLastUpdate').text('Loading...');
        $('#trackingEstimated').text('Loading...');
    }

    // Show alert function
    function showAlert(type, message) {
        let alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        let alert = `<div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>`;
        
        $('.container-fluid').prepend(alert);
        
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Table search
    $('#tableSearch').on('keyup', function() {
        table.search(this.value).draw();
    });
});
</script>
@endpush