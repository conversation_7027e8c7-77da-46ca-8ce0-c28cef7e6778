<!-- Customer Modal -->
<div id="customerModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white">
            <div class="flex items-center justify-between">
                <h3 id="customerModalLabel" class="text-xl font-semibold flex items-center">
                    <i class="fas fa-user-plus mr-3"></i>Add New Customer
                </h3>
                <button type="button" onclick="closeCustomerModal()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6 max-h-[calc(90vh-140px)] overflow-y-auto">
            <form id="customerForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="customer_id" name="customer_id">
                
                <!-- Personal Information Section -->
                <div class="mb-8">
                    <div class="flex items-center mb-6 pb-3 border-b-2 border-blue-100">
                        <i class="fas fa-user text-blue-600 mr-3 text-lg"></i>
                        <h4 class="text-lg font-semibold text-gray-800">Personal Information</h4>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user mr-2 text-gray-500"></i>First Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="first_name" name="first_name" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user mr-2 text-gray-500"></i>Last Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="last_name" name="last_name" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-envelope mr-2 text-gray-500"></i>Email Address
                            </label>
                            <input type="email" id="email" name="email"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-phone mr-2 text-gray-500"></i>Phone Number <span class="text-red-500">*</span>
                            </label>
                            <input type="tel" id="phone" name="phone" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-birthday-cake mr-2 text-gray-500"></i>Date of Birth
                            </label>
                            <input type="date" id="date_of_birth" name="date_of_birth"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-venus-mars mr-2 text-gray-500"></i>Gender
                            </label>
                            <select id="gender" name="gender"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="mb-8">
                    <div class="flex items-center mb-6 pb-3 border-b-2 border-green-100">
                        <i class="fas fa-map-marker-alt text-green-600 mr-3 text-lg"></i>
                        <h4 class="text-lg font-semibold text-gray-800">Contact Information</h4>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-home mr-2 text-gray-500"></i>Address
                            </label>
                            <textarea id="address" name="address" rows="3" placeholder="Enter full address"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"></textarea>
                        </div>
                        
                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-city mr-2 text-gray-500"></i>City
                            </label>
                            <input type="text" id="city" name="city"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-mail-bulk mr-2 text-gray-500"></i>Postal Code
                            </label>
                            <input type="text" id="postal_code" name="postal_code"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                </div>

                <!-- Loyalty Information Section -->
                <div class="mb-8">
                    <div class="flex items-center mb-6 pb-3 border-b-2 border-yellow-100">
                        <i class="fas fa-star text-yellow-600 mr-3 text-lg"></i>
                        <h4 class="text-lg font-semibold text-gray-800">Loyalty Information</h4>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="loyalty_points" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-coins mr-2 text-gray-500"></i>Initial Loyalty Points
                            </label>
                            <input type="number" id="loyalty_points" name="loyalty_points" min="0" step="0.01" value="0"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                            <p class="text-sm text-gray-500 mt-1">Points to assign to the customer initially</p>
                        </div>
                        
                        <div>
                            <label for="customer_group" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-users mr-2 text-gray-500"></i>Customer Group
                            </label>
                            <select id="customer_group" name="customer_group"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                <option value="">Select Group</option>
                                <option value="regular">Regular</option>
                                <option value="vip">VIP</option>
                                <option value="premium">Premium</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Status and Notes Section -->
                <div class="mb-6">
                    <div class="flex items-center mb-6 pb-3 border-b-2 border-purple-100">
                        <i class="fas fa-cog text-purple-600 mr-3 text-lg"></i>
                        <h4 class="text-lg font-semibold text-gray-800">Additional Settings</h4>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" checked
                                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                            <label for="is_active" class="ml-3 text-sm font-medium text-gray-700">
                                <i class="fas fa-toggle-on mr-2 text-green-500"></i>Active Customer
                            </label>
                        </div>
                        <p class="text-sm text-gray-500 ml-8">Inactive customers cannot make orders or earn points</p>
                        
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-sticky-note mr-2 text-gray-500"></i>Notes
                            </label>
                            <textarea id="notes" name="notes" rows="3" placeholder="Any additional notes about the customer"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t">
            <button type="button" onclick="closeCustomerModal()" 
                class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                <i class="fas fa-times mr-2"></i>Cancel
            </button>
            <button type="submit" form="customerForm"
                class="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                <i class="fas fa-save mr-2"></i>Save Customer
            </button>
        </div>
    </div>
</div>

.invalid-feedback {
    display: block;
}
</style><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Customer\Providers/../resources/views/modals/customer-modal.blade.php ENDPATH**/ ?>