<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Delivery\Entities\DeliveryTracking;
use Modules\Orders\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class DeliveryTrackingWebController extends Controller
{
    /**
     * Display a listing of delivery tracking records.
     */
    public function index()
    {
        $deliveryPersonnel = User::whereHas('roles', function ($query) {
            $query->where('name', 'delivery_personnel');
        })->where('status', 'active')->get();
        
        return view('delivery::tracking.index', compact('deliveryPersonnel'));
    }

    /**
     * Display the specified tracking record.
     */
    public function show(DeliveryTracking $tracking)
    {
        $tracking->load(['order', 'deliveryPersonnel']);
        
        return view('delivery::tracking.show', compact('tracking'));
    }

    /**
     * Get tracking data for DataTable.
     */
    public function getTrackingData(Request $request)
    {
        $query = DeliveryTracking::with(['order', 'deliveryPersonnel'])
            ->select(['id', 'order_id', 'delivery_personnel_id', 'status', 'latitude', 'longitude', 
                     'estimated_arrival_time', 'actual_arrival_time', 'created_at', 'updated_at']);

        // Apply filters
        if ($request->filled('delivery_personnel_id')) {
            $query->where('delivery_personnel_id', $request->delivery_personnel_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('order_number', function ($tracking) {
                return $tracking->order ? '#' . $tracking->order->order_number : '-';
            })
            ->addColumn('delivery_personnel_name', function ($tracking) {
                return $tracking->deliveryPersonnel ? $tracking->deliveryPersonnel->name : '-';
            })
            ->addColumn('location', function ($tracking) {
                if ($tracking->latitude && $tracking->longitude) {
                    return '<div class="location-info">' .
                           '<div><strong>خط العرض:</strong> ' . number_format($tracking->latitude, 6) . '</div>' .
                           '<div><strong>خط الطول:</strong> ' . number_format($tracking->longitude, 6) . '</div>' .
                           '<a href="https://maps.google.com/?q=' . $tracking->latitude . ',' . $tracking->longitude . '" target="_blank" class="btn btn-sm btn-outline-primary mt-1">' .
                           '<i class="fa fa-map-marker"></i> عرض على الخريطة</a>' .
                           '</div>';
                }
                return '-';
            })
            ->addColumn('status_badge', function ($tracking) {
                $statusColors = [
                    'assigned' => 'info',
                    'picked_up' => 'warning',
                    'in_transit' => 'primary',
                    'delivered' => 'success',
                    'failed' => 'danger',
                    'cancelled' => 'secondary'
                ];
                
                $statusLabels = [
                    'assigned' => 'مُعيّن',
                    'picked_up' => 'تم الاستلام',
                    'in_transit' => 'في الطريق',
                    'delivered' => 'تم التوصيل',
                    'failed' => 'فشل',
                    'cancelled' => 'ملغي'
                ];
                
                $color = $statusColors[$tracking->status] ?? 'secondary';
                $label = $statusLabels[$tracking->status] ?? $tracking->status;
                
                return '<span class="badge badge-' . $color . '">' . $label . '</span>';
            })
            ->addColumn('timing_info', function ($tracking) {
                $info = '<div class="timing-info">';
                
                if ($tracking->estimated_arrival_time) {
                    $info .= '<div><strong>الوقت المتوقع:</strong> ' . $tracking->estimated_arrival_time->format('H:i') . '</div>';
                }
                
                if ($tracking->actual_arrival_time) {
                    $info .= '<div><strong>الوقت الفعلي:</strong> ' . $tracking->actual_arrival_time->format('H:i') . '</div>';
                }
                
                $info .= '<div><strong>آخر تحديث:</strong> ' . $tracking->updated_at->diffForHumans() . '</div>';
                $info .= '</div>';
                
                return $info;
            })
            ->addColumn('action', function ($tracking) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('delivery.tracking.show', $tracking) . '" class="btn btn-sm btn-info" title="عرض التفاصيل"><i class="fa fa-eye"></i></a>';
                
                if ($tracking->latitude && $tracking->longitude) {
                    $actions .= '<a href="https://maps.google.com/?q=' . $tracking->latitude . ',' . $tracking->longitude . '" target="_blank" class="btn btn-sm btn-primary" title="عرض على الخريطة"><i class="fa fa-map-marker"></i></a>';
                }
                
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['location', 'status_badge', 'timing_info', 'action'])
            ->make(true);
    }

    /**
     * Get real-time tracking data for a specific order.
     */
    public function getOrderTracking(Request $request, $orderId)
    {
        $tracking = DeliveryTracking::with(['order', 'deliveryPersonnel'])
            ->where('order_id', $orderId)
            ->latest()
            ->first();

        if (!$tracking) {
            return response()->json([
                'success' => false,
                'message' => 'لا توجد بيانات تتبع لهذا الطلب'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $tracking->id,
                'order_number' => $tracking->order->order_number,
                'delivery_personnel' => $tracking->deliveryPersonnel->name,
                'status' => $tracking->status,
                'latitude' => $tracking->latitude,
                'longitude' => $tracking->longitude,
                'estimated_arrival_time' => $tracking->estimated_arrival_time,
                'actual_arrival_time' => $tracking->actual_arrival_time,
                'last_updated' => $tracking->updated_at->diffForHumans()
            ]
        ]);
    }
}