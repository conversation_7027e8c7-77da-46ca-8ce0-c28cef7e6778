

<?php $__env->startSection('title', 'Customer Management'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/dataTables.responsive.min.css">
<style>
    /* DataTables Tailwind Styling */
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        @apply px-3 py-2 mx-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        @apply bg-blue-600 text-white border-blue-600;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Customer Management</h1>
            <p class="text-gray-600 mt-1">Manage your customers and their information</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <button type="button" 
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium shadow-sm transition-all duration-200" 
                onclick="openCustomerModal()">
                <i class="fas fa-plus mr-2"></i>Add New Customer
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm font-medium uppercase tracking-wide">Total Customers</p>
                    <p class="text-3xl font-bold mt-2 text-gray-900" id="total-customers">0</p>
                </div>
                <div class="text-gray-400">
                    <i class="fas fa-users text-4xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm font-medium uppercase tracking-wide">Active Customers</p>
                    <p class="text-3xl font-bold mt-2 text-gray-900" id="active-customers">0</p>
                </div>
                <div class="text-gray-400">
                    <i class="fas fa-user-check text-4xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm font-medium uppercase tracking-wide">Total Loyalty Points</p>
                    <p class="text-3xl font-bold mt-2 text-gray-900" id="total-loyalty-points">0</p>
                </div>
                <div class="text-gray-400">
                    <i class="fas fa-star text-4xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm font-medium uppercase tracking-wide">New This Month</p>
                    <p class="text-3xl font-bold mt-2 text-gray-900" id="new-customers">0</p>
                </div>
                <div class="text-gray-400">
                    <i class="fas fa-user-plus text-4xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-users mr-2 text-blue-600"></i>Customers List
            </h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="customersTable" class="w-full">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Full Name</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loyalty Points</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Visit</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Customer Modal -->
<?php echo $__env->make('customer::modals.customer-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<!-- Loyalty Points Modal -->
<?php echo $__env->make('customer::modals.loyalty-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<!-- View Customer Modal -->
<?php echo $__env->make('customer::modals.view-customer-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Customer\Providers/../resources/views/index.blade.php ENDPATH**/ ?>