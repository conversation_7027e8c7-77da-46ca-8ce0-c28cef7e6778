<?php $__env->startSection('title', 'تفاصيل الدور'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <i class="fas fa-user-shield text-blue-600"></i>
                        تفاصيل الدور: <?php echo e($role->name); ?>

                    </h1>
                    <div class="flex space-x-2">
                        <a href="<?php echo e(route('roles.edit', $role)); ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            تعديل
                        </a>
                        <a href="<?php echo e(route('roles.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-2"></i>
                            العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-shield text-blue-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">اسم الدور</p>
                        <p class="text-lg font-bold text-gray-900"><?php echo e($role->name); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shield-alt text-purple-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">نوع الحارس</p>
                        <p class="text-lg font-bold text-gray-900">
                            <?php if($role->guard_name === 'web'): ?>
                                ويب
                            <?php elseif($role->guard_name === 'api'): ?>
                                API
                            <?php elseif($role->guard_name === 'sanctum'): ?>
                                Sanctum
                            <?php else: ?>
                                <?php echo e($role->guard_name); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar text-green-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">تاريخ الإنشاء</p>
                        <p class="text-lg font-bold text-gray-900"><?php echo e($role->created_at->format('Y-m-d')); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">عدد الصلاحيات</p>
                        <p class="text-3xl font-bold text-blue-600"><?php echo e($role->permissions->count()); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-key text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="showPermissions()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        عرض جميع الصلاحيات <i class="fas fa-arrow-left mr-1"></i>
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">عدد المستخدمين</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo e($role->users->count()); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="showUsers()" class="text-green-600 hover:text-green-800 text-sm font-medium">
                        عرض جميع المستخدمين <i class="fas fa-arrow-left mr-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button class="tab-button active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-tab="permissions">
                        الصلاحيات (<?php echo e($role->permissions->count()); ?>)
                    </button>
                    <button class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="users">
                        المستخدمين (<?php echo e($role->users->count()); ?>)
                    </button>
                </nav>
            </div>

            <!-- Permissions Tab -->
            <div id="permissions" class="tab-content active p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">صلاحيات الدور</h3>
                    <button onclick="managePermissions(<?php echo e($role->id); ?>)" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog mr-2"></i>
                        إدارة الصلاحيات
                    </button>
                </div>

                <?php if($role->permissions->count() > 0): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php $__currentLoopData = $role->permissions->groupBy(function($permission) { return explode('.', $permission->name)[0] ?? 'other'; }); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $permissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-3 capitalize"><?php echo e($group); ?></h4>
                                <div class="space-y-2">
                                    <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-gray-600"><?php echo e($permission->name); ?></span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                <?php if($permission->guard_name === 'web'): ?> bg-indigo-100 text-indigo-800
                                                <?php elseif($permission->guard_name === 'api'): ?> bg-purple-100 text-purple-800
                                                <?php elseif($permission->guard_name === 'sanctum'): ?> bg-yellow-100 text-yellow-800
                                                <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                                <?php echo e($permission->guard_name); ?>

                                            </span>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-key text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">لا توجد صلاحيات مخصصة لهذا الدور</p>
                        <button onclick="managePermissions(<?php echo e($role->id); ?>)" class="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            إضافة صلاحيات
                        </button>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Users Tab -->
            <div id="users" class="tab-content p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">المستخدمين المخصص لهم هذا الدور</h3>
                </div>

                <?php if($role->users->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-right text-gray-500">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3">الاسم</th>
                                    <th class="px-6 py-3">البريد الإلكتروني</th>
                                    <th class="px-6 py-3">المنصب</th>
                                    <th class="px-6 py-3">الحالة</th>
                                    <th class="px-6 py-3">تاريخ الإنشاء</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__currentLoopData = $role->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-user text-blue-600"></i>
                                                </div>
                                                <div class="mr-3">
                                                    <div class="text-sm font-medium text-gray-900"><?php echo e($user->name); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo e($user->email); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo e($user->position ?? '-'); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if($user->is_active): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    نشط
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    غير نشط
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo e($user->created_at->format('Y-m-d')); ?>

                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">لا يوجد مستخدمين مخصص لهم هذا الدور</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function showPermissions() {
    $('.tab-button').removeClass('active');
    $('.tab-content').removeClass('active');
    $('[data-tab="permissions"]').addClass('active');
    $('#permissions').addClass('active');
}

function showUsers() {
    $('.tab-button').removeClass('active');
    $('.tab-content').removeClass('active');
    $('[data-tab="users"]').addClass('active');
    $('#users').addClass('active');
}

function managePermissions(roleId) {
    window.location.href = `/admin/roles/${roleId}/permissions`;
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/roles/show.blade.php ENDPATH**/ ?>