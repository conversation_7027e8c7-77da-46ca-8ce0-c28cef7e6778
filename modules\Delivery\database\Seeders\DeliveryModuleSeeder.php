<?php

namespace Modules\Delivery\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Branch;
use App\Models\Customer;
use App\Models\Order;
use Modules\Delivery\Entities\DeliveryPersonnel;
use Modules\Delivery\Entities\DeliveryZone;
use Modules\Delivery\Entities\DeliveryAssignment;
use Modules\Delivery\Entities\DeliveryReview;
use Modules\Delivery\Entities\DeliveryTip;

class DeliveryModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedDeliveryPersonnel();
        $this->seedDeliveryZones();
        $this->seedDeliveryAssignments();
        $this->seedDeliveryReviews();
        $this->seedDeliveryTips();
    }

    /**
     * Seed delivery personnel.
     */
    private function seedDeliveryPersonnel(): void
    {
        $branches = Branch::all();
        $users = User::whereDoesntHave('deliveryPersonnel')->limit(10)->get();

        foreach ($users as $index => $user) {
            $branch = $branches->random();
            
            DeliveryPersonnel::create([
                'user_id' => $user->id,
                'branch_id' => $branch->id,
                'license_number' => 'DL' . str_pad($index + 1, 6, '0', STR_PAD_LEFT),
                'license_expiry_date' => now()->addYears(2),
                'vehicle_type' => collect(['motorcycle', 'bicycle', 'car'])->random(),
                'vehicle_model' => collect(['Honda CB150', 'Yamaha Vixion', 'Toyota Avanza', 'Mountain Bike'])->random(),
                'vehicle_plate_number' => 'B' . rand(1000, 9999) . 'DEL',
                'phone_number' => '+62812' . rand(10000000, 99999999),
                'emergency_contact_name' => 'Emergency Contact ' . ($index + 1),
                'emergency_contact_phone' => '+62813' . rand(10000000, 99999999),
                'status' => collect(['active', 'inactive'])->random(),
                'is_verified' => rand(0, 1),
                'current_latitude' => -6.2 + (rand(-100, 100) / 1000),
                'current_longitude' => 106.8 + (rand(-100, 100) / 1000),
                'last_location_update' => now()->subMinutes(rand(1, 60)),
                'max_concurrent_deliveries' => rand(2, 5),
                'delivery_radius_km' => rand(500, 2000) / 100,
                'working_hours' => [
                    ['day' => 'monday', 'start_time' => '09:00', 'end_time' => '18:00'],
                    ['day' => 'tuesday', 'start_time' => '09:00', 'end_time' => '18:00'],
                    ['day' => 'wednesday', 'start_time' => '09:00', 'end_time' => '18:00'],
                    ['day' => 'thursday', 'start_time' => '09:00', 'end_time' => '18:00'],
                    ['day' => 'friday', 'start_time' => '09:00', 'end_time' => '18:00'],
                    ['day' => 'saturday', 'start_time' => '10:00', 'end_time' => '16:00'],
                ],
                'rating' => rand(35, 50) / 10,
                'total_deliveries' => rand(50, 500),
                'total_earnings' => rand(1000000, 10000000) / 100,
            ]);
        }
    }

    /**
     * Seed delivery zones.
     */
    private function seedDeliveryZones(): void
    {
        $branches = Branch::all();
        
        $zones = [
            [
                'name' => 'Central Jakarta',
                'description' => 'Central business district delivery zone',
                'coordinates' => [
                    [-6.1744, 106.8227],
                    [-6.1744, 106.8427],
                    [-6.1944, 106.8427],
                    [-6.1944, 106.8227],
                ],
                'delivery_fee' => 15000,
                'minimum_order_amount' => 50000,
                'estimated_delivery_time_minutes' => 30,
                'priority' => 1,
            ],
            [
                'name' => 'South Jakarta',
                'description' => 'Residential area with shopping centers',
                'coordinates' => [
                    [-6.2244, 106.7927],
                    [-6.2244, 106.8327],
                    [-6.2644, 106.8327],
                    [-6.2644, 106.7927],
                ],
                'delivery_fee' => 12000,
                'minimum_order_amount' => 40000,
                'estimated_delivery_time_minutes' => 35,
                'priority' => 2,
            ],
            [
                'name' => 'North Jakarta',
                'description' => 'Industrial and port area',
                'coordinates' => [
                    [-6.1044, 106.7827],
                    [-6.1044, 106.8427],
                    [-6.1444, 106.8427],
                    [-6.1444, 106.7827],
                ],
                'delivery_fee' => 18000,
                'minimum_order_amount' => 60000,
                'estimated_delivery_time_minutes' => 45,
                'priority' => 3,
            ],
            [
                'name' => 'East Jakarta',
                'description' => 'Suburban residential area',
                'coordinates' => [
                    [-6.1744, 106.8627],
                    [-6.1744, 106.9227],
                    [-6.2344, 106.9227],
                    [-6.2344, 106.8627],
                ],
                'delivery_fee' => 20000,
                'minimum_order_amount' => 70000,
                'estimated_delivery_time_minutes' => 50,
                'priority' => 4,
            ],
            [
                'name' => 'West Jakarta',
                'description' => 'Mixed commercial and residential',
                'coordinates' => [
                    [-6.1744, 106.7427],
                    [-6.1744, 106.8027],
                    [-6.2344, 106.8027],
                    [-6.2344, 106.7427],
                ],
                'delivery_fee' => 16000,
                'minimum_order_amount' => 55000,
                'estimated_delivery_time_minutes' => 40,
                'priority' => 2,
            ],
        ];

        foreach ($branches as $branch) {
            foreach ($zones as $zoneData) {
                DeliveryZone::create(array_merge($zoneData, [
                    'branch_id' => $branch->id,
                    'is_active' => rand(0, 1) ? true : false,
                ]));
            }
        }
    }

    /**
     * Seed delivery assignments.
     */
    private function seedDeliveryAssignments(): void
    {
        $orders = Order::whereDoesntHave('deliveryAssignment')
            ->where('order_type', 'delivery')
            ->limit(50)
            ->get();
        
        $personnel = DeliveryPersonnel::all();
        $zones = DeliveryZone::all();
        
        $statuses = ['pending', 'assigned', 'picked_up', 'in_transit', 'delivered', 'failed', 'cancelled'];
        
        foreach ($orders as $order) {
            $status = collect($statuses)->random();
            $assignedPersonnel = $personnel->random();
            $zone = $zones->where('branch_id', $order->branch_id)->random();
            
            $assignment = DeliveryAssignment::create([
                'order_id' => $order->id,
                'delivery_personnel_id' => in_array($status, ['pending']) ? null : $assignedPersonnel->id,
                'delivery_zone_id' => $zone->id,
                'status' => $status,
                'pickup_latitude' => -6.2 + (rand(-50, 50) / 1000),
                'pickup_longitude' => 106.8 + (rand(-50, 50) / 1000),
                'pickup_address' => 'Restaurant Branch Address ' . $order->branch_id,
                'delivery_latitude' => -6.2 + (rand(-100, 100) / 1000),
                'delivery_longitude' => 106.8 + (rand(-100, 100) / 1000),
                'delivery_address' => 'Customer Address ' . $order->id,
                'distance_km' => rand(100, 2000) / 100,
                'estimated_duration_minutes' => rand(20, 60),
                'delivery_fee' => $zone->delivery_fee,
                'customer_phone' => '+62812' . rand(10000000, 99999999),
                'customer_name' => 'Customer ' . $order->id,
                'special_instructions' => rand(0, 1) ? 'Please call when arrived' : null,
            ]);
            
            // Set timestamps based on status
            $this->setAssignmentTimestamps($assignment, $status);
        }
    }
    
    /**
     * Set assignment timestamps based on status.
     */
    private function setAssignmentTimestamps(DeliveryAssignment $assignment, string $status): void
    {
        $now = now();
        
        switch ($status) {
            case 'assigned':
                $assignment->assigned_at = $now->subMinutes(rand(5, 30));
                break;
                
            case 'picked_up':
                $assignment->assigned_at = $now->subMinutes(rand(30, 60));
                $assignment->picked_up_at = $now->subMinutes(rand(10, 25));
                break;
                
            case 'in_transit':
                $assignment->assigned_at = $now->subMinutes(rand(40, 80));
                $assignment->picked_up_at = $now->subMinutes(rand(20, 35));
                $assignment->in_transit_at = $now->subMinutes(rand(5, 15));
                break;
                
            case 'delivered':
                $assignment->assigned_at = $now->subMinutes(rand(60, 120));
                $assignment->picked_up_at = $now->subMinutes(rand(40, 80));
                $assignment->in_transit_at = $now->subMinutes(rand(20, 35));
                $assignment->delivered_at = $now->subMinutes(rand(1, 10));
                $assignment->delivery_proof = [
                    'photo' => 'delivery_proof_' . $assignment->id . '.jpg',
                    'signature' => 'signature_' . $assignment->id . '.png',
                    'recipient_name' => $assignment->customer_name,
                    'notes' => 'Package delivered successfully'
                ];
                break;
                
            case 'failed':
                $assignment->assigned_at = $now->subMinutes(rand(60, 120));
                $assignment->picked_up_at = $now->subMinutes(rand(40, 80));
                $assignment->in_transit_at = $now->subMinutes(rand(20, 35));
                $assignment->failed_at = $now->subMinutes(rand(1, 10));
                $assignment->failure_reason = collect([
                    'Customer not available',
                    'Wrong address',
                    'Customer refused delivery',
                    'Vehicle breakdown'
                ])->random();
                break;
                
            case 'cancelled':
                $assignment->cancelled_at = $now->subMinutes(rand(1, 30));
                break;
        }
        
        $assignment->save();
    }
    
    /**
     * Seed delivery reviews.
     */
    private function seedDeliveryReviews(): void
    {
        $assignments = DeliveryAssignment::where('status', 'delivered')->get();
        $customers = Customer::all();
        
        $reviewCategories = ['punctuality', 'professionalism', 'food_condition', 'communication'];
        $comments = [
            'Excellent delivery service! Very professional.',
            'Food arrived hot and on time.',
            'Driver was very polite and helpful.',
            'Quick delivery, great service.',
            'Food was well packaged and delivered safely.',
            'Driver called before arriving, very considerate.',
            'Outstanding service, will order again.',
            'Delivery was faster than expected.',
            'Professional and friendly driver.',
            'Food arrived in perfect condition.'
        ];
        
        foreach ($assignments->take(30) as $assignment) {
            if (rand(0, 100) < 70) { // 70% chance of getting a review
                $customer = $customers->random();
                $rating = rand(3, 5); // Mostly positive reviews
                
                DeliveryReview::create([
                    'delivery_assignment_id' => $assignment->id,
                    'customer_id' => $customer->id,
                    'rating' => $rating,
                    'comment' => collect($comments)->random(),
                    'review_categories' => collect($reviewCategories)->random(rand(1, 3))->toArray(),
                    'is_anonymous' => rand(0, 1),
                    'is_verified' => rand(0, 1),
                    'helpful_count' => rand(0, 10),
                    'created_at' => $assignment->delivered_at ?? now(),
                ]);
            }
        }
    }
    
    /**
     * Seed delivery tips.
     */
    private function seedDeliveryTips(): void
    {
        $assignments = DeliveryAssignment::where('status', 'delivered')->get();
        $customers = Customer::all();
        
        $paymentMethods = ['cash', 'card', 'digital_wallet', 'app_credit'];
        $tipNotes = [
            'Great service!',
            'Thank you for the quick delivery',
            'Excellent driver',
            'Keep up the good work',
            'Very professional',
            null, // Some tips without notes
        ];
        
        foreach ($assignments->take(20) as $assignment) {
            if (rand(0, 100) < 40) { // 40% chance of getting a tip
                $customer = $customers->random();
                $amount = collect([5000, 10000, 15000, 20000, 25000])->random(); // Indonesian Rupiah
                $paymentMethod = collect($paymentMethods)->random();
                
                DeliveryTip::create([
                    'delivery_assignment_id' => $assignment->id,
                    'customer_id' => $customer->id,
                    'amount' => $amount,
                    'payment_method' => $paymentMethod,
                    'transaction_reference' => 'TIP' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
                    'status' => collect(['pending', 'paid', 'failed'])->random(),
                    'notes' => collect($tipNotes)->random(),
                    'processed_at' => rand(0, 1) ? now()->subMinutes(rand(1, 60)) : null,
                    'created_at' => $assignment->delivered_at ?? now(),
                ]);
            }
        }
    }
}