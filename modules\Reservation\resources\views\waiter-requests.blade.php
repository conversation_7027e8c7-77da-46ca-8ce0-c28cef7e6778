@extends('layouts.master')

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<style>
.status-pending { 
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200; 
}
.status-in_progress { 
    @apply bg-blue-100 text-blue-800 border border-blue-200; 
}
.status-completed { 
    @apply bg-green-100 text-green-800 border border-green-200; 
}
.status-cancelled { 
    @apply bg-red-100 text-red-800 border border-red-200; 
}

.request-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 mb-4 cursor-pointer;
}

.request-card:hover {
    @apply shadow-md border-gray-300;
}

.request-card.status-pending {
    @apply border-l-4 border-l-yellow-400;
}

.request-card.status-in_progress {
    @apply border-l-4 border-l-blue-400;
}

.request-card.status-completed {
    @apply border-l-4 border-l-green-400;
}

.request-card.status-cancelled {
    @apply border-l-4 border-l-red-400;
}

.request-type-service {
    @apply bg-blue-500;
}

.request-type-bill {
    @apply bg-green-500;
}

.request-type-assistance {
    @apply bg-yellow-500;
}

.request-type-complaint {
    @apply bg-red-500;
}

.btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700;
}

.btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700;
}

.btn-warning {
    @apply bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600 hover:border-yellow-700;
}

.btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700;
}

.btn-info {
    @apply bg-cyan-600 hover:bg-cyan-700 text-white border-cyan-600 hover:border-cyan-700;
}

.btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700;
}




</style>
@endpush

@section('title')
طلبات النادل
@stop

@section('page-header')
<div class="flex justify-between items-center">
    <div class="my-auto">
        <div class="flex items-center">
            <h4 class="text-lg font-semibold text-gray-800 mb-0 my-auto">طلبات النادل</h4>
            <span class="text-gray-500 text-sm mr-2 mb-0">/ الطلبات</span>
        </div>
    </div>
    <div class="flex my-xl-auto">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md ml-2 flex items-center gap-2" onclick="showModal('addRequestModal')">
                <i class="mdi mdi-plus"></i>
                <span>إضافة طلب</span>
            </button>
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="grid grid-cols-1 gap-6">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-clock text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">في الانتظار</p>
                    <p class="text-lg font-semibold text-gray-900" id="pending-count">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-spinner text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">قيد التنفيذ</p>
                    <p class="text-lg font-semibold text-gray-900" id="in-progress-count">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">مكتمل</p>
                    <p class="text-lg font-semibold text-gray-900" id="completed-count">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-times text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">ملغي</p>
                    <p class="text-lg font-semibold text-gray-900" id="cancelled-count">0</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-span-full">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-semibold text-gray-800 mb-0">طلبات النادل</h4>
                    <button type="button" id="refresh-requests" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>تحديث
                    </button>
                </div>
                <p class="text-sm text-gray-500 mb-2">إدارة جميع طلبات النادل</p>
            </div>
            
            <!-- Filters -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="in_progress">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نوع الطلب</label>
                        <select id="type-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الأنواع</option>
                            <option value="service">خدمة</option>
                            <option value="bill">فاتورة</option>
                            <option value="assistance">مساعدة</option>
                            <option value="complaint">شكوى</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">النادل</label>
                        <select id="waiter-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع النادلين</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4 flex gap-2">
                    <button type="button" id="apply-filters" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">تطبيق الفلاتر</button>
                    <button type="button" id="clear-filters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">مسح الفلاتر</button>
                </div>
            </div>

            <div class="p-6">
                <div id="requests-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <!-- Request cards will be loaded here -->
                </div>
                
                <!-- Loading indicator -->
                <div id="loading-indicator" class="text-center py-8 hidden">
                    <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        جاري التحميل...
                    </div>
                </div>
                
                <!-- Empty state -->
                <div id="empty-state" class="text-center py-12 hidden">
                    <div class="mx-auto h-12 w-12 text-gray-400">
                        <i class="fas fa-inbox text-4xl"></i>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد طلبات</h3>
                    <p class="mt-1 text-sm text-gray-500">لم يتم العثور على أي طلبات تطابق المعايير المحددة.</p>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

<!-- Add Request Modal -->
<div id="addRequestModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="addRequestModal" class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">إضافة طلب جديد</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('addRequestModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="addRequestForm" class="p-6">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="table-select" class="block text-sm font-medium text-gray-700 mb-2">الطاولة *</label>
                    <select id="table-select" name="table_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">اختر الطاولة</option>
                    </select>
                </div>

                <div>
                    <label for="request-type" class="block text-sm font-medium text-gray-700 mb-2">نوع الطلب *</label>
                    <select id="request-type" name="request_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">اختر نوع الطلب</option>
                        <option value="service">خدمة</option>
                        <option value="bill">فاتورة</option>
                        <option value="assistance">مساعدة</option>
                        <option value="complaint">شكوى</option>
                    </select>
                </div>

                <div>
                    <label for="waiter-select" class="block text-sm font-medium text-gray-700 mb-2">النادل</label>
                    <select id="waiter-select" name="waiter_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">اختر النادل (اختياري)</option>
                    </select>
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="pending">في الانتظار</option>
                        <option value="in_progress">قيد التنفيذ</option>
                        <option value="completed">مكتمل</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>
            </div>

            <div class="mt-4">
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                <textarea id="notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
            </div>

            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('addRequestModal')">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="fas fa-plus mr-2"></i>إضافة الطلب
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Request Modal -->
<div id="editRequestModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="editRequestModal" class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">تعديل الطلب</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('editRequestModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="editRequestForm" class="p-6">
            @csrf
            @method('PUT')
            <input type="hidden" id="edit-request-id" name="id">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="edit-table-select" class="block text-sm font-medium text-gray-700 mb-2">الطاولة *</label>
                    <select id="edit-table-select" name="table_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">اختر الطاولة</option>
                    </select>
                </div>

                <div>
                    <label for="edit-request-type" class="block text-sm font-medium text-gray-700 mb-2">نوع الطلب *</label>
                    <select id="edit-request-type" name="request_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">اختر نوع الطلب</option>
                        <option value="service">خدمة</option>
                        <option value="bill">فاتورة</option>
                        <option value="assistance">مساعدة</option>
                        <option value="complaint">شكوى</option>
                    </select>
                </div>

                <div>
                    <label for="edit-waiter-select" class="block text-sm font-medium text-gray-700 mb-2">النادل</label>
                    <select id="edit-waiter-select" name="waiter_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">اختر النادل (اختياري)</option>
                    </select>
                </div>

                <div>
                    <label for="edit-status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="edit-status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="pending">في الانتظار</option>
                        <option value="in_progress">قيد التنفيذ</option>
                        <option value="completed">مكتمل</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>
            </div>

            <div class="mt-4">
                <label for="edit-notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                <textarea id="edit-notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
            </div>

            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('editRequestModal')">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-yellow-600 border border-transparent rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                    <i class="fas fa-save mr-2"></i>حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Request Modal -->
<div id="viewRequestModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="viewRequestModal" class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">تفاصيل الطلب</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('viewRequestModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="p-6">
            <div id="request-details" class="space-y-4">
                <!-- Request details will be loaded here -->
            </div>

            <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('viewRequestModal')">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Assign Request Modal -->
<div id="assignRequestModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="assignRequestModal" class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/3 lg:w-1/4 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">تكليف النادل</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('assignRequestModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="assignRequestForm" class="p-6">
            @csrf
            <input type="hidden" id="assign-request-id" name="request_id">

            <div class="mb-4">
                <label for="assign-waiter-select" class="block text-sm font-medium text-gray-700 mb-2">اختر النادل *</label>
                <select id="assign-waiter-select" name="waiter_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    <option value="">اختر النادل</option>
                </select>
            </div>

            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" id="assign-to-self" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="mr-2 text-sm text-gray-700">تكليف نفسي بهذا الطلب</span>
                </label>
            </div>

            <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('assignRequestModal')">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                    <i class="fas fa-user-plus mr-2"></i>تكليف
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Complete Request Modal -->
<div id="completeRequestModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="completeRequestModal" class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/3 lg:w-1/4 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">إكمال الطلب</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('completeRequestModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="completeRequestForm" class="p-6">
            @csrf
            <input type="hidden" id="complete-request-id" name="request_id">

            <div class="mb-4">
                <p class="text-sm text-gray-600 mb-4">هل أنت متأكد من إكمال هذا الطلب؟</p>

                <div class="mb-4">
                    <label for="completion-notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات الإكمال (اختياري)</label>
                    <textarea id="completion-notes" name="completion_notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="أدخل أي ملاحظات حول إكمال الطلب..."></textarea>
                </div>
            </div>

            <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('completeRequestModal')">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                    <i class="fas fa-check mr-2"></i>إكمال الطلب
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Tailwind Modal Functions
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    const backdrop = document.getElementById(modalId + 'Backdrop');
    if (modal && backdrop) {
        modal.classList.remove('hidden');
        modal.classList.add('show');
        backdrop.classList.remove('hidden');
        backdrop.classList.add('show');
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    const backdrop = document.getElementById(modalId + 'Backdrop');
    if (modal && backdrop) {
        modal.classList.add('hidden');
        modal.classList.remove('show');
        backdrop.classList.add('hidden');
        backdrop.classList.remove('show');
    }
}

// Close modal when clicking backdrop
function setupModalBackdropClose() {
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
        backdrop.addEventListener('click', function(e) {
            if (e.target === this) {
                const modalId = this.id.replace('Backdrop', '');
                hideModal(modalId);
            }
        });
    });
}

$(document).ready(function() {
    // Setup modal backdrop close functionality
    setupModalBackdropClose();
    
    // Load initial data
    loadTables();
    loadWaiters();
    loadStatistics();
    loadRequests();
    
    // Auto refresh every 30 seconds
    setInterval(function() {
        loadStatistics();
        loadRequests();
    }, 30000);

    function loadTables() {
        $.get('{{ route("reservation.tables.index") }}', function(data) {
            var tableSelects = ['#table-select', '#edit-table-select'];
            tableSelects.forEach(function(selector) {
                $(selector).empty().append('<option value="">اختر الطاولة</option>');
                data.forEach(function(table) {
                    $(selector).append('<option value="' + table.id + '">' + table.table_name + ' - ' + (table.area ? table.area.name : 'بدون منطقة') + '</option>');
                });
            });
        });
    }

    function loadWaiters() {
        $.get('{{ route("reservation.waiters") }}', function(data) {
            var waiterSelects = ['#waiter-select', '#edit-waiter-select', '#waiter-filter'];
            waiterSelects.forEach(function(selector) {
                var defaultOption = selector === '#waiter-filter' ? 'جميع النادلين' : 'اختر النادل (اختياري)';
                $(selector).empty().append('<option value="">' + defaultOption + '</option>');
                data.forEach(function(waiter) {
                    $(selector).append('<option value="' + waiter.id + '">' + waiter.name + '</option>');
                });
            });
        });
    }

    function loadStatistics() {
        $.get('{{ route("reservation.waiter-requests.statistics") }}', function(data) {
            $('#pending-count').text(data.pending || 0);
            $('#in-progress-count').text(data.in_progress || 0);
            $('#completed-count').text(data.completed || 0);
            $('#cancelled-count').text(data.cancelled || 0);
        });
    }

    function loadRequests() {
        $('#loading-indicator').removeClass('hidden');
        $('#empty-state').addClass('hidden');
        
        var filters = {
            status: $('#status-filter').val(),
            request_type: $('#type-filter').val(),
            waiter_id: $('#waiter-filter').val()
        };

        $.get('{{ route("reservation.waiter-requests.cards") }}', filters, function(response) {
            $('#loading-indicator').addClass('hidden');
            
            // Access the data property from the response
            var data = response.data || [];
            
            if (data.length === 0) {
                $('#empty-state').removeClass('hidden');
                $('#requests-container').empty();
                return;
            }

            var html = '';
            data.forEach(function(request) {
                html += generateRequestCard(request);
            });
            
            $('#requests-container').html(html);
        }).fail(function() {
            $('#loading-indicator').addClass('hidden');
            Swal.fire('خطأ!', 'حدث خطأ أثناء تحميل الطلبات', 'error');
        });
    }

    function generateRequestCard(request) {
        var statusClass = 'status-' + request.status;
        var typeClass = 'request-type-' + request.request_type;
        
        var statusLabels = {
            'pending': 'في الانتظار',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        };
        
        var typeLabels = {
            'service': 'خدمة',
            'bill': 'فاتورة',
            'assistance': 'مساعدة',
            'complaint': 'شكوى'
        };

        var typeIcons = {
            'service': 'fas fa-concierge-bell',
            'bill': 'fas fa-receipt',
            'assistance': 'fas fa-hands-helping',
            'complaint': 'fas fa-exclamation-triangle'
        };

        return `
            <div class="request-card ${statusClass}" data-id="${request.id}">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-8 h-8 ${typeClass} rounded-full flex items-center justify-center mr-3">
                                <i class="${typeIcons[request.request_type]} text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900">${typeLabels[request.request_type]}</h3>
                                <p class="text-xs text-gray-500">طلب #${request.id}</p>
                            </div>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                            ${statusLabels[request.status]}
                        </span>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-table mr-2 text-xs"></i>
                            <span>${request.table ? request.table.table_name : 'غير محدد'}</span>
                            ${request.table && request.table.area ? '<span class="text-gray-400 mr-1">(' + request.table.area.name + ')</span>' : ''}
                        </div>
                        
                        ${request.waiter ? `
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-user mr-2 text-xs"></i>
                                <span>${request.waiter.name}</span>
                            </div>
                        ` : ''}
                        
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock mr-2 text-xs"></i>
                            <span>${new Date(request.created_at).toLocaleString('ar-EG')}</span>
                        </div>
                    </div>
                    
                    ${request.notes ? `
                        <div class="mb-4">
                            <p class="text-sm text-gray-700 bg-gray-50 p-2 rounded">${request.notes}</p>
                        </div>
                    ` : ''}
                    
                    <div class="flex gap-2">
                        <button onclick="viewRequest(${request.id})" class="flex-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700">
                            <i class="fas fa-eye mr-1"></i>عرض
                        </button>
                        
                        ${request.status !== 'completed' && request.status !== 'cancelled' ? `
                            <button onclick="editRequest(${request.id})" class="flex-1 px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700">
                                <i class="fas fa-edit mr-1"></i>تعديل
                            </button>
                        ` : ''}
                        
                        ${request.status === 'pending' ? `
                            <button onclick="assignRequest(${request.id})" class="flex-1 px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700">
                                <i class="fas fa-user-plus mr-1"></i>تكليف
                            </button>
                        ` : ''}
                        
                        ${request.status === 'in_progress' ? `
                            <button onclick="completeRequest(${request.id})" class="flex-1 px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700">
                                <i class="fas fa-check mr-1"></i>إكمال
                            </button>
                        ` : ''}
                        
                        ${request.status !== 'completed' ? `
                            <button onclick="deleteRequest(${request.id})" class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // Filter functionality
    $('#apply-filters, #refresh-requests').click(function() {
        loadRequests();
    });

    $('#clear-filters').click(function() {
        $('#status-filter').val('');
        $('#type-filter').val('');
        $('#waiter-filter').val('');
        loadRequests();
    });

    // Add request form
    $('#addRequestForm').submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '{{ route("reservation.waiter-requests.store") }}',
            method: 'POST',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                hideModal('addRequestModal');
                $('#addRequestForm')[0].reset();
                loadRequests();
                loadStatistics();
                Swal.fire('نجح!', 'تم إضافة الطلب بنجاح', 'success');
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة الطلب';
                if (errors) {
                    errorMessage = Object.values(errors).flat().join('\n');
                }
                Swal.fire('خطأ!', errorMessage, 'error');
            }
        });
    });

    // Edit request form
    $('#editRequestForm').submit(function(e) {
        e.preventDefault();

        var requestId = $('#edit-request-id').val();

        $.ajax({
            url: '{{ route("reservation.waiter-requests.update", ":id") }}'.replace(':id', requestId),
            method: 'PUT',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                hideModal('editRequestModal');
                loadRequests();
                loadStatistics();
                Swal.fire('نجح!', 'تم تحديث الطلب بنجاح', 'success');
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث الطلب';
                if (errors) {
                    errorMessage = Object.values(errors).flat().join('\n');
                }
                Swal.fire('خطأ!', errorMessage, 'error');
            }
        });
    });

    // Assign request form
    $('#assignRequestForm').submit(function(e) {
        e.preventDefault();

        var requestId = $('#assign-request-id').val();
        var waiterId = $('#assign-waiter-select').val();
        var assignToSelf = $('#assign-to-self').is(':checked');

        if (!waiterId && !assignToSelf) {
            Swal.fire('تنبيه!', 'يرجى اختيار نادل أو تحديد تكليف نفسك', 'warning');
            return;
        }

        $.ajax({
            url: '{{ route("reservation.waiter-requests.assign", ":id") }}'.replace(':id', requestId),
            method: 'POST',
            data: {
                waiter_id: assignToSelf ? null : waiterId,
                assign_to_self: assignToSelf
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                hideModal('assignRequestModal');
                $('#assignRequestForm')[0].reset();
                loadRequests();
                loadStatistics();
                Swal.fire('نجح!', 'تم تكليف الطلب بنجاح', 'success');
            },
            error: function(xhr) {
                Swal.fire('خطأ!', 'حدث خطأ أثناء تكليف الطلب', 'error');
            }
        });
    });

    // Complete request form
    $('#completeRequestForm').submit(function(e) {
        e.preventDefault();

        var requestId = $('#complete-request-id').val();

        $.ajax({
            url: '{{ route("reservation.waiter-requests.complete", ":id") }}'.replace(':id', requestId),
            method: 'POST',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                hideModal('completeRequestModal');
                $('#completeRequestForm')[0].reset();
                loadRequests();
                loadStatistics();
                Swal.fire('نجح!', 'تم إكمال الطلب بنجاح', 'success');
            },
            error: function(xhr) {
                Swal.fire('خطأ!', 'حدث خطأ أثناء إكمال الطلب', 'error');
            }
        });
    });

    // Handle assign to self checkbox
    $('#assign-to-self').change(function() {
        if ($(this).is(':checked')) {
            $('#assign-waiter-select').prop('disabled', true).val('');
        } else {
            $('#assign-waiter-select').prop('disabled', false);
        }
    });
});

// Global functions for actions
function viewRequest(id) {
    $.get('{{ route("reservation.waiter-requests.show", ":id") }}'.replace(':id', id), function(data) {
        var request = data.data;
        
        var statusLabels = {
            'pending': 'في الانتظار',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        };
        
        var typeLabels = {
            'service': 'خدمة',
            'bill': 'فاتورة',
            'assistance': 'مساعدة',
            'complaint': 'شكوى'
        };
        
        var html = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div><strong>رقم الطلب:</strong> #${request.id}</div>
                <div><strong>نوع الطلب:</strong> ${typeLabels[request.request_type] || request.request_type}</div>
                <div><strong>الحالة:</strong> ${statusLabels[request.status] || request.status}</div>
                <div><strong>الطاولة:</strong> ${request.table ? request.table.table_name : 'غير محدد'}</div>
                <div><strong>المنطقة:</strong> ${request.table && request.table.area ? request.table.area.name : 'غير محدد'}</div>
                <div><strong>النادل:</strong> ${request.waiter ? request.waiter.name : 'غير مكلف'}</div>
                <div><strong>تاريخ الإنشاء:</strong> ${new Date(request.created_at).toLocaleString('ar-EG')}</div>
                <div><strong>آخر تحديث:</strong> ${new Date(request.updated_at).toLocaleString('ar-EG')}</div>
            </div>
            ${request.notes ? '<div class="mt-4"><strong>ملاحظات:</strong><br>' + request.notes + '</div>' : ''}
        `;
        
        $('#request-details').html(html);
        showModal('viewRequestModal');
    });
}

function editRequest(id) {
    $.get('{{ route("reservation.waiter-requests.edit", ":id") }}'.replace(':id', id), function(data) {
        var request = data.data;
        
        $('#edit-request-id').val(request.id);
        $('#edit-table-select').val(request.table_id);
        $('#edit-request-type').val(request.request_type);
        $('#edit-status').val(request.status);
        $('#edit-waiter-select').val(request.waiter_id);
        $('#edit-notes').val(request.notes);
        
        showModal('editRequestModal');
    });
}

function assignRequest(id) {
    $('#assign-request-id').val(id);
    showModal('assignRequestModal');
}

function completeRequest(id) {
    $('#complete-request-id').val(id);
    showModal('completeRequestModal');
}

function deleteRequest(id) {
    Swal.fire({
        title: 'حذف الطلب',
        text: 'هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، حذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#d33'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route("reservation.waiter-requests.destroy", ":id") }}'.replace(':id', id),
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    loadRequests();
                    loadStatistics();
                    Swal.fire('تم الحذف!', 'تم حذف الطلب بنجاح', 'success');
                },
                error: function(xhr) {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء حذف الطلب', 'error');
                }
            });
        }
    });
}
</script>
@endpush