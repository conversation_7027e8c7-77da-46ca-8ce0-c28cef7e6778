

<?php $__env->startSection('css'); ?>
<!-- DataTables CSS from CDN -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
<!-- Select2 CSS from CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<!-- SweetAlert CSS from CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert@2.1.2/dist/sweetalert.min.css">
<style>
    /* Status badge styles */
    .status-available { 
        @apply bg-green-100 text-green-800 border border-green-200; 
    }
    .status-occupied { 
        @apply bg-red-100 text-red-800 border border-red-200; 
    }
    .status-reserved { 
        @apply bg-yellow-100 text-yellow-800 border border-yellow-200; 
    }
    .status-cleaning { 
        @apply bg-blue-100 text-blue-800 border border-blue-200; 
    }
    .status-out_of_order { 
        @apply bg-gray-100 text-gray-800 border border-gray-200; 
    }
    
    /* Custom animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .animate-fade-in-up {
        animation: fadeInUp 0.3s ease-out;
    }
    
    /* Responsive grid improvements */
    @media (max-width: 640px) {
        #tables-container {
            grid-template-columns: 1fr;
        }
    }
    
    @media (min-width: 641px) and (max-width: 1024px) {
        #tables-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (min-width: 1025px) {
        #tables-container {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (min-width: 1280px) {
        #tables-container {
            grid-template-columns: repeat(4, 1fr);
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="flex justify-between items-center">
    <div class="my-auto">
        <div class="flex items-center">
            <h4 class="text-lg font-semibold text-gray-800 mb-0 my-auto">إدارة الطاولات</h4>
            <span class="text-gray-500 text-sm mr-2 mb-0">/ الطاولات</span>
        </div>
    </div>
    <div class="flex my-xl-auto">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200 ml-2 flex items-center gap-2" id="add-table-btn">
                <i class="mdi mdi-plus"></i>
                <span>إضافة طاولة</span>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- row -->
<div class="grid grid-cols-1 gap-6">
    <div class="col-span-full">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-semibold text-gray-800 mb-0">قائمة الطاولات</h4>
                    <div class="view-toggle">
                        <div class="inline-flex rounded-md shadow-sm" role="group">
                            <button type="button" class="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-l-md hover:bg-blue-100 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:bg-blue-100 active" id="cards-view-btn">
                                <i class="mdi mdi-view-grid"></i> عرض البطاقات
                            </button>
                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-r-md hover:bg-gray-50 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:bg-gray-50" id="table-view-btn">
                                <i class="mdi mdi-table"></i> عرض جدولي
                            </button>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mb-2">إدارة جميع طاولات المطعم مع رموز QR</p>
            </div>
            <div class="p-6">
                <!-- Filters -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="area-filter">
                            <option value="">جميع المناطق</option>
                        </select>
                    </div>
                    <div>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="available">متاحة</option>
                            <option value="occupied">مشغولة</option>
                            <option value="reserved">محجوزة</option>
                            <option value="cleaning">تنظيف</option>
                            <option value="out_of_order">خارج الخدمة</option>
                        </select>
                    </div>
                    <div>
                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="capacity-filter" placeholder="الحد الأدنى للسعة" min="1">
                    </div>
                    <div class="flex gap-2">
                        <button type="button" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="apply-filters">تطبيق الفلاتر</button>
                        <button type="button" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="clear-filters">مسح</button>
                    </div>
                </div>

                <!-- Cards View -->
                <div id="cards-view">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" id="tables-container">
                        <!-- Tables will be loaded here -->
                    </div>
                </div>

                <!-- Table View -->
                <div id="table-view" style="display: none;">
                    <div class="overflow-x-auto">
                        <table id="tables-table" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الطاولة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الطاولة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنطقة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">QR Code</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">QR يدوي</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add Table Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="addTableModal" tabindex="-1" role="dialog" aria-labelledby="addTableModalLabel" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>
        <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-900" id="addTableModalLabel">إضافة طاولة جديدة</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-2xl">&times;</span>
                </button>
            </div>
            <div class="pt-6">
                <form id="addTableForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label for="table_number" class="block text-sm font-medium text-gray-700 mb-2">رقم الطاولة <span class="text-red-500">*</span></label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="table_number" name="table_number" required maxlength="20">
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label for="table_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الطاولة</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="table_name" name="table_name" maxlength="255">
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label for="area_id" class="block text-sm font-medium text-gray-700 mb-2">المنطقة <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="area_id" name="area_id" required>
                                    <option value="">اختر المنطقة</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label for="seating_capacity" class="block text-sm font-medium text-gray-700 mb-2">السعة <span class="text-red-500">*</span></label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="seating_capacity" name="seating_capacity" required min="1" max="50">
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label for="section" class="block text-sm font-medium text-gray-700 mb-2">القسم</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="section" name="section">
                                    <option value="">اختر القسم</option>
                                    <option value="Indoor">داخلي</option>
                                    <option value="Outdoor">خارجي</option>
                                    <option value="VIP">VIP</option>
                                    <option value="Terrace">تراس</option>
                                    <option value="Garden">حديقة</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="status" name="status" required>
                                    <option value="available">متاحة</option>
                                    <option value="occupied">مشغولة</option>
                                    <option value="reserved">محجوزة</option>
                                    <option value="cleaning">تنظيف</option>
                                    <option value="out_of_order">خارج الخدمة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label for="position_x" class="block text-sm font-medium text-gray-700 mb-2">موقع X (اختياري)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="position_x" name="position_x" step="0.01" placeholder="إحداثي X">
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label for="position_y" class="block text-sm font-medium text-gray-700 mb-2">موقع Y (اختياري)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="position_y" name="position_y" step="0.01" placeholder="إحداثي Y">
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية حول الطاولة"></textarea>
                    </div>

                    <div class="mb-4">
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="is_active" name="is_active" checked>
                            <label class="mr-2 block text-sm text-gray-900" for="is_active">
                                نشطة
                            </label>
                        </div>
                    </div>

                    <!-- QR Code Section -->
                    <hr class="my-6 border-gray-200">
                    <h6 class="text-base font-medium text-gray-900 mb-4">إعدادات QR Code</h6>
                    <div class="mb-4">
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="generate_qr" name="generate_qr">
                            <label class="mr-2 block text-sm text-gray-900" for="generate_qr">
                                إنشاء QR Code للطاولة
                            </label>
                        </div>
                    </div>

                    <div id="qr-options" style="display: none;">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">نوع QR Code:</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="radio" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" id="auto_qr" name="qr_type" value="auto" checked>
                                    <label class="mr-2 block text-sm text-gray-900" for="auto_qr">
                                        إنشاء تلقائي
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" id="custom_qr" name="qr_type" value="custom">
                                    <label class="mr-2 block text-sm text-gray-900" for="custom_qr">
                                        QR Code مخصص
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4" id="custom-qr-input" style="display: none;">
                            <label for="custom_qr_code" class="block text-sm font-medium text-gray-700 mb-2">QR Code مخصص:</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="custom_qr_code" name="custom_qr_code" maxlength="255">
                            <small class="text-sm text-gray-500">أدخل QR Code مخصص للطاولة</small>
                        </div>
                    </div>

                    <!-- QR Code Preview -->
                    <div id="qr-preview" class="text-center mt-6" style="display: none;">
                        <h6 class="text-base font-medium text-gray-900 mb-4">معاينة QR Code:</h6>
                        <div id="qr-image"></div>
                        <p id="qr-url" class="mt-2 text-sm text-gray-500"></p>
                    </div>
                </form>
            </div>
            <div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="save-table">حفظ</button>
                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Manual QR Code Setting Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="manualQrModal" tabindex="-1" role="dialog" aria-labelledby="manualQrModalLabel" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>
        <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-900" id="manualQrModalLabel">تعيين QR Code يدوياً</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-2xl">&times;</span>
                </button>
            </div>
            <div class="pt-6">
                <form id="manualQrForm">
                    <div class="mb-4">
                        <label for="manual-qr-code" class="block text-sm font-medium text-gray-700 mb-2">QR Code:</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="manual-qr-code" name="qr_code" required>
                        <small class="text-sm text-gray-500">أدخل QR Code مخصص للطاولة</small>
                    </div>
                    <div id="manual-qr-preview" class="text-center mt-6" style="display: none;">
                        <h6 class="text-base font-medium text-gray-900 mb-4">معاينة QR Code:</h6>
                        <div id="manual-qr-image"></div>
                    </div>
                </form>
            </div>
            <div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="preview-manual-qr">معاينة</button>
                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="save-manual-qr">حفظ</button>
                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Display Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="qrModal" tabindex="-1" role="dialog" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>
        <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-900" id="qrModalLabel">QR Code</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-2xl">&times;</span>
                </button>
            </div>
            <div class="pt-6 text-center">
                <div id="qr-table-info" class="mb-4"></div>
                <div id="qr-code-container" class="mb-4"></div>
                <div id="qr-url-info" class="mb-4"></div>
            </div>
            <div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="download-qr" onclick="downloadQR($(this).data('qr-url'), $(this).data('filename'))">تحميل QR Code</button>
                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables JS from CDN -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap4.min.js"></script>
<!-- Select2 JS from CDN -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert JS from CDN -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert@2.1.2/dist/sweetalert.min.js"></script>

<script>
$(document).ready(function() {
    let tablesTable;
    let currentView = 'cards';

    // Initialize
    loadTables();
    loadAreas(); // Load areas for the dropdown

    // Add Table Button Click Event
    $('#add-table-btn').click(function() {
        $('#addTableForm')[0].reset();
        $('#addTableModal').removeClass('hidden');
    });

    // Modal close handlers
    $('[data-dismiss="modal"]').click(function() {
        $(this).closest('.fixed').addClass('hidden');
    });

    // Close modal when clicking on backdrop
    $('.fixed.inset-0').click(function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });

    // Save Table Button Click Event
    $('#save-table').click(function() {
        let formData = {
            table_number: $('#table_number').val(),
            table_name: $('#table_name').val(),
            area_id: $('#area_id').val(),
            seating_capacity: $('#seating_capacity').val(),
            section: $('#section').val(),
            status: $('#status').val(),
            notes: $('#notes').val(),
            is_active: $('#is_active').is(':checked') ? 1 : 0,
            _token: '<?php echo e(csrf_token()); ?>'
        };

        // Handle position coordinates
        let positionX = $('#position_x').val();
        let positionY = $('#position_y').val();
        if (positionX && positionY) {
            formData.position_coordinates = JSON.stringify({
                x: parseFloat(positionX),
                y: parseFloat(positionY)
            });
        }

        // Handle QR code generation
        formData.generate_qr = $('#generate_qr').is(':checked') ? 1 : 0;

        if (formData.generate_qr) {
            formData.qr_type = $('input[name="qr_type"]:checked').val();
            if (formData.qr_type === 'custom') {
                formData.custom_qr_code = $('#custom_qr_code').val();
            }
        }

        // Validate required fields
        if (!formData.table_number || !formData.area_id || !formData.seating_capacity || !formData.status) {
            swal('تنبيه!', 'يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // Validate custom QR code if selected
        if (formData.generate_qr && $('input[name="qr_type"]:checked').val() === 'custom' && !formData.custom_qr_code) {
            swal('تنبيه!', 'يرجى إدخال QR Code مخصص', 'warning');
            return;
        }

        $.post('<?php echo e(route("tables.store")); ?>', formData, function(data) {
            if (data.success) {
                swal('نجح!', data.message, 'success');
                $('#addTableModal').addClass('hidden');
                
                // Show QR code if generated
                if (data.qr_data) {
                    setTimeout(() => {
                        showQRResult(data.qr_data);
                    }, 500);
                }
                
                if (currentView === 'cards') {
                    loadTables();
                } else {
                    tablesTable.ajax.reload();
                }
            }
        }).fail(function(xhr) {
            let message = 'حدث خطأ أثناء إضافة الطاولة';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                let errors = xhr.responseJSON.errors;
                message = Object.values(errors).flat().join('\n');
            }
            swal('خطأ!', message, 'error');
        });
    });

    // QR Code functionality
    $('#generate_qr').change(function() {
        if ($(this).is(':checked')) {
            $('#qr-options').show();
        } else {
            $('#qr-options').hide();
            $('#qr-preview').hide();
        }
    });

    $('input[name="qr_type"]').change(function() {
        if ($(this).val() === 'custom') {
            $('#custom-qr-input').show();
        } else {
            $('#custom-qr-input').hide();
        }
        $('#qr-preview').hide();
    });

    // Preview QR code
    $('#custom_qr_code').on('input', function() {
        let qrCode = $(this).val().trim();
        if (qrCode && $('input[name="qr_type"]:checked').val() === 'custom') {
            previewQRCode(qrCode);
        } else {
            $('#qr-preview').hide();
        }
    });

    function previewQRCode(qrCode) {
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug();
        let qrUrl = `${baseUrl}/restaurant/table/${qrCode}?hash=${tenantSlug}`;
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrUrl)}`;
        
        $('#qr-image').html(`<img src="${qrImageUrl}" alt="QR Code Preview" style="width: 150px; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; background: white;">`);
        $('#qr-url').text(qrUrl);
        $('#qr-preview').show();
    }

    function showQRResult(qrData) {
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData.url)}`;
        
        swal({
            title: 'تم إنشاء QR Code بنجاح!',
            content: {
                element: 'div',
                attributes: {
                    innerHTML: `
                        <div class="text-center">
                            <img src="${qrImageUrl}" alt="QR Code" style="width: 200px; height: 200px; border: 1px solid #ddd; border-radius: 4px; padding: 10px; background: white; margin: 10px 0;">
                            <p><strong>QR Code:</strong> ${qrData.qr_code}</p>
                            <p><strong>الرابط:</strong></p>
                            <p style="word-break: break-all; font-size: 12px;">${qrData.url}</p>
                        </div>
                    `
                }
            },
            buttons: {
                download: {
                    text: 'تحميل QR Code',
                    value: 'download',
                    className: 'btn-success'
                },
                copy: {
                    text: 'نسخ الرابط',
                    value: 'copy',
                    className: 'btn-primary'
                },
                close: {
                    text: 'إغلاق',
                    value: 'close',
                    className: 'btn-secondary'
                }
            }
        }).then((value) => {
            if (value === 'download') {
                downloadQR(qrImageUrl, `table-${qrData.table_number}-qr.png`);
            } else if (value === 'copy') {
                navigator.clipboard.writeText(qrData.url).then(() => {
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    let textArea = document.createElement('textarea');
                    textArea.value = qrData.url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                });
            }
        });
    }

    // Load areas for dropdown
    function loadAreas() {
        $.get('<?php echo e(route("reservation.areas.list")); ?>', function(data) {
            let areaSelect = $('#area_id');
            let areaFilter = $('#area-filter');
            
            areaSelect.empty().append('<option value="">اختر المنطقة</option>');
            areaFilter.empty().append('<option value="">جميع المناطق</option>');
            
            if (data && data.length > 0) {
                data.forEach(function(area) {
                    areaSelect.append(`<option value="${area.id}">${area.name}</option>`);
                    areaFilter.append(`<option value="${area.id}">${area.name}</option>`);
                });
            }
        }).fail(function() {
            console.log('Failed to load areas');
        });
    }

    // View toggle
    $('#cards-view-btn').click(function() {
        currentView = 'cards';
        $('#cards-view').show();
        $('#table-view').hide();
        $(this).addClass('active');
        $('#table-view-btn').removeClass('active');
        loadTables();
    });

    $('#table-view-btn').click(function() {
        currentView = 'table';
        $('#cards-view').hide();
        $('#table-view').show();
        $(this).addClass('active');
        $('#cards-view-btn').removeClass('active');
        initDataTable();
    });

    // Load tables for cards view
    function loadTables() {
        if (currentView !== 'cards') return;

        let filters = {
            area_id: $('#area-filter').val(),
            status: $('#status-filter').val(),
            capacity: $('#capacity-filter').val()
        };

        // Using the controller route
        $.get('<?php echo e(route("reservation.tables.datatable")); ?>', filters, function(data) {
            renderTablesCards(data.data || data);
        }).fail(function() {
            // Fallback to mock data for testing
            renderTablesCards([
                {
                    id: 1,
                    table_number: 'T001',
                    table_name: 'طاولة رقم 1',
                    seating_capacity: 4,
                    status: 'available',
                    area_name: 'الصالة الرئيسية',
                    qr_code: '436a1f405d08a1a57030802e561811ae'
                },
                {
                    id: 2,
                    table_number: 'T002',
                    table_name: 'طاولة رقم 2',
                    seating_capacity: 6,
                    status: 'occupied',
                    area_name: 'الصالة الرئيسية',
                    qr_code: '536a1f405d08a1a57030802e561811af'
                }
            ]);
        });
    }

    // Render tables as cards
    function renderTablesCards(tables) {
        let container = $('#tables-container');
        container.empty();

        if (!tables || tables.length === 0) {
            container.append('<div class="col-span-full"><p class="text-center text-gray-500 py-8">لا توجد طاولات</p></div>');
            return;
        }

        tables.forEach(function(table) {
            let statusClass = `status-${table.status}`;
            let statusText = getStatusText(table.status);
            
            let qrCodeUrl = generateTableUrl(table);
            let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrCodeUrl)}`;
            
            let card = `
                <div class="group">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-blue-300">
                        <!-- Table Header -->
                        <div class="flex justify-between items-start mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-bold text-gray-800 mb-1">${table.table_name || table.table_number}</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    طاولة ${table.table_number}
                                </span>
                            </div>
                            <span class="table-status ${statusClass} text-xs font-semibold px-3 py-1.5 rounded-full">${statusText}</span>
                        </div>
                        
                        <!-- QR Code Section -->
                        <div class="text-center my-6">
                            <div class="inline-block p-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 group-hover:border-blue-300 transition-colors duration-300">
                                <img src="${qrImageUrl}" alt="QR Code" class="w-32 h-32 mx-auto rounded-lg shadow-sm bg-white border border-gray-200">
                            </div>
                        </div>
                        
                        <!-- Table Info -->
                        <div class="flex justify-between items-center py-3 border-t border-gray-100">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-users text-blue-500 ml-2"></i>
                                <span class="text-sm font-medium">${table.seating_capacity} أشخاص</span>
                            </div>
                            <span class="text-sm text-gray-500">${table.area_name || 'غير محدد'}</span>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="grid grid-cols-2 gap-2 mt-4">
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1" onclick="showTableUrl('${qrCodeUrl}')" title="عرض الرابط">
                                <i class="fas fa-link ml-1"></i>
                                الرابط
                            </button>
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 hover:border-green-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1" onclick="downloadQR('${qrImageUrl}', 'table-${table.table_number}-qr.png')" title="تحميل QR">
                                <i class="fas fa-download ml-1"></i>
                                تحميل
                            </button>
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1" onclick="generateNewQR(${table.id})" title="إنشاء QR جديد">
                                <i class="fas fa-sync ml-1"></i>
                                تجديد
                            </button>
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-amber-700 bg-amber-50 border border-amber-200 rounded-lg hover:bg-amber-100 hover:border-amber-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-1" onclick="showManualQRModal(${table.id})" title="تعيين QR يدوياً">
                                <i class="fas fa-edit ml-1"></i>
                                يدوي
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.append(card);
        });
    }

    // Generate table URL
    function generateTableUrl(table) {
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug(); // Get from global or config
        return `${baseUrl}/restaurant/table/${table.qr_code || table.id}?hash=${tenantSlug}`;
    }

    // Get tenant slug (this should be available globally or from config)
    function getTenantSlug() {
        // Get tenant slug from authenticated user's branch tenant
        <?php if(auth()->user() && auth()->user()->branch && auth()->user()->branch->tenant): ?>
            <?php
                $tenant = auth()->user()->branch->tenant;
                $tenantSlug = $tenant->code ?? strtolower(str_replace([' ', '-', '_'], '', $tenant->name));
            ?>
            return '<?php echo e($tenantSlug); ?>';
        <?php else: ?>
            return 'default-tenant'; // Default fallback
        <?php endif; ?>
    }

    // Generate QR Code (removed - now using QR Server API)

    // Show table URL in a modal or alert
    window.showTableUrl = function(url) {
        swal({
            title: 'رابط الطاولة',
            text: url,
            icon: 'info',
            buttons: {
                copy: {
                    text: 'نسخ الرابط',
                    value: 'copy',
                    className: 'btn-primary'
                },
                close: {
                    text: 'إغلاق',
                    value: 'close',
                    className: 'btn-secondary'
                }
            }
        }).then((value) => {
            if (value === 'copy') {
                // Copy to clipboard
                navigator.clipboard.writeText(url).then(() => {
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    let textArea = document.createElement('textarea');
                    textArea.value = url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                });
            }
        });
    };

    // Download QR code
    window.downloadQR = function(qrImageUrl, filename) {
        let link = document.createElement('a');
        link.href = qrImageUrl;
        link.download = filename || 'qr-code.png';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Get status text in Arabic
    function getStatusText(status) {
        const statusMap = {
            'available': 'متاحة',
            'occupied': 'مشغولة',
            'reserved': 'محجوزة',
            'cleaning': 'تنظيف',
            'out_of_order': 'خارج الخدمة'
        };
        return statusMap[status] || status;
    }

    // Initialize DataTable for table view
    function initDataTable() {
        if (tablesTable) {
            tablesTable.destroy();
        }

        tablesTable = $('#tables-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?php echo e(route("reservation.tables.datatable")); ?>',
                data: function(d) {
                    d.area_id = $('#area-filter').val();
                    d.status = $('#status-filter').val();
                    d.capacity = $('#capacity-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'table_number', name: 'table_number' },
                { data: 'table_name', name: 'table_name' },
                { data: 'area_name', name: 'area_name' },
                { data: 'seating_capacity', name: 'seating_capacity' },
                { 
                    data: 'status', 
                    name: 'status',
                    render: function(data, type, row) {
                        let statusClass = `status-${data}`;
                        let statusText = getStatusText(data);
                        return `<span class="table-status ${statusClass}">${statusText}</span>`;
                    }
                },
                { 
                    data: 'qr_code_display', 
                    name: 'qr_code_display',
                    orderable: false,
                    searchable: false
                },
                { 
                    data: 'action', 
                    name: 'action', 
                    orderable: false, 
                    searchable: false
                }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
            }
        });
    }

    // Filters
    $('#apply-filters').click(function() {
        if (currentView === 'cards') {
            loadTables();
        } else {
            tablesTable.ajax.reload();
        }
    });

    $('#clear-filters').click(function() {
        $('#area-filter, #status-filter').val('');
        $('#capacity-filter').val('');
        if (currentView === 'cards') {
            loadTables();
        } else {
            tablesTable.ajax.reload();
        }
    });

    // Generate new QR code
    window.generateNewQR = function(tableId) {
        if (confirm('هل أنت متأكد من إنشاء QR Code جديد لهذه الطاولة؟')) {
            $.post(`<?php echo e(url('tables')); ?>/${tableId}/regenerate-qr`, {
                _token: '<?php echo e(csrf_token()); ?>'
            }, function(data) {
                if (data.success) {
                    swal('نجح!', data.message, 'success');
                    if (currentView === 'cards') {
                        loadTables();
                    } else {
                        tablesTable.ajax.reload();
                    }
                }
            }).fail(function(xhr) {
                let message = 'حدث خطأ أثناء إنشاء QR Code جديد';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                swal('خطأ!', message, 'error');
            });
        }
    };

    // Show manual QR modal
    window.showManualQRModal = function(tableId) {
        $('#manualQrModal').data('table-id', tableId);
        $('#manual-qr-code').val('');
        $('#manual-qr-preview').hide();
        $('#manualQrModal').removeClass('hidden');
    };

    // Preview manual QR code
    $('#preview-manual-qr').click(function() {
        let qrCode = $('#manual-qr-code').val().trim();
        if (!qrCode) {
            swal('تنبيه!', 'يرجى إدخال QR Code أولاً', 'warning');
            return;
        }

        let tableId = $('#manualQrModal').data('table-id');
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug();
        let qrUrl = `${baseUrl}/restaurant/table/${qrCode}?hash=${tenantSlug}`;

        // Generate QR code preview using QR Server API
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrUrl)}`;
        
        let container = document.getElementById('manual-qr-image');
        container.innerHTML = `<img src="${qrImageUrl}" alt="QR Code Preview" style="width: 150px; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; background: white;">`;
        $('#manual-qr-preview').show();
    });

    // Save manual QR code
    $('#save-manual-qr').click(function() {
        let qrCode = $('#manual-qr-code').val().trim();
        let tableId = $('#manualQrModal').data('table-id');
        
        if (!qrCode) {
            swal('تنبيه!', 'يرجى إدخال QR Code أولاً', 'warning');
            return;
        }

        $.post(`<?php echo e(url('tables')); ?>/${tableId}/set-manual-qr`, {
            _token: '<?php echo e(csrf_token()); ?>',
            qr_code: qrCode
        }, function(data) {
            if (data.success) {
                swal('نجح!', data.message, 'success');
                $('#manualQrModal').addClass('hidden');
                if (currentView === 'cards') {
                    loadTables();
                } else {
                    tablesTable.ajax.reload();
                }
            }
        }).fail(function(xhr) {
            let message = 'حدث خطأ أثناء حفظ QR Code';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            swal('خطأ!', message, 'error');
        });
    });

    // Show QR Modal with larger image
    window.showQRModal = function(qrImageUrl, qrUrl, qrCode) {
        $('#qrModalLabel').text(`QR Code - ${qrCode}`);
        $('#qr-table-info').html(`<h6>QR Code: ${qrCode}</h6>`);
        $('#qr-url-info').html(`<small class="text-muted">${qrUrl}</small>`);
        
        // Display the QR image
        let container = document.getElementById('qr-code-container');
        container.innerHTML = `<img src="${qrImageUrl}" alt="QR Code" style="width: 200px; height: 200px;" class="img-thumbnail">`;
        
        // Store for download
        $('#download-qr').data('qr-url', qrImageUrl);
        $('#download-qr').data('filename', `qr-code-${qrCode}.png`);
        
        $('#qrModal').removeClass('hidden');
    };
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Reservation\Providers/../resources/views/tables.blade.php ENDPATH**/ ?>