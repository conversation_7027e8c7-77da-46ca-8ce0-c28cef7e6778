@extends('layouts.master')

@section('title', 'Payment - Order #' . $order->order_number)

@push('styles')
<style>
    /* Modal Overlay */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    /* Modal Container */
    .modal-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    /* Modal Header */
    .modal-header {
        background: #1f2937;
        color: white;
        padding: 20px;
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-close {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
    }

    .modal-close:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Modal Body */
    .modal-body {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
    }

    /* Modal Footer */
    .modal-footer {
        padding: 20px;
        border-top: 1px solid #e5e7eb;
        background: #f9fafb;
        flex-shrink: 0;
    }

    .payment-container {
        max-width: 100%;
        margin: 0;
        padding: 0;
    }
    .payment-header {
        background: none;
        color: inherit;
        padding: 0;
        border-radius: 0;
        margin-bottom: 20px;
    }
    .order-summary {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 30px;
    }
    .payment-methods {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    /* Payment Methods Slider Styles */
    .payment-methods-slider {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

    .payment-methods-slider::-webkit-scrollbar {
        height: 6px;
    }

    .payment-methods-slider::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 3px;
    }

    .payment-methods-slider::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    .payment-methods-slider::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    .payment-method-card {
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 140px;
        text-align: center;
        flex-shrink: 0;
    }
    .payment-method-card:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }
    .payment-method-card.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
    .payment-method-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px auto;
        background-color: #f3f4f6;
    }
    .payment-method-card.selected .payment-method-icon {
        background-color: #dbeafe;
    }
    .amount-input {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .amount-input:focus {
        border-color: #3b82f6;
        outline: none;
    }
    .quick-amounts {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
        margin-bottom: 20px;
    }
    .quick-amount-btn {
        padding: 10px;
        border: 2px solid #e5e7eb;
        border-radius: 6px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    .quick-amount-btn:hover {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
    .payment-summary {
        background: #f8fafc;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .summary-line {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .summary-line.total {
        font-size: 18px;
        font-weight: bold;
        border-top: 2px solid #e5e7eb;
        padding-top: 10px;
        margin-top: 10px;
    }
    .process-payment-btn {
        width: 100%;
        background: #10b981;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 15px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .process-payment-btn:hover {
        background: #059669;
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(16, 185, 129, 0.3);
    }
    .process-payment-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .back-btn {
        width: 100%;
        background: #6b7280;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        margin-top: 12px;
    }

    .back-btn:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }
</style>
@endpush

@section('content')
<div class="modal-overlay">
    <div class="modal-container">
        <!-- Modal Header -->
        <div class="modal-header">
            <div>
                <h1 class="text-2xl font-bold mb-2">Payment Processing</h1>
                <p class="text-gray-300">Order #{{ $order->order_number }} - {{ $order->created_at->format('d/m/Y H:i') }}</p>
            </div>
            <button type="button" class="modal-close" onclick="window.location.href='{{ route('pos.create') }}'">
                <i class="mdi mdi-close"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body">
<div class="payment-container">
    <!-- Order Info -->
    <div class="payment-header">
        <div class="flex justify-between items-center">
            <div>
                @if($order->table)
                    <div class="text-gray-600">Table: {{ $order->table->name }}</div>
                @endif
                @if($order->customer)
                    <div class="text-gray-600">Customer: {{ $order->customer->name }}</div>
                @endif
            </div>
        </div>
    </div>

    <!-- Payment Methods -->
    <div class="payment-methods mb-8">
        <h2 class="text-xl font-bold mb-4">Payment Method</h2>
        
        <form id="paymentForm">
            @csrf
            <input type="hidden" name="transaction_id" value="{{ $order->transaction ? $order->transaction->id : '' }}">
            
            <!-- Payment Methods Slider -->
            <div class="mb-6">
                <div class="payment-methods-slider">
                    <div class="flex gap-3 pb-2">
                        @foreach($paymentMethods as $method)
                            <div class="payment-method-card" data-method-id="{{ $method->id }}">
                                <div class="payment-method-icon">
                                    <i class="mdi mdi-{{ $method->icon ?? 'credit-card' }} text-gray-600 text-2xl"></i>
                                </div>
                                <div class="font-medium text-sm mb-1">{{ $method->name }}</div>
                                @if($method->description)
                                    <div class="text-xs text-gray-500 mb-2">{{ $method->description }}</div>
                                @endif
                                <input type="radio" name="payment_method_id" value="{{ $method->id }}" class="w-4 h-4 text-blue-600">
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Payment Amount -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Payment Amount</label>
                <input type="number" 
                       id="paymentAmount" 
                       name="amount" 
                       class="amount-input w-full" 
                       placeholder="0.00" 
                       step="0.01" 
                       min="0.01"
                       value="{{ $order->transaction ? $order->transaction->due_amount : $order->total_amount }}"
                       required>
                
                <!-- Quick Amount Buttons -->
                <div class="quick-amounts">
                    <button type="button" class="quick-amount-btn" data-amount="{{ $order->transaction ? $order->transaction->due_amount : $order->total_amount }}">Exact</button>
                    <button type="button" class="quick-amount-btn" data-amount="10">$10</button>
                    <button type="button" class="quick-amount-btn" data-amount="20">$20</button>
                    <button type="button" class="quick-amount-btn" data-amount="50">$50</button>
                    <button type="button" class="quick-amount-btn" data-amount="100">$100</button>
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="payment-summary">
                <div class="summary-line">
                    <span>Payment Amount:</span>
                    <span id="displayPaymentAmount">${{ number_format($order->transaction ? $order->transaction->due_amount : $order->total_amount, 2) }}</span>
                </div>
                <div class="summary-line">
                    <span>Amount Due:</span>
                    <span>${{ number_format($order->transaction ? $order->transaction->due_amount : $order->total_amount, 2) }}</span>
                </div>
                <div class="summary-line total" id="changeAmount" style="display: none;">
                    <span>Change:</span>
                    <span id="displayChange">$0.00</span>
                </div>
            </div>

        </form>
    </div>

    <!-- Order Summary -->
    <div class="order-summary">
        <h2 class="text-xl font-bold mb-4">Order Summary</h2>
        
        <div class="space-y-3 mb-6">
            @foreach($order->orderItems as $item)
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="font-medium">{{ $item->quantity }}x {{ $item->menuItem->name }}</div>
                        @if($item->variant_name)
                            <div class="text-sm text-gray-600">Variant: {{ $item->variant_name }}</div>
                        @endif
                        @if($item->notes)
                            <div class="text-sm text-gray-600">Notes: {{ $item->notes }}</div>
                        @endif
                        @if($item->addons && count($item->addons) > 0)
                            @foreach($item->addons as $addon)
                                <div class="text-sm text-gray-600">+ {{ $addon['addon_name'] }} ({{ $addon['quantity'] }}x)</div>
                            @endforeach
                        @endif
                    </div>
                    <div class="font-bold">${{ number_format($item->total_price, 2) }}</div>
                </div>
            @endforeach
        </div>

        <div class="border-t pt-4">
            <div class="summary-line">
                <span>Subtotal:</span>
                <span>${{ number_format($order->subtotal, 2) }}</span>
            </div>
            @if($order->discount_amount > 0)
                <div class="summary-line">
                    <span>Discount:</span>
                    <span>-${{ number_format($order->discount_amount, 2) }}</span>
                </div>
            @endif
            @if($order->tax_amount > 0)
                <div class="summary-line">
                    <span>Tax:</span>
                    <span>${{ number_format($order->tax_amount, 2) }}</span>
                </div>
            @endif
            <div class="summary-line total">
                    <span>Total Amount:</span>
                    <span>${{ number_format($order->total_amount, 2) }}</span>
                </div>
                @if($order->transaction && $order->transaction->paid_amount > 0)
                    <div class="summary-line">
                        <span>Paid Amount:</span>
                        <span>${{ number_format($order->transaction->paid_amount, 2) }}</span>
                    </div>
                    <div class="summary-line total text-red-600">
                        <span>Amount Due:</span>
                        <span>${{ number_format($order->transaction ? $order->transaction->due_amount : $order->total_amount, 2) }}</span>
                    </div>
                @endif
        </div>
    </div>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer">
            <!-- Process Payment Button -->
            <button type="submit" form="paymentForm" class="process-payment-btn" id="processPaymentBtn" disabled>
                <i class="mdi mdi-credit-card mr-2"></i>
                Process Payment
            </button>

            <!-- Back to POS Button -->
            <a href="{{ route('pos.create') }}" class="back-btn">
                <i class="mdi mdi-arrow-left mr-2"></i>
                Back to POS
            </a>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
    <div class="bg-white p-6 rounded-lg">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span>Processing payment...</span>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    const dueAmount = {{ $order->transaction ? $order->transaction->due_amount : $order->total_amount }};

    // Payment method selection
    $('.payment-method-card').on('click', function() {
        $('.payment-method-card').removeClass('selected');
        $(this).addClass('selected');
        $(this).find('input[type="radio"]').prop('checked', true);
        updateProcessButton();
    });

    // Quick amount buttons
    $('.quick-amount-btn').on('click', function() {
        const amount = $(this).data('amount');
        $('#paymentAmount').val(amount);
        updatePaymentSummary();
    });

    // Payment amount input
    $('#paymentAmount').on('input', function() {
        updatePaymentSummary();
    });

    // Update payment summary
    function updatePaymentSummary() {
        const paymentAmount = parseFloat($('#paymentAmount').val()) || 0;
        $('#displayPaymentAmount').text('$' + paymentAmount.toFixed(2));

        const change = paymentAmount - dueAmount;
        if (change > 0) {
            $('#changeAmount').show();
            $('#displayChange').text('$' + change.toFixed(2));
        } else {
            $('#changeAmount').hide();
        }

        updateProcessButton();
    }

    // Update process button state
    function updateProcessButton() {
        const hasPaymentMethod = $('input[name="payment_method_id"]:checked').length > 0;
        const hasAmount = parseFloat($('#paymentAmount').val()) > 0;

        $('#processPaymentBtn').prop('disabled', !(hasPaymentMethod && hasAmount));
    }

    // Process payment form
    $('#paymentForm').on('submit', function(e) {
        e.preventDefault();

        const paymentAmount = parseFloat($('#paymentAmount').val());
        if (paymentAmount <= 0) {
            Swal.fire('Error', 'Please enter a valid payment amount', 'error');
            return;
        }

        const paymentMethodId = $('input[name="payment_method_id"]:checked').val();
        if (!paymentMethodId) {
            Swal.fire('Error', 'Please select a payment method', 'error');
            return;
        }

        // Show loading
        $('#loadingOverlay').show();

        // Process payment
        $.ajax({
            url: '{{ route("api.payments.store") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            data: JSON.stringify({
                transaction_id: {{ $order->transaction ? $order->transaction->id : 'null' }},
                payment_method_id: paymentMethodId,
                amount: paymentAmount,
                payment_date: new Date().toISOString().split('T')[0],
                notes: 'POS Payment'
            }),
            success: function(response) {
                $('#loadingOverlay').hide();

                if (response.success) {
                    const change = paymentAmount - dueAmount;
                    let message = 'Payment processed successfully!';
                    if (change > 0) {
                        message += \n\nChange to give: $${change.toFixed(2)};
                    }

                    Swal.fire({
                        icon: 'success',
                        title: 'Payment Successful!',
                        text: message,
                        confirmButtonText: 'Print Receipt'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Print receipt
                            window.open({{ route('pos.orders.bill', $order) }}, '_blank');
                        }
                        // Redirect back to POS
                        window.location.href = '{{ route("pos.create") }}';
                    });
                } else {
                    Swal.fire('Error', response.message || 'Payment failed', 'error');
                }
            },
            error: function(xhr) {
                $('#loadingOverlay').hide();

                let errorMessage = 'Payment processing failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                Swal.fire('Error', errorMessage, 'error');
            }
        });
    });

    // Initialize
    updatePaymentSummary();
});
</script>
@endpush