<?php

namespace Modules\Reservation\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Reservation;
use App\Models\Area;
use App\Models\WaiterRequest;
use App\Models\Table;
use App\Models\Customer;
use App\Models\ReservationStatus;
use App\Models\User;
use Carbon\Carbon;
use Modules\Reservation\Services\ReservationService;

class ReservationController extends Controller
{
    protected $reservationService;

    public function __construct(ReservationService $reservationService)
    {
        $this->reservationService = $reservationService;
    }

    // Index Methods for Views
    public function dashboard()
    {
        return view('Reservation::dashboard');
    }

    public function reservationsIndex()
    {
        return view('Reservation::reservations');
    }

    public function areasIndex()
    {
        return view('Reservation::areas');
    }

    public function tablesIndex()
    {
        return view('Reservation::tables');
    }

    public function waiterRequestsIndex()
    {
        return view('Reservation::waiter-requests');    
    }

    // DataTable Methods
    public function reservationsDataTable(Request $request)
    {
        if ($request->ajax()) {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'error' => 'User is not assigned to any branch'
                ], 400);
            }

            $query = Reservation::with(['customer', 'table', 'area', 'branch', 'user', 'reservationStatus'])
                ->where('branch_id', $user->branch_id);

            // Apply filters
            if ($request->has('status') && $request->status != '') {
                $query->whereHas('reservationStatus', function($q) use ($request) {
                    $q->where('name', $request->status);
                });
            }

            if ($request->has('date_from') && $request->date_from != '') {
                $query->whereDate('reservation_datetime', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to != '') {
                $query->whereDate('reservation_datetime', '<=', $request->date_to);
            }

            if ($request->has('customer_search') && $request->customer_search != '') {
                $query->where(function($q) use ($request) {
                    $q->whereHas('customer', function($customerQuery) use ($request) {
                        $customerQuery->where('first_name', 'like', '%' . $request->customer_search . '%')
                                     ->orWhere('last_name', 'like', '%' . $request->customer_search . '%')
                                     ->orWhere('phone', 'like', '%' . $request->customer_search . '%');
                    })
                    ->orWhere('customer_name', 'like', '%' . $request->customer_search . '%')
                    ->orWhere('customer_phone', 'like', '%' . $request->customer_search . '%');
                });
            }

            if ($request->has('area_id') && $request->area_id != '') {
                $query->where('area_id', $request->area_id);
            }

            if ($request->has('table_id') && $request->table_id != '') {
                $query->where('table_id', $request->table_id);
            }

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($reservation) {
                    $actions = '<div class="btn-group" role="group">';

                    $actions .= '<button type="button" class="btn btn-sm btn-info" onclick="showReservation(' . $reservation->id . ')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>';

                    $statusName = $reservation->reservationStatus ? $reservation->reservationStatus->name : null;

                    if ($reservation->can_cancel) {
                        $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="editReservation(' . $reservation->id . ')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>';
                    }

                    if ($statusName === 'pending') {
                        $actions .= '<button type="button" class="btn btn-sm btn-success" onclick="confirmReservation(' . $reservation->id . ')" title="تأكيد">
                            <i class="fas fa-check"></i>
                        </button>';
                    }

                    if ($reservation->can_cancel) {
                        $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteReservation(' . $reservation->id . ')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>';
                    }

                    $actions .= '</div>';
                    return $actions;
                })
                ->addColumn('customer_name', function ($reservation) {
                    if ($reservation->customer) {
                        return $reservation->customer->first_name . ' ' . $reservation->customer->last_name;
                    }
                    return $reservation->customer_name ?? '-';
                })
                ->addColumn('customer_phone', function ($reservation) {
                    if ($reservation->customer) {
                        return $reservation->customer->phone;
                    }
                    return $reservation->customer_phone ?? '-';
                })
                ->addColumn('table_name', function ($reservation) {
                    return $reservation->table ? $reservation->table->table_name : '-';
                })
                ->addColumn('area_name', function ($reservation) {
                    return $reservation->area ? $reservation->area->name : '-';
                })
                ->editColumn('reservation_datetime', function ($reservation) {
                    return Carbon::parse($reservation->reservation_datetime)->format('Y-m-d H:i');
                })
                ->editColumn('status', function ($reservation) {
                    $statusName = $reservation->reservationStatus ? $reservation->reservationStatus->name : 'unknown';

                    $statusColors = [
                        'pending' => 'warning',
                        'confirmed' => 'success',
                        'seated' => 'info',
                        'completed' => 'primary',
                        'cancelled' => 'danger',
                        'no_show' => 'secondary'
                    ];

                    $statusLabels = [
                        'pending' => 'في الانتظار',
                        'confirmed' => 'مؤكد',
                        'seated' => 'جالس',
                        'completed' => 'مكتمل',
                        'cancelled' => 'ملغي',
                        'no_show' => 'لم يحضر'
                    ];

                    $color = $statusColors[$statusName] ?? 'secondary';
                    $label = $statusLabels[$statusName] ?? ucfirst($statusName);
                    return '<span class="badge badge-' . $color . '">' . $label . '</span>';
                })
                ->editColumn('party_size', function ($reservation) {
                    return $reservation->party_size . ' أشخاص';
                })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }

        return view('Reservation::reservations');
    }
    // public function reservationsDataTable(Request $request)
    // {
    //     if ($request->ajax()) {
    //         $user = auth()->user();
            
    //         if (!$user->branch_id) {
    //             return response()->json([
    //                 'error' => 'User is not assigned to any branch'
    //             ], 400);
    //         }

    //         $query = Reservation::with(['customer', 'table', 'area', 'branch', 'user', 'reservationStatus'])
    //             ->where('branch_id', $user->branch_id);

    //         // Apply filters
    //         if ($request->has('status') && $request->status != '') {
    //             $query->where('status', $request->status);
    //         }

    //         if ($request->has('date_from') && $request->date_from != '') {
    //             $query->whereDate('reservation_datetime', '>=', $request->date_from);
    //         }

    //         if ($request->has('date_to') && $request->date_to != '') {
    //             $query->whereDate('reservation_datetime', '<=', $request->date_to);
    //         }

    //         if ($request->has('customer_search') && $request->customer_search != '') {
    //             $query->whereHas('customer', function($q) use ($request) {
    //                 $q->where('name', 'like', '%' . $request->customer_search . '%')
    //                   ->orWhere('phone', 'like', '%' . $request->customer_search . '%');
    //             });
    //         }

    //         if ($request->has('area_id') && $request->area_id != '') {
    //             $query->where('area_id', $request->area_id);
    //         }

    //         if ($request->has('table_id') && $request->table_id != '') {
    //             $query->where('table_id', $request->table_id);
    //         }

    //         return DataTables::of($query)
    //             ->addIndexColumn()
    //             ->addColumn('action', function ($reservation) {
    //                 $actions = '<div class="btn-group" role="group">';
                    
    //                 $actions .= '<button type="button" class="btn btn-sm btn-info" onclick="showReservation(' . $reservation->id . ')" title="عرض">
    //                     <i class="fas fa-eye"></i>
    //                 </button>';
                    
    //                 if ($reservation->can_cancel) {
    //                     $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="editReservation(' . $reservation->id . ')" title="تعديل">
    //                         <i class="fas fa-edit"></i>
    //                     </button>';
    //                 }
                    
    //                 if ($reservation->status === 'pending') {
    //                     $actions .= '<button type="button" class="btn btn-sm btn-success" onclick="confirmReservation(' . $reservation->id . ')" title="تأكيد">
    //                         <i class="fas fa-check"></i>
    //                     </button>';
    //                 }
                    
    //                 if ($reservation->can_cancel) {
    //                     $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteReservation(' . $reservation->id . ')" title="حذف">
    //                         <i class="fas fa-trash"></i>
    //                     </button>';
    //                 }
                    
    //                 $actions .= '</div>';
    //                 return $actions;
    //             })
    //             ->editColumn('customer_name', function ($reservation) {
    //                 return $reservation->customer ? $reservation->customer->name : '-';
    //             })
    //             ->editColumn('customer_phone', function ($reservation) {
    //                 return $reservation->customer ? $reservation->customer->phone : '-';
    //             })
    //             ->editColumn('table_name', function ($reservation) {
    //                 return $reservation->table ? $reservation->table->name : '-';
    //             })
    //             ->editColumn('area_name', function ($reservation) {
    //                 return $reservation->area ? $reservation->area->name : '-';
    //             })
    //             ->editColumn('reservation_datetime', function ($reservation) {
    //                 return Carbon::parse($reservation->reservation_datetime)->format('Y-m-d H:i');
    //             })
    //             ->editColumn('status', function ($reservation) {
    //                 $statusColors = [
    //                     'pending' => 'warning',
    //                     'confirmed' => 'success',
    //                     'seated' => 'info',
    //                     'completed' => 'primary',
    //                     'cancelled' => 'danger',
    //                     'no_show' => 'secondary'
    //                 ];
                    
    //                 $color = $statusColors[$reservation->status] ?? 'secondary';
    //                 return '<span class="badge badge-' . $color . '">' . ucfirst($reservation->status) . '</span>';
    //             })
    //             ->editColumn('party_size', function ($reservation) {
    //                 return $reservation->party_size . ' أشخاص';
    //             })
    //             ->rawColumns(['action', 'status'])
    //             ->make(true);
    //     }

    //     return view('Reservation::reservations');
    // }

    public function areasDataTable(Request $request)
    {
        if ($request->ajax()) {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'error' => 'User is not assigned to any branch'
                ], 400);
            }

            $query = Area::with(['branch'])
                ->where('branch_id', $user->branch_id);

            return DataTables::of($query)
                ->addColumn('action', function ($area) {
                    $actions = '<div class="btn-group" role="group">';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-info" onclick="showArea(' . $area->id . ')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-primary" onclick="showAreaTables(' . $area->id . ')" title="عرض الطاولات">
                        <i class="fas fa-table"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="editArea(' . $area->id . ')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteArea(' . $area->id . ')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>';
                    
                    $actions .= '</div>';
                    return $actions;
                })
                ->editColumn('tables_count', function ($area) {
                    return $area->tables_count;
                })
                ->editColumn('total_capacity', function ($area) {
                    return $area->total_capacity . ' شخص';
                })
                ->editColumn('available_tables', function ($area) {
                    return $area->available_tables_count . ' / ' . $area->tables_count;
                })
                ->editColumn('occupied_tables', function ($area) {
                    return $area->occupied_tables_count . ' / ' . $area->tables_count;
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('Reservation::areas');
    }

    public function waiterRequestsDataTable(Request $request)
    {
        if ($request->ajax()) {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'error' => 'User is not assigned to any branch'
                ], 400);
            }

            $query = WaiterRequest::with(['table.area'])->where('branch_id', $user->branch_id);
             

            // Apply filters
            if ($request->has('status') && $request->status != '') {
                $query->where('status', $request->status);
            }

            // if ($request->has('waiter_id') && $request->waiter_id != '') {
            //     $query->where('waiter_id', $request->waiter_id);
            // }

            if ($request->has('table_id') && $request->table_id != '') {
                $query->where('table_id', $request->table_id);
            }

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($request) {
                    $actions = '<div class="btn-group" role="group">';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-info" onclick="showWaiterRequest(' . $request->id . ')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>';
                    
                    if ($request->status === 'pending') {
                        $actions .= '<button type="button" class="btn btn-sm btn-success" onclick="completeWaiterRequest(' . $request->id . ')" title="إكمال">
                            <i class="fas fa-check"></i>
                        </button>';
                        
                        $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="editWaiterRequest(' . $request->id . ')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>';
                        
                        $actions .= '<button type="button" class="btn btn-sm btn-secondary" onclick="cancelWaiterRequest(' . $request->id . ')" title="إلغاء">
                            <i class="fas fa-times"></i>
                        </button>';
                    }
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteWaiterRequest(' . $request->id . ')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>';
                    
                    $actions .= '</div>';
                    return $actions;
                })
                ->editColumn('table_name', function ($request) {
                    if ($request->table) {
                        return $request->table->table_name ?: 'طاولة ' . $request->table->table_number;
                    }
                    return '-';
                })
                ->editColumn('area_name', function ($request) {
                    return $request->table && $request->table->area ? $request->table->area->name : '-';
                })
                ->editColumn('waiter_name', function ($request) {
                    return $request->waiter ? $request->waiter->name : 'غير محدد';
                })
                ->editColumn('request_type', function ($request) {
                    return $request->request_type ?: 'خدمة عامة';
                })
                ->editColumn('notes', function ($request) {
                    return $request->notes ? Str::limit($request->notes, 50) : '-';
                })
                ->editColumn('status', function ($request) {
                    $statusColors = [
                        'pending' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger'
                    ];
                    
                    $statusLabels = [
                        'pending' => 'في الانتظار',
                        'completed' => 'مكتمل',
                        'cancelled' => 'ملغي'
                    ];
                    
                    $color = $statusColors[$request->status] ?? 'secondary';
                    $label = $statusLabels[$request->status] ?? $request->status;
                    return '<span class="badge badge-' . $color . '">' . $label . '</span>';
                })
                ->editColumn('created_at', function ($request) {
                    return $request->created_at->format('Y-m-d H:i');
                })
                ->editColumn('response_time', function ($request) {
                    return $request->response_time ? $request->response_time->format('Y-m-d H:i') : '-';
                })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }

        return view('Reservation.waiter-requests');
    }

    public function waiterRequestsCards(Request $request)
    {
        if ($request->ajax()) {
            $branchId = auth()->user()->branch_id ?? 1;
            
            $query = WaiterRequest::with(['table.area', 'waiter'])
                ->where('branch_id', $branchId);

            // Apply filters
            if ($request->has('status') && $request->status != '') {
                $query->where('status', $request->status);
            }

            if ($request->has('waiter_id') && $request->waiter_id != '') {
                $query->where('waiter_id', $request->waiter_id);
            }

            if ($request->has('table_id') && $request->table_id != '') {
                $query->where('table_id', $request->table_id);
            }

            if ($request->has('request_type') && $request->request_type != '') {
                $query->where('request_type', $request->request_type);
            }

            // Order by status priority and creation time
            $query->orderByRaw("CASE 
                WHEN status = 'pending' THEN 1 
                WHEN status = 'completed' THEN 2 
                WHEN status = 'cancelled' THEN 3 
                ELSE 4 END")
                ->orderBy('created_at', 'desc');

            $requests = $query->paginate(12);

            // Get statistics
            $statistics = [
                'pending' => WaiterRequest::where('branch_id', $branchId)->where('status', 'pending')->count(),
                'completed' => WaiterRequest::where('branch_id', $branchId)->where('status', 'completed')->count(),
                'cancelled' => WaiterRequest::where('branch_id', $branchId)->where('status', 'cancelled')->count(),
                'total' => WaiterRequest::where('branch_id', $branchId)->count(),
            ];

            return response()->json([
                'data' => $requests->items(),
                'statistics' => $statistics,
                'pagination' => [
                    'current_page' => $requests->currentPage(),
                    'last_page' => $requests->lastPage(),
                    'per_page' => $requests->perPage(),
                    'total' => $requests->total(),
                    'from' => $requests->firstItem(),
                    'to' => $requests->lastItem(),
                ]
            ]);
        }

        return view('Reservation.waiter-requests-cards');
    }

    public function tablesDataTable(Request $request)
    {
        if ($request->ajax()) {
            $query = Table::with(['area'])
                ->whereHas('area', function($q) {
                    $q->where('branch_id', auth()->user()->branch_id ?? 1);
                });

            // Apply filters
            if ($request->has('area_id') && $request->area_id != '') {
                $query->where('area_id', $request->area_id);
            }

            if ($request->has('status') && $request->status != '') {
                $query->where('status', $request->status);
            }

            if ($request->has('capacity') && $request->capacity != '') {
                $query->where('seating_capacity', '>=', $request->capacity);
            }

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($table) {
                    $actions = '<div class="btn-group" role="group">';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-info show-table" data-id="' . $table->id . '" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-warning edit-table" data-id="' . $table->id . '" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-success generate-qr" data-id="' . $table->id . '" title="QR Code">
                        <i class="fas fa-qrcode"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-primary regenerate-qr" data-id="' . $table->id . '" title="إنشاء QR جديد">
                        <i class="fas fa-sync"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-secondary set-manual-qr" data-id="' . $table->id . '" title="تعيين QR يدوياً">
                        <i class="fas fa-keyboard"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-danger delete-table" data-id="' . $table->id . '" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>';
                    
                    $actions .= '</div>';
                    return $actions;
                })
                ->editColumn('area_name', function ($table) {
                    return $table->area ? $table->area->name : '-';
                })
                ->editColumn('table_name', function ($table) {
                    return $table->table_name ?: $table->table_number;
                })
                ->addColumn('qr_code_display', function ($table) {
                    if ($table->qr_code) {
                        $baseUrl = request()->getSchemeAndHttpHost();
                        $tenantSlug = $this->getTenantSlug();
                        $qrUrl = "{$baseUrl}/restaurant/table/{$table->qr_code}?hash={$tenantSlug}";
                        
                        // Generate QR code image using a QR code service
                        $qrImageUrl = "https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=" . urlencode($qrUrl);
                        
                        return '<div class="text-center">
                                    <img src="' . $qrImageUrl . '" alt="QR Code" style="width: 50px; height: 50px;" class="img-thumbnail">
                                    <br><small class="text-muted">' . $table->qr_code . '</small>
                                    <br><button type="button" class="btn btn-xs btn-outline-primary mt-1" onclick="showQRModal(\'' . $qrImageUrl . '\', \'' . $qrUrl . '\', \'' . $table->qr_code . '\')" title="عرض QR كبير">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>';
                    }
                    return '<span class="text-muted">لا يوجد QR</span>';
                })
                ->addColumn('qr_url', function ($table) {
                    $baseUrl = request()->getSchemeAndHttpHost();
                    $tenantSlug = $this->getTenantSlug();
                    return "{$baseUrl}/restaurant/table/{$table->qr_code}?hash={$tenantSlug}";
                })
                ->editColumn('status', function ($table) {
                    $statusColors = [
                        'available' => 'success',
                        'occupied' => 'danger',
                        'reserved' => 'warning',
                        'cleaning' => 'info',
                        'out_of_order' => 'dark'
                    ];
                    
                    $statusLabels = [
                        'available' => 'متاحة',
                        'occupied' => 'مشغولة',
                        'reserved' => 'محجوزة',
                        'cleaning' => 'تنظيف',
                        'out_of_order' => 'خارج الخدمة'
                    ];
                    
                    $color = $statusColors[$table->status] ?? 'secondary';
                    $label = $statusLabels[$table->status] ?? $table->status;
                    return '<span class="badge badge-' . $color . '">' . $label . '</span>';
                })
                ->editColumn('is_active', function ($table) {
                    return $table->is_active ? 
                        '<span class="badge badge-success">نشط</span>' : 
                        '<span class="badge badge-danger">غير نشط</span>';
                })
                ->editColumn('seating_capacity', function ($table) {
                    return $table->seating_capacity . ' شخص';
                })
                ->rawColumns(['action', 'status', 'is_active', 'qr_code_display'])
                ->make(true);
        }

        return view('Reservation.tables');
    }

    // CRUD Methods for Reservations
    public function reservationsStore(Request $request)
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'required_without:customer_id|string|max:255',
            'customer_phone' => 'required_without:customer_id|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'table_id' => 'required|exists:tables,id',
            'area_id' => 'required|exists:areas,id',
            'reservation_datetime' => 'required|date|after:now',
            'party_size' => 'required|integer|min:1|max:20',
            'special_requests' => 'nullable|string|max:500',
            'duration_minutes' => 'nullable|integer|min:30|max:480'
        ]);

        // Get pending status
        $pendingStatus = ReservationStatus::where('name', 'pending')->first();
        if (!$pendingStatus) {
            return response()->json(['success' => false, 'message' => 'حالة الانتظار غير موجودة'], 400);
        }

        // Handle customer
        $customerId = $request->customer_id;
        if (!$customerId && $request->customer_name) {
            // Create customer from form data
            $nameParts = explode(' ', $request->customer_name, 2);
            $customer = Customer::firstOrCreate(
                [
                    'phone' => $request->customer_phone,
                    'branch_id' => $user->branch_id
                ],
                [
                    'first_name' => $nameParts[0],
                    'last_name' => $nameParts[1] ?? '',
                    'email' => $request->customer_email,
                    'branch_id' => $user->branch_id
                ]
            );
            $customerId = $customer->id;
        }

        $reservation = Reservation::create([
            'customer_id' => $customerId,
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'customer_email' => $request->customer_email,
            'table_id' => $request->table_id,
            'area_id' => $request->area_id,
            'reservation_datetime' => $request->reservation_datetime,
            'party_size' => $request->party_size,
            'special_requests' => $request->special_requests,
            'duration_minutes' => $request->duration_minutes ?? 120,
            'reservation_status_id' => $pendingStatus->id,
            'branch_id' => $user->branch_id,
            'created_by' => auth()->id()
        ]);

        return response()->json([
            'success' => true, 
            'message' => 'تم إضافة الحجز بنجاح',
            'data' => $reservation->load(['customer', 'table', 'area'])
        ]);
    }

    public function reservationsShow($id)
    {
        $reservation = Reservation::with(['customer', 'table', 'area', 'branch', 'user', 'reservationStatus'])->findOrFail($id);
        return response()->json($reservation);
    }

    public function reservationsEdit($id)
    {
        $reservation = Reservation::with(['customer', 'table', 'area'])->findOrFail($id);
        return response()->json($reservation);
    }

    public function reservationsUpdate(Request $request, $id)
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'required_without:customer_id|string|max:255',
            'customer_phone' => 'required_without:customer_id|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'table_id' => 'required|exists:tables,id',
            'area_id' => 'required|exists:areas,id',
            'reservation_datetime' => 'required|date',
            'party_size' => 'required|integer|min:1|max:20',
            'special_requests' => 'nullable|string|max:500',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'status' => 'nullable|string'
        ]);

        $reservation = Reservation::findOrFail($id);

        // Handle customer
        $customerId = $request->customer_id;
        if (!$customerId && $request->customer_name) {
            // Create customer from form data
            $nameParts = explode(' ', $request->customer_name, 2);
            $customer = Customer::firstOrCreate(
                [
                    'phone' => $request->customer_phone,
                    'branch_id' => $user->branch_id
                ],
                [
                    'first_name' => $nameParts[0],
                    'last_name' => $nameParts[1] ?? '',
                    'email' => $request->customer_email,
                    'branch_id' => $user->branch_id
                ]
            );
            $customerId = $customer->id;
        }

        $updateData = [
            'customer_id' => $customerId,
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'customer_email' => $request->customer_email,
            'table_id' => $request->table_id,
            'area_id' => $request->area_id,
            'reservation_datetime' => $request->reservation_datetime,
            'party_size' => $request->party_size,
            'special_requests' => $request->special_requests,
            'duration_minutes' => $request->duration_minutes ?? 120
        ];

        // Handle status update if provided
        if ($request->has('status') && $request->status) {
            $status = ReservationStatus::where('name', $request->status)->first();
            if ($status) {
                $updateData['reservation_status_id'] = $status->id;
            }
        }

        $reservation->update($updateData);

        return response()->json([
            'success' => true, 
            'message' => 'تم تحديث الحجز بنجاح',
            'data' => $reservation->load(['customer', 'table', 'area'])
        ]);
    }

    public function reservationsDestroy($id)
    {
        $reservation = Reservation::findOrFail($id);
        $reservation->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف الحجز بنجاح']);
    }

    public function reservationsConfirm($id)
    {
        $reservation = Reservation::findOrFail($id);

        $confirmedStatus = ReservationStatus::where('name', 'confirmed')->first();
        if (!$confirmedStatus) {
            return response()->json(['success' => false, 'message' => 'حالة التأكيد غير موجودة'], 400);
        }

        $reservation->update(['reservation_status_id' => $confirmedStatus->id]);
        return response()->json(['success' => true, 'message' => 'تم تأكيد الحجز بنجاح']);
    }

    // CRUD Methods for Areas
    public function areasStore(Request $request)
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500'
        ]);

        $area = Area::create([
            'name' => $request->name,
            'description' => $request->description,
            'branch_id' => $user->branch_id
        ]);

        return response()->json(['success' => true, 'message' => 'تم إضافة المنطقة بنجاح']);
    }

    public function areasShow($id)
    {
        $area = Area::with(['branch', 'tables'])->findOrFail($id);
        return response()->json($area);
    }

    public function areasEdit($id)
    {
        $area = Area::findOrFail($id);
        return response()->json($area);
    }

    public function areasUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500'
        ]);

        $area = Area::findOrFail($id);
        $area->update([
            'name' => $request->name,
            'description' => $request->description
        ]);

        return response()->json(['success' => true, 'message' => 'تم تحديث المنطقة بنجاح']);
    }

    public function areasDestroy($id)
    {
        $area = Area::findOrFail($id);
        $area->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف المنطقة بنجاح']);
    }

    public function getAreaTables($id)
    {
        $area = Area::with(['tables' => function($query) {
            $query->with(['currentReservation', 'activeOrders']);
        }])->findOrFail($id);

        $tables = $area->tables->map(function($table) {
            return [
                'id' => $table->id,
                'name' => $table->name,
                'capacity' => $table->capacity,
                'status' => $table->status,
                'is_occupied' => $table->is_occupied,
                'current_reservation' => $table->currentReservation,
                'active_orders_count' => $table->activeOrders->count()
            ];
        });

        return response()->json([
            'area' => $area,
            'tables' => $tables
        ]);
    }

    public function getAvailableTablesByArea($areaId)
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $tables = Table::where('area_id', $areaId)
            ->where('status', 'available')
            ->whereHas('area', function($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })
            ->select('id', 'table_number', 'table_name', 'seating_capacity', 'status')
            ->orderBy('table_number')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $tables
        ]);
    }

    // CRUD Methods for Tables
    public function tablesStore(Request $request)
    {
        $request->validate([
            'table_number' => 'required|string|max:20',
            'table_name' => 'nullable|string|max:255',
            'area_id' => 'required|exists:areas,id',
            'seating_capacity' => 'required|integer|min:1|max:50',
            'section' => 'nullable|string|max:50|in:Indoor,Outdoor,VIP,Terrace,Garden',
            'status' => 'required|in:available,occupied,reserved,cleaning,out_of_order',
            'position_coordinates' => 'nullable|json',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
            'generate_qr' => 'nullable|in:0,1,true,false',
            'custom_qr_code' => 'nullable|string|max:255|unique:tables,qr_code'
        ]);

        $user = auth()->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated'
            ], 401);
        }
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        // Check for duplicate table number within the same area
        $existingTable = Table::where('table_number', $request->table_number)
            ->where('area_id', $request->area_id)
            ->where('branch_id', $user->branch_id)
            ->first();

        if ($existingTable) {
            return response()->json([
                'success' => false,
                'message' => 'رقم الطاولة موجود بالفعل في هذه المنطقة'
            ], 422);
        }

        $tableData = [
            'branch_id' => $user->branch_id,
            'table_number' => $request->table_number,
            'table_name' => $request->table_name,
            'area_id' => $request->area_id,
            'seating_capacity' => $request->seating_capacity,
            'section' => $request->section,
            'status' => $request->status,
            'position_coordinates' => $request->position_coordinates,
            'notes' => $request->notes,
            'is_active' => $request->is_active ?? true
        ];

        $table = Table::create($tableData);

        $qrData = null;

        // Handle QR code generation
        if ($request->generate_qr) {
            if ($request->custom_qr_code) {
                // Use custom QR code
                $success = $table->setQrCode($request->custom_qr_code);
                if (!$success) {
                    return response()->json([
                        'success' => false,
                        'message' => 'QR Code المخصص موجود بالفعل لطاولة أخرى'
                    ], 422);
                }
            } else {
                // Generate automatic QR code
                $table->generateQrCode();
                $table->save();
            }

            // Generate QR code URL
            $baseUrl = request()->getSchemeAndHttpHost();
            $tenantSlug = $this->getTenantSlug();
            $qrUrl = "{$baseUrl}/restaurant/table/{$table->qr_code}?hash={$tenantSlug}";

            $qrData = [
                'qr_code' => $table->qr_code,
                'url' => $qrUrl,
                'table_number' => $table->table_number
            ];
        }

        return response()->json([
            'success' => true, 
            'message' => 'تم إضافة الطاولة بنجاح', 
            'data' => $table->load('area'),
            'qr_data' => $qrData
        ]);
    }

    public function tablesShow($id)
    {
        $table = Table::with(['area', 'currentReservation', 'activeOrders'])->findOrFail($id);
        return response()->json(['success' => true, 'data' => $table]);
    }

    public function tablesEdit($id)
    {
        $table = Table::with(['area'])->findOrFail($id);
        return response()->json(['success' => true, 'data' => $table]);
    }

    public function tablesUpdate(Request $request, $id)
    {
        $request->validate([
            'table_number' => 'required|string|max:50',
            'area_id' => 'required|exists:areas,id',
            'seating_capacity' => 'required|integer|min:1|max:20',
            'status' => 'required|in:available,occupied,reserved,cleaning,out_of_order',
            'is_active' => 'boolean'
        ]);

        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $table = Table::findOrFail($id);
        $table->update([
            'branch_id' => $user->branch_id,
            'table_number' => $request->table_number,
            'area_id' => $request->area_id,
            'seating_capacity' => $request->seating_capacity,
            'status' => $request->status,
            'is_active' => $request->is_active ?? true
        ]);

        return response()->json(['success' => true, 'message' => 'تم تحديث الطاولة بنجاح', 'data' => $table]);
    }

    public function tablesDestroy($id)
    {
        $table = Table::findOrFail($id);
        $table->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف الطاولة بنجاح']);
    }

    public function generateTableQR($id)
    {
        $table = Table::with('area')->findOrFail($id);
        
        // Generate QR code URL for the table using the qr_code field
        $baseUrl = request()->getSchemeAndHttpHost();
        $tenantSlug = $this->getTenantSlug(); // You'll need to implement this method
        $qrUrl = "{$baseUrl}/restaurant/table/{$table->qr_code}?hash={$tenantSlug}";
        
        return response()->json([
            'success' => true, 
            'qr_url' => $qrUrl,
            'table' => $table,
            'message' => 'تم إنشاء QR Code بنجاح'
        ]);
    }

    public function regenerateTableQR($id)
    {
        $table = Table::with('area')->findOrFail($id);
        
        // Generate new QR code hash
        $table->generateQrCode();
        $table->save();
        
        // Generate QR code URL for the table
        $baseUrl = request()->getSchemeAndHttpHost();
        $tenantSlug = $this->getTenantSlug();
        $qrUrl = "{$baseUrl}/restaurant/table/{$table->qr_code}?hash={$tenantSlug}";
        
        return response()->json([
            'success' => true, 
            'qr_url' => $qrUrl,
            'qr_code' => $table->qr_code,
            'table' => $table,
            'message' => 'تم إنشاء QR Code جديد بنجاح'
        ]);
    }

    public function setManualTableQR(Request $request, $id)
    {
        $request->validate([
            'qr_code' => 'required|string|max:255|unique:tables,qr_code,' . $id
        ]);

        $table = Table::with('area')->findOrFail($id);
        
        // Set the manual QR code
        $success = $table->setQrCode($request->qr_code);
        
        if (!$success) {
            return response()->json([
                'success' => false,
                'message' => 'QR Code موجود بالفعل لطاولة أخرى'
            ], 422);
        }
        
        // Generate QR code URL for the table
        $baseUrl = request()->getSchemeAndHttpHost();
        $tenantSlug = $this->getTenantSlug();
        $qrUrl = "{$baseUrl}/restaurant/table/{$table->qr_code}?hash={$tenantSlug}";
        
        return response()->json([
            'success' => true, 
            'qr_url' => $qrUrl,
            'qr_code' => $table->qr_code,
            'table' => $table,
            'message' => 'تم تعيين QR Code بنجاح'
        ]);
    }

    private function getTenantSlug()
    {
        // Get tenant slug from authenticated user's branch tenant
        $user = auth()->user();
        if ($user && $user->branch && $user->branch->tenant) {
            $tenant = $user->branch->tenant;
            return $tenant->code ?? strtolower(str_replace([' ', '-', '_'], '', $tenant->name));
        }
        
        // Fallback to default if no tenant found
        return 'default-tenant';
    }

    // CRUD Methods for Waiter Requests
    public function waiterRequestsStore(Request $request)
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'waiter_id' => 'nullable|exists:users,id',
            'status' => 'required|in:pending,completed,cancelled',
            'request_type' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500'
        ]);

        $waiterRequest = WaiterRequest::create([
            'table_id' => $request->table_id,
            'waiter_id' => $request->waiter_id,
            'status' => $request->status ?? 'pending',
            'request_type' => $request->request_type ?? 'service',
            'notes' => $request->notes,
            'branch_id' => $user->branch_id
        ]);

        return response()->json([
            'success' => true, 
            'message' => 'تم إضافة طلب النادل بنجاح',
            'data' => $waiterRequest->load(['table.area', 'waiter'])
        ]);
    }

    public function waiterRequestsShow($id)
    {
        $waiterRequest = WaiterRequest::with(['table.area', 'waiter', 'branch'])->findOrFail($id);
        return response()->json([
            'success' => true,
            'data' => $waiterRequest
        ]);
    }

    public function waiterRequestsEdit($id)
    {
        $waiterRequest = WaiterRequest::with(['table.area', 'waiter'])->findOrFail($id);
        return response()->json([
            'success' => true,
            'data' => $waiterRequest
        ]);
    }

    public function waiterRequestsUpdate(Request $request, $id)
    {
        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'waiter_id' => 'nullable|exists:users,id',
            'status' => 'required|in:pending,completed,cancelled',
            'request_type' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500'
        ]);

        $waiterRequest = WaiterRequest::findOrFail($id);
        $waiterRequest->update([
            'table_id' => $request->table_id,
            'waiter_id' => $request->waiter_id,
            'status' => $request->status,
            'request_type' => $request->request_type,
            'notes' => $request->notes
        ]);

        return response()->json([
            'success' => true, 
            'message' => 'تم تحديث طلب النادل بنجاح',
            'data' => $waiterRequest->load(['table.area', 'waiter'])
        ]);
    }

    public function waiterRequestsDestroy($id)
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        $waiterRequest->delete();
        return response()->json([
            'success' => true, 
            'message' => 'تم حذف طلب النادل بنجاح'
        ]);
    }

    public function waiterRequestsComplete($id)
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        $waiterRequest->update([
            'status' => 'completed',
            'response_time' => now()
        ]);
        return response()->json([
            'success' => true, 
            'message' => 'تم إكمال طلب النادل بنجاح',
            'data' => $waiterRequest->load(['table.area', 'waiter'])
        ]);
    }

    public function waiterRequestsCancel($id)
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        $waiterRequest->update([
            'status' => 'cancelled',
            'response_time' => now()
        ]);
        return response()->json([
            'success' => true, 
            'message' => 'تم إلغاء طلب النادل بنجاح',
            'data' => $waiterRequest->load(['table.area', 'waiter'])
        ]);
    }

    public function waiterRequestsAssign($id)
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        $user = auth()->user();
        
        $waiterRequest->update([
            'waiter_id' => $user->id,
            'status' => 'in_progress'
        ]);
        
        return response()->json([
            'success' => true, 
            'message' => 'تم تكليفك بالطلب بنجاح',
            'data' => $waiterRequest->load(['table.area', 'waiter'])
        ]);
    }

    // Additional methods for dropdowns and data fetching
    public function getCustomersList()
    {
        $customers = Customer::select('id', 'name', 'phone', 'email')
            ->orderBy('name')
            ->get();
            
        return response()->json($customers);
    }

    public function getAreasList()
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $areas = Area::where('branch_id', $user->branch_id)
            ->select('id', 'name', 'description')
            ->orderBy('name')
            ->get();
            
        return response()->json($areas);
    }

    public function getAreas()
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $areas = Area::where('branch_id', $user->branch_id)
            ->select('id', 'name', 'description')
            ->orderBy('name')
            ->get();
            
        return response()->json($areas);
    }

    public function getCustomers()
    {
        $customers = Customer::select('id', 'first_name', 'last_name', 'phone', 'email')
            ->orderBy('first_name')
            ->get()
            ->map(function($customer) {
                return [
                    'id' => $customer->id,
                    'name' => $customer->first_name . ' ' . $customer->last_name,
                    'phone' => $customer->phone,
                    'email' => $customer->email
                ];
            });

        return response()->json($customers);
    }

    public function getTables(Request $request)
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = Table::with('area:id,name')
            ->whereHas('area', function($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })
            ->select('id', 'table_number', 'table_name', 'area_id', 'status', 'seating_capacity');

        // Filter by area if provided
        if ($request->has('area_id') && $request->area_id != '') {
            $query->where('area_id', $request->area_id);
        }

        $tables = $query->orderBy('table_number')->get()
            ->map(function($table) {
                return [
                    'id' => $table->id,
                    'name' => $table->table_name ?: $table->table_number,
                    'table_number' => $table->table_number,
                    'seating_capacity' => $table->seating_capacity,
                    'area' => $table->area
                ];
            });

        return response()->json($tables);
    }

    public function getWaiters()
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $waiters = User::where('role', 'waiter')
            ->where('branch_id', $user->branch_id)
            ->select('id', 'name', 'email')
            ->orderBy('name')
            ->get();
            
        return response()->json($waiters);
    }

    public function getReservationStatuses()
    {
        $statuses = [
            ['value' => 'pending', 'label' => 'في الانتظار'],
            ['value' => 'confirmed', 'label' => 'مؤكد'],
            ['value' => 'seated', 'label' => 'جالس'],
            ['value' => 'completed', 'label' => 'مكتمل'],
            ['value' => 'cancelled', 'label' => 'ملغي'],
            ['value' => 'no_show', 'label' => 'لم يحضر']
        ];
        
        return response()->json($statuses);
    }

    public function getTablesList(Request $request)
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = Table::with('area:id,name')
            ->whereHas('area', function($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })
            ->select('id', 'table_number', 'area_id', 'status', 'seating_capacity');
            
        // Filter by area if provided
        if ($request->has('area_id') && $request->area_id != '') {
            $query->where('area_id', $request->area_id);
        }
        
        $tables = $query->orderBy('table_number')->get();
        
        return response()->json($tables);
    }

    public function getWaitersList()
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $waiters = User::where('role', 'waiter')
            ->where('branch_id', $user->branch_id)
            ->select('id', 'name', 'email')
            ->orderBy('name')
            ->get();
            
        return response()->json($waiters);
    }

    public function getReservationStatusList()
    {
        $statuses = [
            ['value' => 'pending', 'label' => 'في الانتظار'],
            ['value' => 'confirmed', 'label' => 'مؤكد'],
            ['value' => 'seated', 'label' => 'جالس'],
            ['value' => 'completed', 'label' => 'مكتمل'],
            ['value' => 'cancelled', 'label' => 'ملغي'],
            ['value' => 'no_show', 'label' => 'لم يحضر']
        ];
        
        return response()->json($statuses);
    }

    /**
     * Get reservation statistics.
     */
    public function statistics(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $stats = $this->reservationService->getReservationStats(
                $user->branch_id,
                $request->get('date_from'),
                $request->get('date_to')
            );

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistics retrieved successfully'
            ]);
        } catch (\Exception $e) {
             return response()->json([
                 'success' => false,
                 'message' => 'Failed to retrieve statistics',
                 'error' => $e->getMessage()
             ], 500);
         }
     }

    /**
     * Get waiter requests statistics.
     */
    public function waiterRequestsStatistics(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $branchId = $user->branch_id;
            $today = now()->format('Y-m-d');

            // Get statistics
            $pending = WaiterRequest::where('branch_id', $branchId)
                ->where('status', 'pending')
                ->count();

            $inProgress = WaiterRequest::where('branch_id', $branchId)
                ->where('status', 'in_progress')
                ->count();

            $completedToday = WaiterRequest::where('branch_id', $branchId)
                ->where('status', 'completed')
                ->whereDate('created_at', $today)
                ->count();

            // Calculate average response time for completed requests today
            $avgResponseTime = WaiterRequest::where('branch_id', $branchId)
                ->where('status', 'completed')
                ->whereDate('created_at', $today)
                ->whereNotNull('response_time')
                ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, created_at, response_time)) as avg_time')
                ->value('avg_time');

            $avgResponseTime = $avgResponseTime ? round($avgResponseTime, 1) : 0;

            return response()->json([
                'pending' => $pending,
                'in_progress' => $inProgress,
                'completed_today' => $completedToday,
                'avg_response_time' => $avgResponseTime . ' دقيقة'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'pending' => 0,
                'in_progress' => 0,
                'completed_today' => 0,
                'avg_response_time' => '0 دقيقة'
            ]);
        }
    }

     /**
      * Export reservations.
      */
     public function export(Request $request)
     {
         try {
             $user = auth()->user();
             
             if (!$user->branch_id) {
                 return response()->json([
                     'success' => false,
                     'message' => 'User is not assigned to any branch'
                 ], 400);
             }

             // For now, just return a simple response
             // You can implement actual export functionality later
             return response()->json([
                 'success' => true,
                 'message' => 'Export functionality will be implemented soon'
             ]);
         } catch (\Exception $e) {
             return response()->json([
                 'success' => false,
                 'message' => 'Failed to export reservations',
                 'error' => $e->getMessage()
             ], 500);
         }
     }

    /**
     * Main reservations index page.
     */
    public function index()
    {
        return $this->reservationsIndex();
    }

    /**
     * Get reservations data for DataTables.
     */
    public function datatable(Request $request)
    {
        return $this->reservationsDataTable($request);
    }

    /**
     * Store a new reservation.
     */
    public function store(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // Basic validation
            $request->validate([
                'customer_name' => 'required|string|max:255',
                'customer_phone' => 'required|string|max:20',
                'party_size' => 'required|integer|min:1',
                'reservation_datetime' => 'required|date|after:now',
                'table_id' => 'required|exists:tables,id',
                'area_id' => 'required|exists:areas,id'
            ]);

            $data = $request->all();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;
            $data['user_id'] = $user->id;

            $reservation = $this->reservationService->createReservation($data);

            return response()->json([
                'success' => true,
                'data' => $reservation,
                'message' => 'Reservation created successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Show a specific reservation.
     */
    public function show($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::with(['customer', 'table', 'area', 'reservationStatus'])
                ->where('branch_id', $user->branch_id)
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $reservation,
                'message' => 'Reservation retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Reservation not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Show the form for editing a reservation.
     */
    public function edit($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::with(['table', 'area', 'status'])
                ->where('branch_id', $user->branch_id)
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $reservation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Reservation not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update a reservation.
     */
    public function update(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::where('branch_id', $user->branch_id)->findOrFail($id);
            
            $reservation->update($request->all());

            return response()->json([
                'success' => true,
                'data' => $reservation,
                'message' => 'Reservation updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Delete a reservation.
     */
    public function destroy($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::where('branch_id', $user->branch_id)->findOrFail($id);
            $reservation->delete();

            return response()->json([
                'success' => true,
                'message' => 'Reservation deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Confirm a reservation.
     */
    public function confirm($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::where('branch_id', $user->branch_id)->findOrFail($id);
            
            $confirmedStatus = ReservationStatus::where('name', 'confirmed')->first();
            if ($confirmedStatus) {
                $reservation->update(['reservation_status_id' => $confirmedStatus->id]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Reservation confirmed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to confirm reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Seat a reservation.
     */
    public function seat($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::where('branch_id', $user->branch_id)->findOrFail($id);
            
            $seatedStatus = ReservationStatus::where('name', 'seated')->first();
            if ($seatedStatus) {
                $reservation->update(['reservation_status_id' => $seatedStatus->id]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Reservation seated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to seat reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Complete a reservation.
     */
    public function complete($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::where('branch_id', $user->branch_id)->findOrFail($id);
            
            $completedStatus = ReservationStatus::where('name', 'completed')->first();
            if ($completedStatus) {
                $reservation->update(['reservation_status_id' => $completedStatus->id]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Reservation completed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Mark reservation as no-show.
     */
    public function noShow($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::where('branch_id', $user->branch_id)->findOrFail($id);
            
            $noShowStatus = ReservationStatus::where('name', 'no_show')->first();
            if ($noShowStatus) {
                $reservation->update(['reservation_status_id' => $noShowStatus->id]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Reservation marked as no-show successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark reservation as no-show',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Cancel a reservation.
     */
    public function cancel($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = Reservation::where('branch_id', $user->branch_id)->findOrFail($id);
            
            $cancelledStatus = ReservationStatus::where('name', 'cancelled')->first();
            if ($cancelledStatus) {
                $reservation->update(['reservation_status_id' => $cancelledStatus->id]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Reservation cancelled successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Check availability for a reservation.
     */
    public function checkAvailability(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $request->validate([
                'table_id' => 'required|exists:tables,id',
                'reservation_datetime' => 'required|date',
                'party_size' => 'required|integer|min:1'
            ]);

            $isAvailable = $this->reservationService->checkTableAvailability(
                $request->table_id,
                $request->reservation_datetime,
                $request->party_size,
                $user->branch_id
            );

            return response()->json([
                'success' => true,
                'available' => $isAvailable,
                'message' => $isAvailable ? 'Table is available' : 'Table is not available'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check availability',
                'error' => $e->getMessage()
            ], 400);
        }
    }



    /**
     * Get waiters dropdown.
     */
    public function waitersDropdown()
    {
        return $this->getWaitersList();
    }

    /**
     * Get reservation reports.
     */
    public function reservationReports(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // For now, just return basic statistics
            return $this->statistics($request);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate reports',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export waiter requests.
     */
    public function waiterRequestsExport(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // For now, just return a simple response
            // You can implement actual export functionality later
            return response()->json([
                'success' => true,
                'message' => 'Waiter requests export functionality will be implemented soon'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export waiter requests',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}