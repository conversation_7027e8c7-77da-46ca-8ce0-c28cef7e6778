@extends('layouts.master')

@section('title', 'تفاصيل موظف التوصيل')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">تفاصيل موظف التوصيل</h1>
                    <p class="mt-2 text-gray-600">عرض معلومات {{ $personnel->user->name }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('delivery.personnel.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-right mr-2"></i>
                        العودة للقائمة
                    </a>
                    <a href="{{ route('delivery.personnel.edit', $personnel->id) }}" 
                       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-edit mr-2"></i>
                        تعديل
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
            @include('delivery::personnel.partials.show-details')
        </div>
    </div>
</div>
@endsection