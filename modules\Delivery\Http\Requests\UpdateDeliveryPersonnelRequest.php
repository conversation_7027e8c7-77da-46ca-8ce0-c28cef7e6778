<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateDeliveryPersonnelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // return Auth::check();
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $personnelId = $this->route('id');

        return [
            'branch_id' => 'sometimes|required|exists:branches,id',
            'vehicle_type' => 'sometimes|required|in:bike,motorcycle,car,van,truck,bicycle,scooter',
            'vehicle_number' => [
                'sometimes',
                'required',
                'string',
                'max:20',
                Rule::unique('delivery_personnel', 'vehicle_number')->ignore($personnelId),
            ],
            'license_number' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                Rule::unique('delivery_personnel', 'license_number')->ignore($personnelId),
            ],
            'phone_number' => 'sometimes|required|string|max:20',
            'emergency_contact_name' => 'sometimes|required|string|max:100',
            'emergency_contact_phone' => 'sometimes|required|string|max:20',
            'max_concurrent_deliveries' => 'sometimes|integer|min:1|max:10',
            'delivery_radius' => 'sometimes|numeric|min:1|max:100',
            'hourly_rate' => 'sometimes|numeric|min:0',
            'commission_rate' => 'sometimes|numeric|min:0|max:100',
            'status' => 'sometimes|in:active,inactive,on_break,off_duty',
            'verification_status' => 'sometimes|in:pending,verified,rejected',
            'verification_notes' => 'sometimes|nullable|string|max:500',
            'is_available' => 'sometimes|boolean',
            'current_latitude' => 'sometimes|nullable|numeric|between:-90,90',
            'current_longitude' => 'sometimes|nullable|numeric|between:-180,180',
            'working_hours' => 'sometimes|array',
            'working_hours.*.day' => 'required_with:working_hours|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'working_hours.*.start_time' => 'required_with:working_hours|date_format:H:i',
            'working_hours.*.end_time' => 'required_with:working_hours|date_format:H:i|after:working_hours.*.start_time',
            'working_hours.*.is_working' => 'required_with:working_hours|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required',
            'branch_id.exists' => 'Selected branch does not exist',
            'vehicle_type.required' => 'Vehicle type is required',
            'vehicle_type.in' => 'Vehicle type must be one of: bike, motorcycle, car, van, truck, bicycle, scooter',
            'vehicle_number.required' => 'Vehicle number is required',
            'vehicle_number.unique' => 'This vehicle number is already registered',
            'license_number.required' => 'License number is required',
            'license_number.unique' => 'This license number is already registered',
            'phone_number.required' => 'Phone number is required',
            'emergency_contact_name.required' => 'Emergency contact name is required',
            'emergency_contact_phone.required' => 'Emergency contact phone is required',
            'max_concurrent_deliveries.min' => 'Maximum concurrent deliveries must be at least 1',
            'max_concurrent_deliveries.max' => 'Maximum concurrent deliveries cannot exceed 10',
            'delivery_radius.min' => 'Delivery radius must be at least 1 km',
            'delivery_radius.max' => 'Delivery radius cannot exceed 100 km',
            'hourly_rate.min' => 'Hourly rate cannot be negative',
            'commission_rate.min' => 'Commission rate cannot be negative',
            'commission_rate.max' => 'Commission rate cannot exceed 100%',
            'status.in' => 'Status must be one of: active, inactive, on_break, off_duty',
            'verification_status.in' => 'Verification status must be one of: pending, verified, rejected',
            'verification_notes.max' => 'Verification notes cannot exceed 500 characters',
            'current_latitude.between' => 'Latitude must be between -90 and 90',
            'current_longitude.between' => 'Longitude must be between -180 and 180',
            'working_hours.*.day.in' => 'Day must be a valid weekday',
            'working_hours.*.start_time.date_format' => 'Start time must be in HH:MM format',
            'working_hours.*.end_time.date_format' => 'End time must be in HH:MM format',
            'working_hours.*.end_time.after' => 'End time must be after start time',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate working hours uniqueness if provided
            if ($this->has('working_hours')) {
                $days = collect($this->working_hours)->pluck('day');
                if ($days->count() !== $days->unique()->count()) {
                    $validator->errors()->add('working_hours', 'Duplicate days found in working hours');
                }
            }

            // Check if personnel has active deliveries when changing status to inactive
            if ($this->has('status') && in_array($this->status, ['inactive', 'off_duty'])) {
                $personnelId = $this->route('id');
                $activeDeliveries = \Modules\Delivery\Entities\DeliveryAssignment::where('delivery_personnel_id', $personnelId)
                    ->whereIn('status', ['assigned', 'picked_up', 'in_transit'])
                    ->count();

                if ($activeDeliveries > 0) {
                    $validator->errors()->add('status', 'Cannot change status to inactive/off_duty while having active deliveries');
                }
            }

            // Validate coordinates are provided together
            if ($this->has('current_latitude') && !$this->has('current_longitude')) {
                $validator->errors()->add('current_longitude', 'Longitude is required when latitude is provided');
            }

            if ($this->has('current_longitude') && !$this->has('current_latitude')) {
                $validator->errors()->add('current_latitude', 'Latitude is required when longitude is provided');
            }
        });
    }
}