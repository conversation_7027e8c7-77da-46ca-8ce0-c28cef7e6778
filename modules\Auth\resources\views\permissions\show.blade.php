@extends('layouts.master')

@section('title', 'تفاصيل الصلاحية: ' . $permission->name)

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('permissions.index') }}" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-arrow-right text-xl"></i>
                        </a>
                        <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                            <i class="fas fa-key text-blue-600"></i>
                            تفاصيل الصلاحية
                        </h1>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="editPermission({{ $permission->id }})" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            تعديل
                        </button>
                        <button onclick="deletePermission({{ $permission->id }})" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Details -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Main Info -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">معلومات الصلاحية</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية</label>
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-semibold text-gray-900">{{ $permission->name }}</span>
                                    <button onclick="copyToClipboard('{{ $permission->name }}')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحارس</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @if($permission->guard_name === 'web') bg-blue-100 text-blue-800
                                    @elseif($permission->guard_name === 'api') bg-green-100 text-green-800
                                    @else bg-purple-100 text-purple-800 @endif">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    {{ ucfirst($permission->guard_name) }}
                                </span>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">المجموعة</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-layer-group mr-2"></i>
                                    {{ explode('.', $permission->name)[0] ?? 'غير محدد' }}
                                </span>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الإجراء</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-cog mr-2"></i>
                                    {{ explode('.', $permission->name)[1] ?? 'غير محدد' }}
                                </span>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنشاء</label>
                                <span class="text-gray-900">{{ $permission->created_at->format('Y-m-d H:i') }}</span>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">آخر تحديث</label>
                                <span class="text-gray-900">{{ $permission->updated_at->format('Y-m-d H:i') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">الإحصائيات</h2>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users-cog text-blue-600"></i>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm font-medium text-gray-600">الأدوار</p>
                                    <p class="text-lg font-bold text-blue-600">{{ $permission->roles->count() }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-green-600"></i>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm font-medium text-gray-600">المستخدمون</p>
                                    <p class="text-lg font-bold text-green-600">{{ $permission->users->count() }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">إجراءات سريعة</h2>
                    </div>
                    <div class="p-6 space-y-3">
                        <button onclick="assignToRole()" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            تعيين لدور
                        </button>
                        <button onclick="assignToUser()" class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-user-plus mr-2"></i>
                            تعيين لمستخدم
                        </button>
                        <button onclick="duplicatePermission()" class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-copy mr-2"></i>
                            نسخ الصلاحية
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    <button onclick="switchTab('roles')" id="roles-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="fas fa-users-cog mr-2"></i>
                        الأدوار ({{ $permission->roles->count() }})
                    </button>
                    <button onclick="switchTab('users')" id="users-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="fas fa-users mr-2"></i>
                        المستخدمون ({{ $permission->users->count() }})
                    </button>
                </nav>
            </div>

            <!-- Roles Tab -->
            <div id="roles-content" class="tab-content p-6">
                @if($permission->roles->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($permission->roles as $role)
                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-user-tag text-blue-600"></i>
                                        </div>
                                        <div class="mr-3">
                                            <h3 class="text-sm font-medium text-gray-900">{{ $role->name }}</h3>
                                            <p class="text-xs text-gray-500">{{ $role->guard_name }}</p>
                                        </div>
                                    </div>
                                    <div class="flex space-x-1">
                                        <a href="{{ route('roles.show', $role->id) }}" class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button onclick="revokeFromRole({{ $role->id }})" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $role->users_count ?? 0 }} مستخدم
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-users-cog text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أدوار</h3>
                        <p class="text-gray-500 mb-4">لم يتم تعيين هذه الصلاحية لأي دور بعد</p>
                        <button onclick="assignToRole()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            تعيين لدور
                        </button>
                    </div>
                @endif
            </div>

            <!-- Users Tab -->
            <div id="users-content" class="tab-content p-6 hidden">
                @if($permission->users->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($permission->users as $user)
                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-user text-green-600"></i>
                                        </div>
                                        <div class="mr-3">
                                            <h3 class="text-sm font-medium text-gray-900">{{ $user->name }}</h3>
                                            <p class="text-xs text-gray-500">{{ $user->email }}</p>
                                        </div>
                                    </div>
                                    <div class="flex space-x-1">
                                        <a href="{{ route('users.show', $user->id) }}" class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button onclick="revokeFromUser({{ $user->id }})" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                        @if($user->status === 'active') bg-green-100 text-green-800 
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ $user->status === 'active' ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا يوجد مستخدمون</h3>
                        <p class="text-gray-500 mb-4">لم يتم تعيين هذه الصلاحية لأي مستخدم مباشرة</p>
                        <button onclick="assignToUser()" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-user-plus mr-2"></i>
                            تعيين لمستخدم
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Edit Permission Modal -->
<div id="permission-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">تعديل الصلاحية</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="permission-form">
                @csrf
                @method('PUT')
                
                <div class="px-6 py-4 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية *</label>
                        <input type="text" id="permission-name" name="name" value="{{ $permission->name }}" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div id="name-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحارس *</label>
                        <select id="permission-guard" name="guard_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="web" {{ $permission->guard_name === 'web' ? 'selected' : '' }}>ويب</option>
                            <option value="api" {{ $permission->guard_name === 'api' ? 'selected' : '' }}>API</option>
                            <option value="sanctum" {{ $permission->guard_name === 'sanctum' ? 'selected' : '' }}>Sanctum</option>
                        </select>
                        <div id="guard-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Assign to Role Modal -->
<div id="assign-role-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">تعيين الصلاحية لدور</h3>
                    <button onclick="closeAssignRoleModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="assign-role-form">
                @csrf
                
                <div class="px-6 py-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختر الأدوار</label>
                    <div class="max-h-60 overflow-y-auto border border-gray-300 rounded-lg p-3">
                        @foreach($availableRoles as $role)
                            <label class="flex items-center py-2">
                                <input type="checkbox" name="role_ids[]" value="{{ $role->id }}" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="mr-3 text-sm">{{ $role->name }}</span>
                                <span class="text-xs text-gray-500">({{ $role->guard_name }})</span>
                            </label>
                        @endforeach
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeAssignRoleModal()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-check mr-2"></i>
                        تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize first tab
    switchTab('roles');

    // Form submissions
    $('#permission-form').on('submit', function(e) {
        e.preventDefault();
        updatePermission();
    });

    $('#assign-role-form').on('submit', function(e) {
        e.preventDefault();
        assignPermissionToRoles();
    });
});

function switchTab(tab) {
    // Remove active class from all tabs
    $('.tab-button').removeClass('border-blue-500 text-blue-600').addClass('border-transparent text-gray-500');
    $('.tab-content').addClass('hidden');
    
    // Add active class to selected tab
    $(`#${tab}-tab`).removeClass('border-transparent text-gray-500').addClass('border-blue-500 text-blue-600');
    $(`#${tab}-content`).removeClass('hidden');
}

function editPermission(id) {
    $('#permission-modal').removeClass('hidden');
}

function deletePermission(id) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/permissions/${id}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    Swal.fire('تم الحذف!', 'تم حذف الصلاحية بنجاح', 'success').then(() => {
                        window.location.href = '{{ route("permissions.index") }}';
                    });
                },
                error: function(xhr) {
                    let message = 'حدث خطأ أثناء حذف الصلاحية';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Swal.fire('خطأ!', message, 'error');
                }
            });
        }
    });
}

function assignToRole() {
    $('#assign-role-modal').removeClass('hidden');
}

function assignToUser() {
    // This would open a user assignment modal
    Swal.fire('قريباً!', 'ستتوفر هذه الميزة قريباً', 'info');
}

function duplicatePermission() {
    const currentName = '{{ $permission->name }}';
    const guardName = '{{ $permission->guard_name }}';
    
    Swal.fire({
        title: 'نسخ الصلاحية',
        input: 'text',
        inputLabel: 'اسم الصلاحية الجديدة',
        inputValue: currentName + '_copy',
        showCancelButton: true,
        confirmButtonText: 'نسخ',
        cancelButtonText: 'إلغاء',
        inputValidator: (value) => {
            if (!value) {
                return 'يرجى إدخال اسم الصلاحية!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '/admin/permissions',
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    name: result.value,
                    guard_name: guardName
                },
                success: function(response) {
                    Swal.fire('تم النسخ!', 'تم نسخ الصلاحية بنجاح', 'success');
                },
                error: function(xhr) {
                    let message = 'حدث خطأ أثناء نسخ الصلاحية';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Swal.fire('خطأ!', message, 'error');
                }
            });
        }
    });
}

function revokeFromRole(roleId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'سيتم إلغاء تعيين هذه الصلاحية من الدور',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، ألغ التعيين!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/roles/${roleId}/revoke-permission`,
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    permission_id: {{ $permission->id }}
                },
                success: function(response) {
                    Swal.fire('تم الإلغاء!', 'تم إلغاء تعيين الصلاحية من الدور', 'success').then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء إلغاء التعيين', 'error');
                }
            });
        }
    });
}

function revokeFromUser(userId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'سيتم إلغاء تعيين هذه الصلاحية من المستخدم',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، ألغ التعيين!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/users/${userId}/revoke-permission`,
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    permission_id: {{ $permission->id }}
                },
                success: function(response) {
                    Swal.fire('تم الإلغاء!', 'تم إلغاء تعيين الصلاحية من المستخدم', 'success').then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء إلغاء التعيين', 'error');
                }
            });
        }
    });
}

function updatePermission() {
    const formData = new FormData($('#permission-form')[0]);
    
    $.ajax({
        url: `/admin/permissions/{{ $permission->id }}`,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            Swal.fire('نجح!', 'تم تحديث الصلاحية بنجاح', 'success').then(() => {
                location.reload();
            });
        },
        error: function(xhr) {
            if (xhr.status === 422) {
                displayErrors(xhr.responseJSON.errors);
            } else {
                Swal.fire('خطأ!', 'حدث خطأ أثناء تحديث الصلاحية', 'error');
            }
        }
    });
}

function assignPermissionToRoles() {
    const formData = new FormData($('#assign-role-form')[0]);
    formData.append('permission_id', {{ $permission->id }});
    
    $.ajax({
        url: '/admin/permissions/assign-to-roles',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            Swal.fire('نجح!', 'تم تعيين الصلاحية للأدوار بنجاح', 'success').then(() => {
                location.reload();
            });
        },
        error: function(xhr) {
            Swal.fire('خطأ!', 'حدث خطأ أثناء تعيين الصلاحية', 'error');
        }
    });
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        Swal.fire({
            icon: 'success',
            title: 'تم النسخ!',
            text: 'تم نسخ النص إلى الحافظة',
            timer: 1500,
            showConfirmButton: false
        });
    });
}

function closeModal() {
    $('#permission-modal').addClass('hidden');
    clearErrors();
}

function closeAssignRoleModal() {
    $('#assign-role-modal').addClass('hidden');
}

function clearErrors() {
    $('.text-red-500').addClass('hidden');
    $('.border-red-500').removeClass('border-red-500');
}

function displayErrors(errors) {
    clearErrors();
    
    for (let field in errors) {
        const errorElement = $(`#${field}-error`);
        const inputElement = $(`#permission-${field}`);
        
        if (errorElement.length) {
            errorElement.text(errors[field][0]).removeClass('hidden');
            inputElement.addClass('border-red-500');
        }
    }
}
</script>
@endpush