@extends('layouts.master')

@section('title', 'User & Role Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">User & Role Settings</h1>
                        <p class="text-purple-100">Manage user roles, permissions, and access controls</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-purple-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-purple-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-purple-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-purple-200 mx-2"></i>
                                <span class="text-white font-medium">User Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white rounded-lg shadow-lg mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active" data-tab="roles">
                    <i class="fas fa-user-tag mr-2"></i>
                    Roles Management
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="permissions">
                    <i class="fas fa-key mr-2"></i>
                    Permissions
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="access">
                    <i class="fas fa-shield-alt mr-2"></i>
                    Access Control
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="defaults">
                    <i class="fas fa-cog mr-2"></i>
                    Default Settings
                </button>
            </nav>
        </div>
    </div>

    <!-- Roles Management Tab -->
    <div id="roles-tab" class="tab-content">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Roles Management</h3>
                        <p class="text-gray-600 mt-1">Create and manage user roles with specific permissions</p>
                    </div>
                    <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add Role
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Default Roles -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Administrator</h4>
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">System</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Full system access with all permissions</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">5 users</span>
                            <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Manager</h4>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Custom</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Branch management and reporting access</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">12 users</span>
                            <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Cashier</h4>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">POS operations and order management</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">25 users</span>
                            <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Kitchen Staff</h4>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Limited</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Kitchen display and order preparation</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">8 users</span>
                            <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Waiter</h4>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Order taking and customer service</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">15 users</span>
                            <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                        </div>
                    </div>

                    <!-- Add New Role Card -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-plus text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-500 text-sm">Add New Role</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Permissions Tab -->
    <div id="permissions-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Permission Categories</h3>
                <p class="text-gray-600 mt-1">Manage system permissions and access levels</p>
            </div>

            <div class="p-6">
                <div class="space-y-6">
                    <!-- POS Permissions -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">POS Operations</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">Create Orders</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">Modify Orders</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Cancel Orders</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">Process Payments</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Apply Discounts</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Refund Orders</span>
                            </label>
                        </div>
                    </div>

                    <!-- Inventory Permissions -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Inventory Management</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">View Inventory</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Update Stock</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Add Products</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Delete Products</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Generate Reports</span>
                            </label>
                        </div>
                    </div>

                    <!-- Reports Permissions -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Reports & Analytics</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Sales Reports</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Financial Reports</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Export Data</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
                    <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Permissions
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Access Control Tab -->
    <div id="access-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Access Control Settings</h3>
                <p class="text-gray-600 mt-1">Configure login restrictions and security policies</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                            Session Timeout (minutes)
                        </label>
                        <input type="number" id="session_timeout" name="session_timeout" value="30" min="5" max="480"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="max_login_attempts" class="block text-sm font-medium text-gray-700 mb-2">
                            Max Login Attempts
                        </label>
                        <input type="number" id="max_login_attempts" name="max_login_attempts" value="5" min="1" max="10"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="lockout_duration" class="block text-sm font-medium text-gray-700 mb-2">
                            Lockout Duration (minutes)
                        </label>
                        <input type="number" id="lockout_duration" name="lockout_duration" value="15" min="1" max="60"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="password_expiry" class="block text-sm font-medium text-gray-700 mb-2">
                            Password Expiry (days)
                        </label>
                        <input type="number" id="password_expiry" name="password_expiry" value="90" min="30" max="365"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="require_2fa" name="require_2fa" value="1"
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="require_2fa" class="ml-2 text-sm text-gray-700">
                            Require Two-Factor Authentication for all users
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="force_password_change" name="force_password_change" value="1" checked
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="force_password_change" class="ml-2 text-sm text-gray-700">
                            Force password change on first login
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="log_user_activity" name="log_user_activity" value="1" checked
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="log_user_activity" class="ml-2 text-sm text-gray-700">
                            Log user activity and access attempts
                        </label>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Access Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Default Settings Tab -->
    <div id="defaults-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Default User Settings</h3>
                <p class="text-gray-600 mt-1">Configure default settings for new users</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="default_role" class="block text-sm font-medium text-gray-700 mb-2">
                            Default Role for New Users
                        </label>
                        <select id="default_role" name="default_role" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                            <option value="cashier">Cashier</option>
                            <option value="waiter">Waiter</option>
                            <option value="kitchen">Kitchen Staff</option>
                        </select>
                    </div>

                    <div>
                        <label for="default_branch" class="block text-sm font-medium text-gray-700 mb-2">
                            Default Branch Assignment
                        </label>
                        <select id="default_branch" name="default_branch" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                            <option value="">No Default Branch</option>
                            <option value="main">Main Branch</option>
                            <option value="downtown">Downtown Branch</option>
                        </select>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_activate" name="auto_activate" value="1" checked
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="auto_activate" class="ml-2 text-sm text-gray-700">
                            Auto-activate new user accounts
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="send_welcome_email" name="send_welcome_email" value="1" checked
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="send_welcome_email" class="ml-2 text-sm text-gray-700">
                            Send welcome email to new users
                        </label>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Default Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Tab switching
    $('.settings-tab').on('click', function() {
        const tabName = $(this).data('tab');
        
        // Update tab appearance
        $('.settings-tab').removeClass('border-purple-500 text-purple-600').addClass('border-transparent text-gray-500');
        $(this).removeClass('border-transparent text-gray-500').addClass('border-purple-500 text-purple-600');
        
        // Show/hide tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}-tab`).removeClass('hidden');
    });

    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Add loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });
});
</script>
@endpush
