<?php

namespace Modules\Auth\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionWebController extends Controller
{
    /**
     * Display a listing of permissions.
     */
    public function index()
    {
        $permissions = Permission::all()
            ->groupBy(function ($permission) {
                return explode('.', $permission->name)[0] ?? 'other';
            });
        
        $groups = $permissions->keys();
        
        return view('Auth::permissions.index', compact('permissions', 'groups'));
    }

    /**
     * Show the form for creating a new permission.
     */
    public function create()
    {
        $groups = Permission::all()
            ->groupBy(function ($permission) {
                return explode('.', $permission->name)[0] ?? 'other';
            })
            ->keys();
        
        return view('Auth::permissions.create', compact('groups'));
    }

    /**
     * Store a newly created permission.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|unique:permissions,name|max:255',
            'guard_name' => 'required|string|max:255',
        ]);

        try {
            Permission::create([
                'name' => $request->name,
                'guard_name' => $request->guard_name,
            ]);
            
            return redirect()->route('permissions.index')
                ->with('success', 'تم إنشاء الصلاحية بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء الصلاحية: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified permission.
     */
    public function show(Permission $permission)
    {
        $permission->load(['roles.users', 'users']);
        
        // Get available roles that don't have this permission
        $availableRoles = Role::where('guard_name', $permission->guard_name)
            ->whereDoesntHave('permissions', function ($query) use ($permission) {
                $query->where('permissions.id', $permission->id);
            })
            ->get();
        
        return view('Auth::permissions.show', compact('permission', 'availableRoles'));
    }

    /**
     * Show the form for editing the specified permission.
     */
    public function edit(Permission $permission)
    {
        return view('Auth::permissions.edit', compact('permission'));
    }

    /**
     * Update the specified permission.
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'guard_name' => 'required|string|max:255',
        ]);

        try {
            $permission->update([
                'name' => $request->name,
                'guard_name' => $request->guard_name,
            ]);
            
            return redirect()->route('permissions.show', $permission)
                ->with('success', 'تم تحديث الصلاحية بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الصلاحية: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified permission.
     */
    public function destroy(Permission $permission)
    {
        try {
            // Check if permission is assigned to any roles
            if ($permission->roles()->count() > 0) {
                return back()->with('error', 'لا يمكن حذف الصلاحية لوجود أدوار مرتبطة بها');
            }

            $permission->delete();
            
            return redirect()->route('permissions.index')
                ->with('success', 'تم حذف الصلاحية بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف الصلاحية: ' . $e->getMessage());
        }
    }

    /**
     * Get permissions data for DataTable.
     */
    public function getPermissionsData(Request $request)
    {
        $query = Permission::withCount(['roles'])
            ->select(['id', 'name', 'guard_name', 'created_at']);

        // Apply filters
        if ($request->filled('group')) {
            $query->where('name', 'like', $request->group . '.%');
        }

        if ($request->filled('guard_name')) {
            $query->where('guard_name', $request->guard_name);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('group', function ($permission) {
                return explode('.', $permission->name)[0] ?? 'other';
            })
            ->addColumn('roles_count', function ($permission) {
                return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' . $permission->roles_count . ' دور</span>';
            })
            ->addColumn('guard_badge', function ($permission) {
                $badges = [
                    'web' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">ويب</span>',
                    'api' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">API</span>',
                    'sanctum' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Sanctum</span>',
                ];
                return $badges[$permission->guard_name] ?? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">' . $permission->guard_name . '</span>';
            })
            ->addColumn('action', function ($permission) {
                $actions = '<div class="flex items-center space-x-1">';
                $actions .= '<a href="' . route('permissions.show', $permission) . '" class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded hover:bg-blue-200 transition-colors duration-200" title="عرض"><i class="fas fa-eye"></i></a>';
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded hover:bg-indigo-200 transition-colors duration-200" onclick="editPermission(' . $permission->id . ')" title="تعديل"><i class="fas fa-edit"></i></button>';
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-100 rounded hover:bg-red-200 transition-colors duration-200" onclick="deletePermission(' . $permission->id . ')" title="حذف"><i class="fas fa-trash"></i></button>';
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['roles_count', 'guard_badge', 'action'])
            ->make(true);
    }

    /**
     * Bulk create permissions.
     */
    public function bulkCreate(Request $request)
    {
        $request->validate([
            'permissions' => 'required|array',
            'permissions.*.name' => 'required|string|unique:permissions,name',
            'permissions.*.guard_name' => 'required|string',
        ]);

        try {
            DB::beginTransaction();

            foreach ($request->permissions as $permissionData) {
                Permission::create($permissionData);
            }

            DB::commit();
            
            return back()->with('success', 'تم إنشاء الصلاحيات بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'حدث خطأ أثناء إنشاء الصلاحيات: ' . $e->getMessage());
        }
    }

    /**
     * Bulk delete permissions.
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'permission_ids' => 'required|array',
            'permission_ids.*' => 'exists:permissions,id',
        ]);

        try {
            DB::beginTransaction();

            $permissions = Permission::whereIn('id', $request->permission_ids)->get();
            
            foreach ($permissions as $permission) {
                // Check if permission is assigned to any roles
                if ($permission->roles()->count() > 0) {
                    throw new \Exception('لا يمكن حذف الصلاحية "' . $permission->name . '" لوجود أدوار مرتبطة بها');
                }
            }

            Permission::whereIn('id', $request->permission_ids)->delete();

            DB::commit();
            
            return back()->with('success', 'تم حذف الصلاحيات المحددة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'حدث خطأ أثناء حذف الصلاحيات: ' . $e->getMessage());
        }
    }

    /**
     * Get permission groups.
     */
    public function groups()
    {
        $groups = Permission::all()
            ->groupBy(function ($permission) {
                return explode('.', $permission->name)[0] ?? 'other';
            })
            ->map(function ($permissions, $group) {
                return [
                    'name' => $group,
                    'count' => $permissions->count(),
                    'permissions' => $permissions->pluck('name')->toArray(),
                ];
            })
            ->values();

        return response()->json($groups);
    }

    /**
     * Get permissions for a specific group.
     */
    public function groupPermissions(Request $request, $group)
    {
        $permissions = Permission::where('name', 'like', $group . '.%')
            ->withCount(['roles'])
            ->get();

        return DataTables::of($permissions)
            ->addIndexColumn()
            ->addColumn('roles_count', function ($permission) {
                return '<span class="badge badge-info">' . $permission->roles_count . ' دور</span>';
            })
            ->addColumn('guard_badge', function ($permission) {
                $badges = [
                    'web' => '<span class="badge badge-primary">ويب</span>',
                    'api' => '<span class="badge badge-secondary">API</span>',
                    'sanctum' => '<span class="badge badge-warning">Sanctum</span>',
                ];
                return $badges[$permission->guard_name] ?? '<span class="badge badge-light">' . $permission->guard_name . '</span>';
            })
            ->addColumn('action', function ($permission) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('permissions.show', $permission) . '" class="btn btn-sm btn-info" title="عرض"><i class="fa fa-eye"></i></a>';
                $actions .= '<a href="' . route('permissions.edit', $permission) . '" class="btn btn-sm btn-primary" title="تعديل"><i class="fa fa-edit"></i></a>';
                $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deletePermission(' . $permission->id . ')" title="حذف"><i class="fa fa-trash"></i></button>';
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['roles_count', 'guard_badge', 'action'])
            ->make(true);
    }

    /**
     * Assign permission to roles.
     */
    public function assignToRoles(Request $request)
    {
        $request->validate([
            'permission_id' => 'required|exists:permissions,id',
            'role_ids' => 'required|array',
            'role_ids.*' => 'exists:roles,id',
        ]);

        try {
            $permission = Permission::findOrFail($request->permission_id);
            $roles = Role::whereIn('id', $request->role_ids)->get();

            foreach ($roles as $role) {
                $role->givePermissionTo($permission);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تعيين الصلاحية للأدوار بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعيين الصلاحية: ' . $e->getMessage()
            ], 500);
        }
    }
}
