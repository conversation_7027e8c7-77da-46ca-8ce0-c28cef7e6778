<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Documentation - Reservation Module</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4f46e5',
                        secondary: '#6366f1'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <i class="fas fa-code text-primary text-2xl ml-3"></i>
                        <h1 class="text-2xl font-bold text-gray-900">API Documentation</h1>
                    </div>
                    <a href="{{ route('reservation.dashboard') }}" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Overview -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle text-primary ml-2"></i>
                    نظرة عامة على API
                </h2>
                <p class="text-gray-600 mb-4">
                    يوفر نظام الحجوزات API شامل لإدارة الحجوزات، طلبات النادل، المناطق، والطاولات.
                    جميع endpoints تتطلب المصادقة باستخدام Sanctum tokens.
                </p>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-900 mb-2">Base URL:</h3>
                    <code class="text-blue-800">{{ url('/api/reservation') }}</code>
                </div>
            </div>

            <!-- API Endpoints -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Reservations API -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-calendar-alt text-green-500 ml-2"></i>
                        Reservations API
                    </h3>
                    <div class="space-y-3">
                        <div class="border-r-4 border-green-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">GET /reservations</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">GET</span>
                            </div>
                            <p class="text-gray-600 text-sm">قائمة جميع الحجوزات</p>
                        </div>
                        
                        <div class="border-r-4 border-blue-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">POST /reservations</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">POST</span>
                            </div>
                            <p class="text-gray-600 text-sm">إنشاء حجز جديد</p>
                        </div>
                        
                        <div class="border-r-4 border-yellow-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">PUT /reservations/{id}</span>
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">PUT</span>
                            </div>
                            <p class="text-gray-600 text-sm">تحديث حجز موجود</p>
                        </div>
                        
                        <div class="border-r-4 border-red-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">DELETE /reservations/{id}</span>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">DELETE</span>
                            </div>
                            <p class="text-gray-600 text-sm">حذف حجز</p>
                        </div>
                        
                        <div class="border-r-4 border-purple-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">POST /reservations/{id}/confirm</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">POST</span>
                            </div>
                            <p class="text-gray-600 text-sm">تأكيد حجز</p>
                        </div>
                    </div>
                </div>

                <!-- Waiter Requests API -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-bell text-orange-500 ml-2"></i>
                        Waiter Requests API
                    </h3>
                    <div class="space-y-3">
                        <div class="border-r-4 border-green-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">GET /waiter-requests</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">GET</span>
                            </div>
                            <p class="text-gray-600 text-sm">قائمة طلبات النادل</p>
                        </div>
                        
                        <div class="border-r-4 border-blue-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">POST /waiter-requests</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">POST</span>
                            </div>
                            <p class="text-gray-600 text-sm">إنشاء طلب نادل جديد</p>
                        </div>
                        
                        <div class="border-r-4 border-purple-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">POST /waiter-requests/{id}/complete</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">POST</span>
                            </div>
                            <p class="text-gray-600 text-sm">إكمال طلب النادل</p>
                        </div>
                        
                        <div class="border-r-4 border-gray-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">GET /waiter-requests/table/{id}</span>
                                <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">GET</span>
                            </div>
                            <p class="text-gray-600 text-sm">طلبات طاولة محددة</p>
                        </div>
                    </div>
                </div>

                <!-- Areas API -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-map-marked-alt text-teal-500 ml-2"></i>
                        Areas API
                    </h3>
                    <div class="space-y-3">
                        <div class="border-r-4 border-green-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">GET /areas</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">GET</span>
                            </div>
                            <p class="text-gray-600 text-sm">قائمة جميع المناطق</p>
                        </div>
                        
                        <div class="border-r-4 border-blue-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">POST /areas</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">POST</span>
                            </div>
                            <p class="text-gray-600 text-sm">إنشاء منطقة جديدة</p>
                        </div>
                        
                        <div class="border-r-4 border-yellow-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">PUT /areas/{id}</span>
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">PUT</span>
                            </div>
                            <p class="text-gray-600 text-sm">تحديث منطقة</p>
                        </div>
                        
                        <div class="border-r-4 border-red-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">DELETE /areas/{id}</span>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">DELETE</span>
                            </div>
                            <p class="text-gray-600 text-sm">حذف منطقة</p>
                        </div>
                    </div>
                </div>

                <!-- Tables API -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-table text-indigo-500 ml-2"></i>
                        Tables API
                    </h3>
                    <div class="space-y-3">
                        <div class="border-r-4 border-green-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">GET /tables</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">GET</span>
                            </div>
                            <p class="text-gray-600 text-sm">قائمة جميع الطاولات</p>
                        </div>
                        
                        <div class="border-r-4 border-blue-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">POST /tables</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">POST</span>
                            </div>
                            <p class="text-gray-600 text-sm">إنشاء طاولة جديدة</p>
                        </div>
                        
                        <div class="border-r-4 border-gray-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">GET /tables/area/{id}</span>
                                <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">GET</span>
                            </div>
                            <p class="text-gray-600 text-sm">طاولات منطقة محددة</p>
                        </div>
                        
                        <div class="border-r-4 border-purple-500 pr-4">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">POST /tables/{id}/status</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">POST</span>
                            </div>
                            <p class="text-gray-600 text-sm">تحديث حالة الطاولة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Authentication -->
            <div class="bg-white rounded-lg shadow-sm p-6 mt-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-shield-alt text-red-500 ml-2"></i>
                    المصادقة والأمان
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">Headers مطلوبة:</h3>
                        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                            <div><code class="text-sm">Authorization: Bearer {token}</code></div>
                            <div><code class="text-sm">Accept: application/json</code></div>
                            <div><code class="text-sm">Content-Type: application/json</code></div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">Response Format:</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <pre class="text-sm text-gray-700">{
  "success": true,
  "data": {...},
  "message": "Success message"
}</pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testing Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 mt-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-flask text-green-500 ml-2"></i>
                    اختبار API
                </h2>
                <p class="text-gray-600 mb-4">
                    يمكنك اختبار API endpoints باستخدام أدوات مثل Postman أو curl.
                </p>
                <div class="flex space-x-4">
                    <a href="{{ route('reservation.api-tester') }}" class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-play ml-2"></i>
                        API Tester
                    </a>
                    <a href="#" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-download ml-2"></i>
                        Download Postman Collection
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>