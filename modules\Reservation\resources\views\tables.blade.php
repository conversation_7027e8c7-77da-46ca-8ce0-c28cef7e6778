@extends('layouts.master')

@section('css')
<!-- DataTables CSS from CDN -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
<!-- Select2 CSS from CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<!-- SweetAlert CSS from CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert@2.1.2/dist/sweetalert.min.css">
<style>
    /* Status badge styles */
    .status-available { 
        @apply bg-green-100 text-green-800 border border-green-200; 
    }
    .status-occupied { 
        @apply bg-red-100 text-red-800 border border-red-200; 
    }
    .status-reserved { 
        @apply bg-yellow-100 text-yellow-800 border border-yellow-200; 
    }
    .status-cleaning { 
        @apply bg-blue-100 text-blue-800 border border-blue-200; 
    }
    .status-out_of_order { 
        @apply bg-gray-100 text-gray-800 border border-gray-200; 
    }
    
    /* Custom animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .animate-fade-in-up {
        animation: fadeInUp 0.3s ease-out;
    }
    
    /* Responsive grid improvements */
    @media (max-width: 640px) {
        #tables-container {
            grid-template-columns: 1fr;
        }
    }
    
    @media (min-width: 641px) and (max-width: 1024px) {
        #tables-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (min-width: 1025px) {
        #tables-container {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (min-width: 1280px) {
        #tables-container {
            grid-template-columns: repeat(4, 1fr);
        }
    }
</style>
@endsection

@section('page-header')
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
            <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                <i class="mdi mdi-table-furniture text-blue-600 text-xl"></i>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">إدارة الطاولات</h1>
                <p class="text-sm text-gray-500">إدارة وتنظيم طاولات المطعم</p>
            </div>
        </div>
        <div class="mt-4 sm:mt-0">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" id="add-table-btn">
                <i class="mdi mdi-plus text-lg ml-2"></i>
                إضافة طاولة جديدة
            </button>
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="p-6 space-y-6">
    <!-- Filters Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <i class="mdi mdi-filter-variant text-gray-400 text-xl ml-2"></i>
                <h3 class="text-lg font-semibold text-gray-900">فلترة الطاولات</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">المنطقة</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="area-filter">
                        <option value="">جميع المناطق</option>
                    </select>
                </div>
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">الحالة</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="status-filter">
                        <option value="">جميع الحالات</option>
                        <option value="available">متاحة</option>
                        <option value="occupied">مشغولة</option>
                        <option value="reserved">محجوزة</option>
                        <option value="cleaning">تنظيف</option>
                        <option value="out_of_order">خارج الخدمة</option>
                    </select>
                </div>
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">السعة الدنيا</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="capacity-filter" placeholder="أدخل السعة" min="1">
                </div>
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">الإجراءات</label>
                    <div class="flex gap-2">
                        <button type="button" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" id="apply-filters">
                            تطبيق
                        </button>
                        <button type="button" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2" id="clear-filters">
                            مسح
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Grid Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <i class="mdi mdi-table-furniture text-gray-400 text-xl ml-2"></i>
                    <h3 class="text-lg font-semibold text-gray-900">قائمة الطاولات</h3>
                </div>
                <div class="text-sm text-gray-500">
                    <span id="tables-count">جاري التحميل...</span>
                </div>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="text-center py-12">
                <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white transition ease-in-out duration-150">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    جاري تحميل الطاولات...
                </div>
            </div>

            <!-- Tables Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="tables-container">
                <!-- Tables will be loaded here -->
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="text-center py-12 hidden">
                <div class="mx-auto h-24 w-24 text-gray-300">
                    <i class="mdi mdi-table-furniture text-6xl"></i>
                </div>
                <h3 class="mt-4 text-lg font-medium text-gray-900">لا توجد طاولات</h3>
                <p class="mt-2 text-sm text-gray-500">ابدأ بإضافة طاولة جديدة لمطعمك</p>
                <div class="mt-6">
                    <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200" onclick="$('#add-table-btn').click()">
                        <i class="mdi mdi-plus text-lg ml-2"></i>
                        إضافة طاولة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Table Modal -->
<div id="addTableModalBackdrop" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="addTableModal" class="relative top-5 mx-auto p-0 border-0 w-11/12 md:w-2/3 lg:w-1/2 xl:w-2/5 shadow-2xl rounded-2xl bg-white hidden max-h-[95vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="bg-blue-50 px-6 py-4 border-b border-blue-100 rounded-t-2xl sticky top-0 z-10">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-2 rounded-lg ml-3">
                        <i class="mdi mdi-table-furniture text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">إضافة طاولة جديدة</h3>
                        <p class="text-sm text-gray-500">أدخل تفاصيل الطاولة الجديدة</p>
                    </div>
                </div>
                <button type="button" class="text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-2 rounded-lg transition-colors" onclick="hideModal('addTableModal')">
                    <i class="mdi mdi-close text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Modal Body -->
        <form id="addTableForm" class="p-6 space-y-6">
            <!-- Basic Information Section -->
            <div class="bg-gray-50 rounded-xl p-5">
                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="mdi mdi-information-outline text-gray-500 text-lg ml-2"></i>
                    المعلومات الأساسية
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <label for="table_number" class="block text-sm font-medium text-gray-700">رقم الطاولة <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="table_number" name="table_number" placeholder="مثال: T001" required maxlength="20">
                    </div>
                    <div class="space-y-2">
                        <label for="table_name" class="block text-sm font-medium text-gray-700">اسم الطاولة</label>
                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="table_name" name="table_name" placeholder="مثال: طاولة النافذة" maxlength="255">
                    </div>
                    <div class="space-y-2">
                        <label for="area_id" class="block text-sm font-medium text-gray-700">المنطقة <span class="text-red-500">*</span></label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="area_id" name="area_id" required>
                            <option value="">اختر المنطقة</option>
                        </select>
                    </div>
                    <div class="space-y-2">
                        <label for="seating_capacity" class="block text-sm font-medium text-gray-700">السعة <span class="text-red-500">*</span></label>
                        <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="seating_capacity" name="seating_capacity" placeholder="عدد الأشخاص" required min="1" max="50">
                    </div>
                </div>
            </div>

            <!-- Additional Details Section -->
            <div class="bg-gray-50 rounded-xl p-5">
                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="mdi mdi-cog-outline text-gray-500 text-lg ml-2"></i>
                    التفاصيل الإضافية
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <label for="section" class="block text-sm font-medium text-gray-700">القسم</label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="section" name="section">
                            <option value="">اختر القسم</option>
                            <option value="Indoor">داخلي</option>
                            <option value="Outdoor">خارجي</option>
                            <option value="VIP">VIP</option>
                            <option value="Terrace">تراس</option>
                            <option value="Garden">حديقة</option>
                        </select>
                    </div>
                    <div class="space-y-2">
                        <label for="status" class="block text-sm font-medium text-gray-700">الحالة <span class="text-red-500">*</span></label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="status" name="status" required>
                            <option value="available">متاحة</option>
                            <option value="occupied">مشغولة</option>
                            <option value="reserved">محجوزة</option>
                            <option value="cleaning">تنظيف</option>
                            <option value="out_of_order">خارج الخدمة</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Position & Settings Section -->
            <div class="bg-gray-50 rounded-xl p-5">
                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="mdi mdi-map-marker-outline text-gray-500 text-lg ml-2"></i>
                    الموقع والإعدادات
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="space-y-2">
                        <label for="position_x" class="block text-sm font-medium text-gray-700">موقع X (اختياري)</label>
                        <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="position_x" name="position_x" step="0.01" placeholder="إحداثي X">
                    </div>
                    <div class="space-y-2">
                        <label for="position_y" class="block text-sm font-medium text-gray-700">موقع Y (اختياري)</label>
                        <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="position_y" name="position_y" step="0.01" placeholder="إحداثي Y">
                    </div>
                </div>
                <div class="space-y-2 mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700">ملاحظات</label>
                    <textarea class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية حول الطاولة"></textarea>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="is_active" name="is_active" checked>
                    <label class="mr-3 block text-sm text-gray-900" for="is_active">
                        نشطة
                    </label>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="bg-blue-50 rounded-xl p-5 border border-blue-100">
                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="mdi mdi-qrcode text-blue-600 text-lg ml-2"></i>
                    إعدادات QR Code
                </h4>
                <div class="mb-4">
                    <div class="flex items-center">
                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="generate_qr" name="generate_qr">
                        <label class="mr-3 block text-sm text-gray-900" for="generate_qr">
                            إنشاء QR Code للطاولة
                        </label>
                    </div>
                </div>

                    <div id="qr-options" style="display: none;">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">نوع QR Code:</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="radio" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" id="auto_qr" name="qr_type" value="auto" checked>
                                    <label class="mr-2 block text-sm text-gray-900" for="auto_qr">
                                        إنشاء تلقائي
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" id="custom_qr" name="qr_type" value="custom">
                                    <label class="mr-2 block text-sm text-gray-900" for="custom_qr">
                                        QR Code مخصص
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4" id="custom-qr-input" style="display: none;">
                            <label for="custom_qr_code" class="block text-sm font-medium text-gray-700 mb-2">QR Code مخصص:</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="custom_qr_code" name="custom_qr_code" maxlength="255">
                            <small class="text-sm text-gray-500">أدخل QR Code مخصص للطاولة</small>
                        </div>
                    </div>

                    <!-- QR Code Preview -->
                    <div id="qr-preview" class="text-center mt-6" style="display: none;">
                        <h6 class="text-base font-medium text-gray-900 mb-4">معاينة QR Code:</h6>
                        <div id="qr-image"></div>
                        <p id="qr-url" class="mt-2 text-sm text-gray-500"></p>
                    </div>
            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('addTableModal')">
                    إلغاء
                </button>
                <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" id="save-table">
                    <i class="fas fa-plus mr-2"></i>إضافة الطاولة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Manual QR Code Setting Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="manualQrModal" tabindex="-1" role="dialog" aria-labelledby="manualQrModalLabel" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>
        <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-900" id="manualQrModalLabel">تعيين QR Code يدوياً</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-2xl">&times;</span>
                </button>
            </div>
            <div class="pt-6">
                <form id="manualQrForm">
                    <div class="mb-4">
                        <label for="manual-qr-code" class="block text-sm font-medium text-gray-700 mb-2">QR Code:</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="manual-qr-code" name="qr_code" required>
                        <small class="text-sm text-gray-500">أدخل QR Code مخصص للطاولة</small>
                    </div>
                    <div id="manual-qr-preview" class="text-center mt-6" style="display: none;">
                        <h6 class="text-base font-medium text-gray-900 mb-4">معاينة QR Code:</h6>
                        <div id="manual-qr-image"></div>
                    </div>
                </form>
            </div>
            <div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="preview-manual-qr">معاينة</button>
                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="save-manual-qr">حفظ</button>
                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Display Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="qrModal" tabindex="-1" role="dialog" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>
        <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-900" id="qrModalLabel">QR Code</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-2xl">&times;</span>
                </button>
            </div>
            <div class="pt-6 text-center">
                <div id="qr-table-info" class="mb-4"></div>
                <div id="qr-code-container" class="mb-4"></div>
                <div id="qr-url-info" class="mb-4"></div>
            </div>
            <div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="download-qr" onclick="downloadQR($(this).data('qr-url'), $(this).data('filename'))">تحميل QR Code</button>
                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Table Modal -->
<div id="editTableModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="editTableModal" class="relative top-10 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">تعديل الطاولة</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('editTableModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="editTableForm" class="p-6">
            @csrf
            @method('PUT')
            <input type="hidden" id="edit-table-id" name="id">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="edit-table-number" class="block text-sm font-medium text-gray-700 mb-2">رقم الطاولة *</label>
                    <input type="text" id="edit-table-number" name="table_number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required maxlength="20">
                </div>

                <div>
                    <label for="edit-table-name" class="block text-sm font-medium text-gray-700 mb-2">اسم الطاولة</label>
                    <input type="text" id="edit-table-name" name="table_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" maxlength="255">
                </div>

                <div>
                    <label for="edit-area-id" class="block text-sm font-medium text-gray-700 mb-2">المنطقة *</label>
                    <select id="edit-area-id" name="area_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">اختر المنطقة</option>
                    </select>
                </div>

                <div>
                    <label for="edit-seating-capacity" class="block text-sm font-medium text-gray-700 mb-2">السعة *</label>
                    <input type="number" id="edit-seating-capacity" name="seating_capacity" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required min="1" max="50">
                </div>

                <div>
                    <label for="edit-section" class="block text-sm font-medium text-gray-700 mb-2">القسم</label>
                    <select id="edit-section" name="section" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">اختر القسم</option>
                        <option value="Indoor">داخلي</option>
                        <option value="Outdoor">خارجي</option>
                        <option value="VIP">VIP</option>
                        <option value="Terrace">تراس</option>
                        <option value="Garden">حديقة</option>
                    </select>
                </div>

                <div>
                    <label for="edit-status" class="block text-sm font-medium text-gray-700 mb-2">الحالة *</label>
                    <select id="edit-status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="available">متاحة</option>
                        <option value="occupied">مشغولة</option>
                        <option value="reserved">محجوزة</option>
                        <option value="cleaning">تنظيف</option>
                        <option value="out_of_order">خارج الخدمة</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                    <label for="edit-position-x" class="block text-sm font-medium text-gray-700 mb-2">موقع X (اختياري)</label>
                    <input type="number" id="edit-position-x" name="position_x" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" step="0.01" placeholder="إحداثي X">
                </div>

                <div>
                    <label for="edit-position-y" class="block text-sm font-medium text-gray-700 mb-2">موقع Y (اختياري)</label>
                    <input type="number" id="edit-position-y" name="position_y" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" step="0.01" placeholder="إحداثي Y">
                </div>
            </div>

            <div class="mt-4">
                <label for="edit-notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                <textarea id="edit-notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="أي ملاحظات إضافية حول الطاولة"></textarea>
            </div>

            <div class="mt-4">
                <label class="flex items-center">
                    <input type="checkbox" id="edit-is-active" name="is_active" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="mr-2 text-sm text-gray-700">نشطة</span>
                </label>
            </div>

            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('editTableModal')">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-yellow-600 border border-transparent rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                    <i class="fas fa-save mr-2"></i>حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Table Modal -->
<div id="viewTableModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="viewTableModal" class="relative top-10 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">تفاصيل الطاولة</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('viewTableModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="p-6">
            <div id="table-details" class="space-y-4">
                <!-- Table details will be loaded here -->
            </div>

            <!-- QR Code Section -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="text-md font-semibold text-gray-900 mb-4">QR Code</h4>
                <div class="text-center">
                    <div id="view-qr-container" class="inline-block p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <!-- QR Code will be displayed here -->
                    </div>
                    <div id="view-qr-actions" class="mt-4 flex justify-center gap-2">
                        <!-- QR Actions will be added here -->
                    </div>
                </div>
            </div>

            <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('viewTableModal')">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteTableModalBackdrop" class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div id="deleteTableModal" class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/3 lg:w-1/4 shadow-lg rounded-md bg-white hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">تأكيد الحذف</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('deleteTableModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">هل أنت متأكد؟</h3>
                <p class="text-sm text-gray-500 mb-4">
                    سيتم حذف هذه الطاولة نهائياً ولا يمكن التراجع عن هذا الإجراء.
                </p>
                <div id="delete-table-info" class="bg-gray-50 p-3 rounded-md mb-4">
                    <!-- Table info will be displayed here -->
                </div>
            </div>

            <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="hideModal('deleteTableModal')">
                    إلغاء
                </button>
                <button type="button" id="confirm-delete-table" class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                    <i class="fas fa-trash mr-2"></i>حذف نهائياً
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<!-- DataTables JS from CDN -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap4.min.js"></script>
<!-- Select2 JS from CDN -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert JS from CDN -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert@2.1.2/dist/sweetalert.min.js"></script>

<script>
// Tailwind Modal Functions
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    const backdrop = document.getElementById(modalId + 'Backdrop');
    if (modal && backdrop) {
        modal.classList.remove('hidden');
        modal.classList.add('show');
        backdrop.classList.remove('hidden');
        backdrop.classList.add('show');
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    const backdrop = document.getElementById(modalId + 'Backdrop');
    if (modal && backdrop) {
        modal.classList.add('hidden');
        modal.classList.remove('show');
        backdrop.classList.add('hidden');
        backdrop.classList.remove('show');
    }
}

// Close modal when clicking backdrop
function setupModalBackdropClose() {
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
        backdrop.addEventListener('click', function(e) {
            if (e.target === this) {
                const modalId = this.id.replace('Backdrop', '');
                hideModal(modalId);
            }
        });
    });
}

$(document).ready(function() {
    let tablesTable;

    // Setup modal backdrop close functionality
    setupModalBackdropClose();

    // Initialize
    loadTables();
    loadAreas(); // Load areas for the dropdown

    // Add Table Button Click Event
    $('#add-table-btn').click(function() {
        $('#addTableForm')[0].reset();
        showModal('addTableModal');
    });

    // Save Table Button Click Event
    $('#save-table').click(function() {
        let formData = {
            table_number: $('#table_number').val(),
            table_name: $('#table_name').val(),
            area_id: $('#area_id').val(),
            seating_capacity: $('#seating_capacity').val(),
            section: $('#section').val(),
            status: $('#status').val(),
            notes: $('#notes').val(),
            is_active: $('#is_active').is(':checked') ? 1 : 0,
            _token: '{{ csrf_token() }}'
        };

        // Handle position coordinates
        let positionX = $('#position_x').val();
        let positionY = $('#position_y').val();
        if (positionX && positionY) {
            formData.position_coordinates = JSON.stringify({
                x: parseFloat(positionX),
                y: parseFloat(positionY)
            });
        }

        // Handle QR code generation
        formData.generate_qr = $('#generate_qr').is(':checked') ? 1 : 0;

        if (formData.generate_qr) {
            formData.qr_type = $('input[name="qr_type"]:checked').val();
            if (formData.qr_type === 'custom') {
                formData.custom_qr_code = $('#custom_qr_code').val();
            }
        }

        // Validate required fields
        if (!formData.table_number || !formData.area_id || !formData.seating_capacity || !formData.status) {
            swal('تنبيه!', 'يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // Validate custom QR code if selected
        if (formData.generate_qr && $('input[name="qr_type"]:checked').val() === 'custom' && !formData.custom_qr_code) {
            swal('تنبيه!', 'يرجى إدخال QR Code مخصص', 'warning');
            return;
        }

        $.post('{{ route("tables.store") }}', formData, function(data) {
            if (data.success) {
                swal('نجح!', data.message, 'success');
                hideModal('addTableModal');

                // Show QR code if generated
                if (data.qr_data) {
                    setTimeout(() => {
                        showQRResult(data.qr_data);
                    }, 500);
                }

                loadTables();
            }
        }).fail(function(xhr) {
            let message = 'حدث خطأ أثناء إضافة الطاولة';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                let errors = xhr.responseJSON.errors;
                message = Object.values(errors).flat().join('\n');
            }
            swal('خطأ!', message, 'error');
        });
    });

    // Edit Table Form Submit
    $('#editTableForm').submit(function(e) {
        e.preventDefault();

        let tableId = $('#edit-table-id').val();
        let formData = {
            table_number: $('#edit-table-number').val(),
            table_name: $('#edit-table-name').val(),
            area_id: $('#edit-area-id').val(),
            seating_capacity: $('#edit-seating-capacity').val(),
            section: $('#edit-section').val(),
            status: $('#edit-status').val(),
            notes: $('#edit-notes').val(),
            is_active: $('#edit-is-active').is(':checked') ? 1 : 0,
            _token: '{{ csrf_token() }}',
            _method: 'PUT'
        };

        // Handle position coordinates
        let positionX = $('#edit-position-x').val();
        let positionY = $('#edit-position-y').val();
        if (positionX && positionY) {
            formData.position_coordinates = JSON.stringify({
                x: parseFloat(positionX),
                y: parseFloat(positionY)
            });
        }

        // Validate required fields
        if (!formData.table_number || !formData.area_id || !formData.seating_capacity || !formData.status) {
            swal('تنبيه!', 'يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        $.ajax({
            url: '{{ route("tables.update", ":id") }}'.replace(':id', tableId),
            method: 'PUT',
            data: formData,
            success: function(data) {
                if (data.success) {
                    swal('نجح!', data.message, 'success');
                    hideModal('editTableModal');
                    loadTables();
                }
            },
            error: function(xhr) {
                let message = 'حدث خطأ أثناء تحديث الطاولة';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    let errors = xhr.responseJSON.errors;
                    message = Object.values(errors).flat().join('\n');
                }
                swal('خطأ!', message, 'error');
            }
        });
    });

    // Delete Table Confirmation
    $('#confirm-delete-table').click(function() {
        let tableId = $(this).data('table-id');

        $.ajax({
            url: '{{ route("tables.destroy", ":id") }}'.replace(':id', tableId),
            method: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(data) {
                if (data.success) {
                    swal('تم الحذف!', data.message, 'success');
                    hideModal('deleteTableModal');
                    loadTables();
                }
            },
            error: function(xhr) {
                let message = 'حدث خطأ أثناء حذف الطاولة';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                swal('خطأ!', message, 'error');
            }
        });
    });

    // QR Code functionality
    $('#generate_qr').change(function() {
        if ($(this).is(':checked')) {
            $('#qr-options').show();
        } else {
            $('#qr-options').hide();
            $('#qr-preview').hide();
        }
    });

    $('input[name="qr_type"]').change(function() {
        if ($(this).val() === 'custom') {
            $('#custom-qr-input').show();
        } else {
            $('#custom-qr-input').hide();
        }
        $('#qr-preview').hide();
    });

    // Preview QR code
    $('#custom_qr_code').on('input', function() {
        let qrCode = $(this).val().trim();
        if (qrCode && $('input[name="qr_type"]:checked').val() === 'custom') {
            previewQRCode(qrCode);
        } else {
            $('#qr-preview').hide();
        }
    });

    function previewQRCode(qrCode) {
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug();
        let qrUrl = `${baseUrl}/restaurant/table/${qrCode}?hash=${tenantSlug}`;
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrUrl)}`;
        
        $('#qr-image').html(`<img src="${qrImageUrl}" alt="QR Code Preview" style="width: 150px; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; background: white;">`);
        $('#qr-url').text(qrUrl);
        $('#qr-preview').show();
    }

    function showQRResult(qrData) {
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData.url)}`;
        
        swal({
            title: 'تم إنشاء QR Code بنجاح!',
            content: {
                element: 'div',
                attributes: {
                    innerHTML: `
                        <div class="text-center">
                            <img src="${qrImageUrl}" alt="QR Code" style="width: 200px; height: 200px; border: 1px solid #ddd; border-radius: 4px; padding: 10px; background: white; margin: 10px 0;">
                            <p><strong>QR Code:</strong> ${qrData.qr_code}</p>
                            <p><strong>الرابط:</strong></p>
                            <p style="word-break: break-all; font-size: 12px;">${qrData.url}</p>
                        </div>
                    `
                }
            },
            buttons: {
                download: {
                    text: 'تحميل QR Code',
                    value: 'download',
                    className: 'btn-success'
                },
                copy: {
                    text: 'نسخ الرابط',
                    value: 'copy',
                    className: 'btn-primary'
                },
                close: {
                    text: 'إغلاق',
                    value: 'close',
                    className: 'btn-secondary'
                }
            }
        }).then((value) => {
            if (value === 'download') {
                downloadQR(qrImageUrl, `table-${qrData.table_number}-qr.png`);
            } else if (value === 'copy') {
                navigator.clipboard.writeText(qrData.url).then(() => {
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    let textArea = document.createElement('textarea');
                    textArea.value = qrData.url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                });
            }
        });
    }

    // Load areas for dropdown
    function loadAreas() {
        $.get('{{ route("reservation.areas.list") }}', function(data) {
            let areaSelects = ['#area_id', '#edit-area-id'];
            let areaFilter = $('#area-filter');

            areaSelects.forEach(function(selector) {
                $(selector).empty().append('<option value="">اختر المنطقة</option>');
                if (data && data.length > 0) {
                    data.forEach(function(area) {
                        $(selector).append(`<option value="${area.id}">${area.name}</option>`);
                    });
                }
            });

            areaFilter.empty().append('<option value="">جميع المناطق</option>');
            if (data && data.length > 0) {
                data.forEach(function(area) {
                    areaFilter.append(`<option value="${area.id}">${area.name}</option>`);
                });
            }
        }).fail(function() {
            console.log('Failed to load areas');
        });
    }

    // Load tables
    function loadTables() {

        let filters = {
            area_id: $('#area-filter').val(),
            status: $('#status-filter').val(),
            capacity: $('#capacity-filter').val()
        };

        // Using the controller route
        $.get('{{ route("reservation.tables.datatable") }}', filters, function(data) {
            renderTablesCards(data.data || data);
        }).fail(function() {
            // Fallback to mock data for testing
            renderTablesCards([
                {
                    id: 1,
                    table_number: 'T001',
                    table_name: 'طاولة رقم 1',
                    seating_capacity: 4,
                    status: 'available',
                    area_name: 'الصالة الرئيسية',
                    qr_code: '436a1f405d08a1a57030802e561811ae'
                },
                {
                    id: 2,
                    table_number: 'T002',
                    table_name: 'طاولة رقم 2',
                    seating_capacity: 6,
                    status: 'occupied',
                    area_name: 'الصالة الرئيسية',
                    qr_code: '536a1f405d08a1a57030802e561811af'
                }
            ]);
        });
    }

    // Render tables as cards
    function renderTablesCards(tables) {
        let container = $('#tables-container');
        container.empty();

        if (!tables || tables.length === 0) {
            container.append('<div class="col-span-full"><p class="text-center text-gray-500 py-8">لا توجد طاولات</p></div>');
            return;
        }

        tables.forEach(function(table) {
            let statusClass = `status-${table.status}`;
            let statusText = getStatusText(table.status);
            
            let qrCodeUrl = generateTableUrl(table);
            let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrCodeUrl)}`;
            
            let card = `
                <div class="group">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-blue-300">
                        <!-- Table Header -->
                        <div class="flex justify-between items-start mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-bold text-gray-800 mb-1">${table.table_name || table.table_number}</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    طاولة ${table.table_number}
                                </span>
                            </div>
                            <span class="table-status ${statusClass} text-xs font-semibold px-3 py-1.5 rounded-full">${statusText}</span>
                        </div>
                        
                        <!-- QR Code Section -->
                        <div class="text-center my-6">
                            <div class="inline-block p-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 group-hover:border-blue-300 transition-colors duration-300">
                                <img src="${qrImageUrl}" alt="QR Code" class="w-32 h-32 mx-auto rounded-lg shadow-sm bg-white border border-gray-200">
                            </div>
                        </div>
                        
                        <!-- Table Info -->
                        <div class="flex justify-between items-center py-3 border-t border-gray-100">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-users text-blue-500 ml-2"></i>
                                <span class="text-sm font-medium">${table.seating_capacity} أشخاص</span>
                            </div>
                            <span class="text-sm text-gray-500">${table.area_name || 'غير محدد'}</span>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="grid grid-cols-2 gap-2 mt-4">
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1" onclick="viewTable(${table.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye ml-1"></i>
                                عرض
                            </button>
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-yellow-700 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 hover:border-yellow-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-1" onclick="editTable(${table.id})" title="تعديل الطاولة">
                                <i class="fas fa-edit ml-1"></i>
                                تعديل
                            </button>
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 hover:border-green-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1" onclick="downloadQR('${qrImageUrl}', 'table-${table.table_number}-qr.png')" title="تحميل QR">
                                <i class="fas fa-download ml-1"></i>
                                تحميل
                            </button>
                            <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-red-700 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 hover:border-red-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1" onclick="deleteTable(${table.id})" title="حذف الطاولة">
                                <i class="fas fa-trash ml-1"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.append(card);
        });
    }

    // Generate table URL
    function generateTableUrl(table) {
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug(); // Get from global or config
        return `${baseUrl}/restaurant/table/${table.qr_code || table.id}?hash=${tenantSlug}`;
    }

    // Get tenant slug (this should be available globally or from config)
    function getTenantSlug() {
        // Get tenant slug from authenticated user's branch tenant
        @if(auth()->user() && auth()->user()->branch && auth()->user()->branch->tenant)
            @php
                $tenant = auth()->user()->branch->tenant;
                $tenantSlug = $tenant->code ?? strtolower(str_replace([' ', '-', '_'], '', $tenant->name));
            @endphp
            return '{{ $tenantSlug }}';
        @else
            return 'default-tenant'; // Default fallback
        @endif
    }

    // Generate QR Code (removed - now using QR Server API)

    // Show table URL in a modal or alert
    window.showTableUrl = function(url) {
        swal({
            title: 'رابط الطاولة',
            text: url,
            icon: 'info',
            buttons: {
                copy: {
                    text: 'نسخ الرابط',
                    value: 'copy',
                    className: 'btn-primary'
                },
                close: {
                    text: 'إغلاق',
                    value: 'close',
                    className: 'btn-secondary'
                }
            }
        }).then((value) => {
            if (value === 'copy') {
                // Copy to clipboard
                navigator.clipboard.writeText(url).then(() => {
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    let textArea = document.createElement('textarea');
                    textArea.value = url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                });
            }
        });
    };

    // Download QR code
    window.downloadQR = function(qrImageUrl, filename) {
        let link = document.createElement('a');
        link.href = qrImageUrl;
        link.download = filename || 'qr-code.png';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Get status text in Arabic
    function getStatusText(status) {
        const statusMap = {
            'available': 'متاحة',
            'occupied': 'مشغولة',
            'reserved': 'محجوزة',
            'cleaning': 'تنظيف',
            'out_of_order': 'خارج الخدمة'
        };
        return statusMap[status] || status;
    }



    // Filters
    $('#apply-filters').click(function() {
        loadTables();
    });

    $('#clear-filters').click(function() {
        $('#area-filter, #status-filter').val('');
        $('#capacity-filter').val('');
        loadTables();
    });

    // Generate new QR code
    window.generateNewQR = function(tableId) {
        if (confirm('هل أنت متأكد من إنشاء QR Code جديد لهذه الطاولة؟')) {
            $.post(`{{ url('tables') }}/${tableId}/regenerate-qr`, {
                _token: '{{ csrf_token() }}'
            }, function(data) {
                if (data.success) {
                    swal('نجح!', data.message, 'success');
                    loadTables();
                }
            }).fail(function(xhr) {
                let message = 'حدث خطأ أثناء إنشاء QR Code جديد';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                swal('خطأ!', message, 'error');
            });
        }
    };

    // Show manual QR modal
    window.showManualQRModal = function(tableId) {
        $('#manualQrModal').data('table-id', tableId);
        $('#manual-qr-code').val('');
        $('#manual-qr-preview').hide();
        $('#manualQrModal').removeClass('hidden');
    };

    // Preview manual QR code
    $('#preview-manual-qr').click(function() {
        let qrCode = $('#manual-qr-code').val().trim();
        if (!qrCode) {
            swal('تنبيه!', 'يرجى إدخال QR Code أولاً', 'warning');
            return;
        }

        let tableId = $('#manualQrModal').data('table-id');
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug();
        let qrUrl = `${baseUrl}/restaurant/table/${qrCode}?hash=${tenantSlug}`;

        // Generate QR code preview using QR Server API
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrUrl)}`;
        
        let container = document.getElementById('manual-qr-image');
        container.innerHTML = `<img src="${qrImageUrl}" alt="QR Code Preview" style="width: 150px; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; background: white;">`;
        $('#manual-qr-preview').show();
    });

    // Save manual QR code
    $('#save-manual-qr').click(function() {
        let qrCode = $('#manual-qr-code').val().trim();
        let tableId = $('#manualQrModal').data('table-id');
        
        if (!qrCode) {
            swal('تنبيه!', 'يرجى إدخال QR Code أولاً', 'warning');
            return;
        }

        $.post(`{{ url('tables') }}/${tableId}/set-manual-qr`, {
            _token: '{{ csrf_token() }}',
            qr_code: qrCode
        }, function(data) {
            if (data.success) {
                swal('نجح!', data.message, 'success');
                $('#manualQrModal').addClass('hidden');
                loadTables();
            }
        }).fail(function(xhr) {
            let message = 'حدث خطأ أثناء حفظ QR Code';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            swal('خطأ!', message, 'error');
        });
    });

    // Show QR Modal with larger image
    window.showQRModal = function(qrImageUrl, qrUrl, qrCode) {
        $('#qrModalLabel').text(`QR Code - ${qrCode}`);
        $('#qr-table-info').html(`<h6>QR Code: ${qrCode}</h6>`);
        $('#qr-url-info').html(`<small class="text-muted">${qrUrl}</small>`);
        
        // Display the QR image
        let container = document.getElementById('qr-code-container');
        container.innerHTML = `<img src="${qrImageUrl}" alt="QR Code" style="width: 200px; height: 200px;" class="img-thumbnail">`;
        
        // Store for download
        $('#download-qr').data('qr-url', qrImageUrl);
        $('#download-qr').data('filename', `qr-code-${qrCode}.png`);
        
        $('#qrModal').removeClass('hidden');
    };
});

// Global CRUD Functions
function viewTable(id) {
    $.get('{{ route("tables.show", ":id") }}'.replace(':id', id), function(data) {
        if (data.success) {
            let table = data.data;

            let statusLabels = {
                'available': 'متاحة',
                'occupied': 'مشغولة',
                'reserved': 'محجوزة',
                'cleaning': 'تنظيف',
                'out_of_order': 'خارج الخدمة'
            };

            let sectionLabels = {
                'Indoor': 'داخلي',
                'Outdoor': 'خارجي',
                'VIP': 'VIP',
                'Terrace': 'تراس',
                'Garden': 'حديقة'
            };

            let html = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div><strong>رقم الطاولة:</strong> ${table.table_number}</div>
                    <div><strong>اسم الطاولة:</strong> ${table.table_name || 'غير محدد'}</div>
                    <div><strong>المنطقة:</strong> ${table.area ? table.area.name : 'غير محدد'}</div>
                    <div><strong>السعة:</strong> ${table.seating_capacity} أشخاص</div>
                    <div><strong>القسم:</strong> ${table.section ? sectionLabels[table.section] || table.section : 'غير محدد'}</div>
                    <div><strong>الحالة:</strong> <span class="status-${table.status} px-2 py-1 rounded-full text-xs">${statusLabels[table.status] || table.status}</span></div>
                    <div><strong>نشطة:</strong> ${table.is_active ? 'نعم' : 'لا'}</div>
                    <div><strong>تاريخ الإنشاء:</strong> ${new Date(table.created_at).toLocaleString('ar-EG')}</div>
                </div>
                ${table.notes ? '<div class="mt-4"><strong>ملاحظات:</strong><br>' + table.notes + '</div>' : ''}
            `;

            $('#table-details').html(html);

            // Display QR Code
            if (table.qr_code) {
                let qrCodeUrl = generateTableUrl(table);
                let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCodeUrl)}`;

                $('#view-qr-container').html(`
                    <img src="${qrImageUrl}" alt="QR Code" class="w-48 h-48 mx-auto rounded-lg shadow-sm bg-white border border-gray-200">
                    <p class="mt-2 text-sm text-gray-600">QR Code: ${table.qr_code}</p>
                `);

                $('#view-qr-actions').html(`
                    <button class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700" onclick="showTableUrl('${qrCodeUrl}')">
                        <i class="fas fa-link mr-1"></i>عرض الرابط
                    </button>
                    <button class="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700" onclick="downloadQR('${qrImageUrl}', 'table-${table.table_number}-qr.png')">
                        <i class="fas fa-download mr-1"></i>تحميل
                    </button>
                `);
            } else {
                $('#view-qr-container').html('<p class="text-gray-500">لا يوجد QR Code</p>');
                $('#view-qr-actions').empty();
            }

            showModal('viewTableModal');
        }
    }).fail(function() {
        swal('خطأ!', 'حدث خطأ أثناء تحميل بيانات الطاولة', 'error');
    });
}

function editTable(id) {
    $.get('{{ route("tables.edit", ":id") }}'.replace(':id', id), function(data) {
        if (data.success) {
            let table = data.data;

            $('#edit-table-id').val(table.id);
            $('#edit-table-number').val(table.table_number);
            $('#edit-table-name').val(table.table_name);
            $('#edit-area-id').val(table.area_id);
            $('#edit-seating-capacity').val(table.seating_capacity);
            $('#edit-section').val(table.section);
            $('#edit-status').val(table.status);
            $('#edit-notes').val(table.notes);
            $('#edit-is-active').prop('checked', table.is_active);

            // Handle position coordinates
            if (table.position_coordinates) {
                try {
                    let coords = JSON.parse(table.position_coordinates);
                    $('#edit-position-x').val(coords.x);
                    $('#edit-position-y').val(coords.y);
                } catch (e) {
                    console.log('Error parsing position coordinates');
                }
            }

            showModal('editTableModal');
        }
    }).fail(function() {
        swal('خطأ!', 'حدث خطأ أثناء تحميل بيانات الطاولة', 'error');
    });
}

function deleteTable(id) {
    // First get table details
    $.get('{{ route("tables.show", ":id") }}'.replace(':id', id), function(data) {
        if (data.success) {
            let table = data.data;

            $('#delete-table-info').html(`
                <div class="text-sm">
                    <p><strong>رقم الطاولة:</strong> ${table.table_number}</p>
                    <p><strong>اسم الطاولة:</strong> ${table.table_name || 'غير محدد'}</p>
                    <p><strong>المنطقة:</strong> ${table.area ? table.area.name : 'غير محدد'}</p>
                </div>
            `);

            $('#confirm-delete-table').data('table-id', id);
            showModal('deleteTableModal');
        }
    }).fail(function() {
        swal('خطأ!', 'حدث خطأ أثناء تحميل بيانات الطاولة', 'error');
    });
}
</script>
@endpush