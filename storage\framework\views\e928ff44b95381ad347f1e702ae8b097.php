<?php $__env->startSection('title', 'KOT Details - ' . $kotOrder->kot_number); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .kot-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .status-badge {
        font-size: 0.875rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .priority-badge {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .info-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .item-card {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .time-indicator {
        font-size: 1.25rem;
        font-weight: 700;
    }
    
    .overdue {
        color: #dc2626;
    }
    
    .warning {
        color: #d97706;
    }
    
    .normal {
        color: #059669;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- KOT Header -->
    <div class="kot-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2"><?php echo e($kotOrder->kot_number); ?></h1>
                <p class="mb-0 opacity-75">Order #<?php echo e($kotOrder->order?->order_number ?? 'N/A'); ?></p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex flex-column align-items-md-end gap-2">
                    <?php
                        $statusClasses = [
                            'pending' => 'bg-warning text-dark',
                            'preparing' => 'bg-info text-white',
                            'ready' => 'bg-success text-white',
                            'completed' => 'bg-primary text-white',
                            'cancelled' => 'bg-danger text-white',
                        ];
                        $priorityClasses = [
                            'low' => 'bg-secondary text-white',
                            'normal' => 'bg-primary text-white',
                            'high' => 'bg-warning text-dark',
                            'urgent' => 'bg-danger text-white',
                        ];
                    ?>
                    <span class="status-badge <?php echo e($statusClasses[$kotOrder->status] ?? 'bg-secondary text-white'); ?>">
                        <?php echo e(ucfirst($kotOrder->status)); ?>

                    </span>
                    <span class="priority-badge <?php echo e($priorityClasses[$kotOrder->priority] ?? 'bg-secondary text-white'); ?>">
                        <?php echo e(ucfirst($kotOrder->priority)); ?> Priority
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- KOT Items -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-utensils text-primary me-2"></i>
                    Order Items (<?php echo e($kotOrder->kotOrderItems->count()); ?>)
                </h5>
                
                <?php $__empty_1 = true; $__currentLoopData = $kotOrder->kotOrderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="item-card">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1"><?php echo e($item->menuItem?->name ?? 'Unknown Item'); ?></h6>
                                <p class="text-muted mb-0 small">
                                    Quantity: <?php echo e($item->quantity); ?>

                                    <?php if($item->special_instructions): ?>
                                        <br><strong>Instructions:</strong> <?php echo e($item->special_instructions); ?>

                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-3">
                                <?php
                                    $itemStatusClasses = [
                                        'pending' => 'bg-warning text-dark',
                                        'preparing' => 'bg-info text-white',
                                        'ready' => 'bg-success text-white',
                                        'completed' => 'bg-primary text-white',
                                        'cancelled' => 'bg-danger text-white',
                                    ];
                                ?>
                                <span class="badge <?php echo e($itemStatusClasses[$item->status] ?? 'bg-secondary text-white'); ?>">
                                    <?php echo e(ucfirst($item->status)); ?>

                                </span>
                            </div>
                            <div class="col-md-3 text-end">
                                <?php if($item->prep_time_minutes): ?>
                                    <small class="text-muted">Prep: <?php echo e($item->prep_time_minutes); ?>m</small>
                                <?php endif; ?>
                                <?php if($item->started_at): ?>
                                    <br><small class="text-muted">Started: <?php echo e($item->started_at->format('H:i')); ?></small>
                                <?php endif; ?>
                                <?php if($item->completed_at): ?>
                                    <br><small class="text-muted">Completed: <?php echo e($item->completed_at->format('H:i')); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-utensils text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No items found</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Special Instructions -->
            <?php if($kotOrder->special_instructions): ?>
                <div class="info-card">
                    <h5 class="mb-3">
                        <i class="fas fa-sticky-note text-warning me-2"></i>
                        Special Instructions
                    </h5>
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo e($kotOrder->special_instructions); ?>

                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Time Information -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-clock text-info me-2"></i>
                    Time Information
                </h5>
                
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label small text-muted">Created</label>
                        <div class="fw-bold"><?php echo e($kotOrder->created_at->format('M d, Y H:i')); ?></div>
                    </div>
                    
                    <?php if($kotOrder->started_at): ?>
                        <div class="col-12">
                            <label class="form-label small text-muted">Started</label>
                            <div class="fw-bold"><?php echo e($kotOrder->started_at->format('M d, Y H:i')); ?></div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($kotOrder->completed_at): ?>
                        <div class="col-12">
                            <label class="form-label small text-muted">Completed</label>
                            <div class="fw-bold"><?php echo e($kotOrder->completed_at->format('M d, Y H:i')); ?></div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="col-12">
                        <label class="form-label small text-muted">Elapsed Time</label>
                        <?php
                            $elapsedMinutes = $kotOrder->getElapsedTimeMinutes();
                            $remainingMinutes = $kotOrder->getRemainingTimeMinutes();
                            $isOverdue = $kotOrder->isOverdue();
                            
                            $timeClass = 'normal';
                            if ($isOverdue) {
                                $timeClass = 'overdue';
                            } elseif ($remainingMinutes !== null && $remainingMinutes <= 5) {
                                $timeClass = 'warning';
                            }
                        ?>
                        <div class="time-indicator <?php echo e($timeClass); ?>">
                            <?php echo e($elapsedMinutes); ?> minutes
                            <?php if($isOverdue): ?>
                                <i class="fas fa-exclamation-triangle ms-1"></i>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if($kotOrder->estimated_prep_time_minutes && $kotOrder->status !== 'completed'): ?>
                        <div class="col-12">
                            <label class="form-label small text-muted">Remaining Time</label>
                            <div class="time-indicator <?php echo e($timeClass); ?>">
                                <?php if($remainingMinutes !== null): ?>
                                    <?php echo e(max(0, $remainingMinutes)); ?> minutes
                                <?php else: ?>
                                    N/A
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($kotOrder->actual_prep_time_minutes): ?>
                        <div class="col-12">
                            <label class="form-label small text-muted">Actual Prep Time</label>
                            <div class="fw-bold text-success"><?php echo e($kotOrder->actual_prep_time_minutes); ?> minutes</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Kitchen Information -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-kitchen-set text-success me-2"></i>
                    Kitchen Information
                </h5>
                
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label small text-muted">Kitchen</label>
                        <div class="fw-bold"><?php echo e($kotOrder->kitchen?->name ?? 'N/A'); ?></div>
                        <?php if($kotOrder->kitchen?->station_type): ?>
                            <small class="text-muted"><?php echo e(ucfirst($kotOrder->kitchen->station_type)); ?> Station</small>
                        <?php endif; ?>
                    </div>
                    
                    <?php if($kotOrder->assignedTo): ?>
                        <div class="col-12">
                            <label class="form-label small text-muted">Assigned To</label>
                            <div class="fw-bold"><?php echo e($kotOrder->assignedTo->name); ?></div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($kotOrder->creator): ?>
                        <div class="col-12">
                            <label class="form-label small text-muted">Created By</label>
                            <div class="fw-bold"><?php echo e($kotOrder->creator->name); ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Customer Information -->
            <?php if($kotOrder->order?->customer): ?>
                <div class="info-card">
                    <h5 class="mb-4">
                        <i class="fas fa-user text-primary me-2"></i>
                        Customer Information
                    </h5>
                    
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label small text-muted">Name</label>
                            <div class="fw-bold"><?php echo e($kotOrder->order->customer->name); ?></div>
                        </div>
                        
                        <?php if($kotOrder->order->customer->phone): ?>
                            <div class="col-12">
                                <label class="form-label small text-muted">Phone</label>
                                <div class="fw-bold"><?php echo e($kotOrder->order->customer->phone); ?></div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($kotOrder->order->table_number): ?>
                            <div class="col-12">
                                <label class="form-label small text-muted">Table</label>
                                <div class="fw-bold">Table <?php echo e($kotOrder->order->table_number); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Actions -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-cogs text-secondary me-2"></i>
                    Actions
                </h5>
                
                <div class="d-grid gap-2">
                    <?php echo $__env->make('kitchen::partials.kot-actions', ['kotOrder' => $kotOrder], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <a href="<?php echo e(route('kitchen.kot-orders.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to KOT List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Auto-refresh every 30 seconds if KOT is not completed
    <?php if($kotOrder->status !== 'completed' && $kotOrder->status !== 'cancelled'): ?>
        setInterval(function() {
            location.reload();
        }, 30000);
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Kitchen\Providers/../resources/views/kot-orders/show.blade.php ENDPATH**/ ?>