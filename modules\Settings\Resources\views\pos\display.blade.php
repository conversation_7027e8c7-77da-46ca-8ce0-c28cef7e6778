@extends('layouts.master')

@section('title', 'POS Display Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-desktop text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">POS Display Settings</h1>
                        <p class="text-purple-100">Configure POS interface appearance and layout</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-purple-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-purple-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-purple-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-purple-200 mx-2"></i>
                                <span class="text-white font-medium">POS Display</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Display Configuration -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Display Configuration</h3>
                <p class="text-gray-600 mt-1">Configure POS interface appearance and behavior</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <!-- Theme Selection -->
                <div>
                    <label for="pos_theme" class="block text-sm font-medium text-gray-700 mb-2">
                        POS Theme
                    </label>
                    <select id="pos_theme" name="pos_theme" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="light" {{ (isset($displaySettings['pos_theme']) && $displaySettings['pos_theme']->value == 'light') ? 'selected' : '' }}>Light Theme</option>
                        <option value="dark" {{ (isset($displaySettings['pos_theme']) && $displaySettings['pos_theme']->value == 'dark') ? 'selected' : '' }}>Dark Theme</option>
                        <option value="blue" {{ (isset($displaySettings['pos_theme']) && $displaySettings['pos_theme']->value == 'blue') ? 'selected' : '' }}>Blue Theme</option>
                        <option value="green" {{ (isset($displaySettings['pos_theme']) && $displaySettings['pos_theme']->value == 'green') ? 'selected' : '' }}>Green Theme</option>
                    </select>
                </div>

                <!-- Grid Columns -->
                <div>
                    <label for="grid_columns" class="block text-sm font-medium text-gray-700 mb-2">
                        Menu Grid Columns
                    </label>
                    <select id="grid_columns" name="grid_columns" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="2" {{ (isset($displaySettings['grid_columns']) && $displaySettings['grid_columns']->value == '2') ? 'selected' : '' }}>2 Columns</option>
                        <option value="3" {{ (isset($displaySettings['grid_columns']) && $displaySettings['grid_columns']->value == '3') ? 'selected' : '' }}>3 Columns</option>
                        <option value="4" {{ (isset($displaySettings['grid_columns']) && $displaySettings['grid_columns']->value == '4') ? 'selected' : '' }}>4 Columns</option>
                        <option value="5" {{ (isset($displaySettings['grid_columns']) && $displaySettings['grid_columns']->value == '5') ? 'selected' : '' }}>5 Columns</option>
                        <option value="6" {{ (isset($displaySettings['grid_columns']) && $displaySettings['grid_columns']->value == '6') ? 'selected' : '' }}>6 Columns</option>
                    </select>
                </div>

                <!-- Font Size -->
                <div>
                    <label for="font_size" class="block text-sm font-medium text-gray-700 mb-2">
                        Font Size
                    </label>
                    <select id="font_size" name="font_size" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="small" {{ (isset($displaySettings['font_size']) && $displaySettings['font_size']->value == 'small') ? 'selected' : '' }}>Small</option>
                        <option value="medium" {{ (isset($displaySettings['font_size']) && $displaySettings['font_size']->value == 'medium') ? 'selected' : '' }}>Medium</option>
                        <option value="large" {{ (isset($displaySettings['font_size']) && $displaySettings['font_size']->value == 'large') ? 'selected' : '' }}>Large</option>
                        <option value="extra-large" {{ (isset($displaySettings['font_size']) && $displaySettings['font_size']->value == 'extra-large') ? 'selected' : '' }}>Extra Large</option>
                    </select>
                </div>

                <!-- Display Options -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Display Options</h4>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="show_images" name="show_images" value="1" 
                                   {{ (isset($displaySettings['show_images']) && $displaySettings['show_images']->value == '1') ? 'checked' : '' }}
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="show_images" class="ml-2 text-sm text-gray-700">
                                Show menu item images
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="show_prices" name="show_prices" value="1" checked
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="show_prices" class="ml-2 text-sm text-gray-700">
                                Show prices on menu items
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="show_categories" name="show_categories" value="1" checked
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="show_categories" class="ml-2 text-sm text-gray-700">
                                Show category tabs
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="show_search" name="show_search" value="1" checked
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="show_search" class="ml-2 text-sm text-gray-700">
                                Show search bar
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Button Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Button Settings</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="button_size" class="block text-sm font-medium text-gray-700 mb-2">
                                Button Size
                            </label>
                            <select id="button_size" name="button_size" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="small">Small</option>
                                <option value="medium" selected>Medium</option>
                                <option value="large">Large</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="button_style" class="block text-sm font-medium text-gray-700 mb-2">
                                Button Style
                            </label>
                            <select id="button_style" name="button_style" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="rounded">Rounded</option>
                                <option value="square" selected>Square</option>
                                <option value="pill">Pill</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" class="preview-btn px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        Preview Changes
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">POS Interface Preview</h3>
                <p class="text-gray-600 mt-1">Preview how your POS interface will look</p>
            </div>

            <div class="p-6">
                <div id="pos-preview" class="bg-gray-100 rounded-lg p-4 min-h-96">
                    <!-- POS Preview Content -->
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-lg font-semibold">Menu Categories</h4>
                        <div class="flex space-x-2">
                            <input type="text" placeholder="Search items..." class="px-3 py-1 border rounded text-sm">
                        </div>
                    </div>

                    <!-- Category Tabs -->
                    <div class="flex space-x-2 mb-4">
                        <button class="px-3 py-1 bg-purple-600 text-white rounded text-sm">All</button>
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">Burgers</button>
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">Drinks</button>
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">Desserts</button>
                    </div>

                    <!-- Menu Items Grid -->
                    <div id="menu-grid" class="grid grid-cols-3 gap-3">
                        <!-- Sample menu items -->
                        <div class="bg-white rounded-lg p-3 shadow-sm border">
                            <div class="w-full h-16 bg-gray-200 rounded mb-2 flex items-center justify-center">
                                <i class="fas fa-hamburger text-gray-400"></i>
                            </div>
                            <h5 class="font-medium text-sm">Burger</h5>
                            <p class="text-purple-600 font-bold text-sm">$12.99</p>
                        </div>
                        
                        <div class="bg-white rounded-lg p-3 shadow-sm border">
                            <div class="w-full h-16 bg-gray-200 rounded mb-2 flex items-center justify-center">
                                <i class="fas fa-pizza-slice text-gray-400"></i>
                            </div>
                            <h5 class="font-medium text-sm">Pizza</h5>
                            <p class="text-purple-600 font-bold text-sm">$15.99</p>
                        </div>
                        
                        <div class="bg-white rounded-lg p-3 shadow-sm border">
                            <div class="w-full h-16 bg-gray-200 rounded mb-2 flex items-center justify-center">
                                <i class="fas fa-coffee text-gray-400"></i>
                            </div>
                            <h5 class="font-medium text-sm">Coffee</h5>
                            <p class="text-purple-600 font-bold text-sm">$4.99</p>
                        </div>
                        
                        <div class="bg-white rounded-lg p-3 shadow-sm border">
                            <div class="w-full h-16 bg-gray-200 rounded mb-2 flex items-center justify-center">
                                <i class="fas fa-ice-cream text-gray-400"></i>
                            </div>
                            <h5 class="font-medium text-sm">Ice Cream</h5>
                            <p class="text-purple-600 font-bold text-sm">$6.99</p>
                        </div>
                        
                        <div class="bg-white rounded-lg p-3 shadow-sm border">
                            <div class="w-full h-16 bg-gray-200 rounded mb-2 flex items-center justify-center">
                                <i class="fas fa-glass-whiskey text-gray-400"></i>
                            </div>
                            <h5 class="font-medium text-sm">Juice</h5>
                            <p class="text-purple-600 font-bold text-sm">$3.99</p>
                        </div>
                        
                        <div class="bg-white rounded-lg p-3 shadow-sm border">
                            <div class="w-full h-16 bg-gray-200 rounded mb-2 flex items-center justify-center">
                                <i class="fas fa-cookie-bite text-gray-400"></i>
                            </div>
                            <h5 class="font-medium text-sm">Cookie</h5>
                            <p class="text-purple-600 font-bold text-sm">$2.99</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Update preview when settings change
    function updatePreview() {
        const columns = $('#grid_columns').val();
        const theme = $('#pos_theme').val();
        const showImages = $('#show_images').is(':checked');
        const showPrices = $('#show_prices').is(':checked');
        const fontSize = $('#font_size').val();
        
        // Update grid columns
        $('#menu-grid').removeClass('grid-cols-2 grid-cols-3 grid-cols-4 grid-cols-5 grid-cols-6')
                      .addClass(`grid-cols-${columns}`);
        
        // Update theme colors
        const themeColors = {
            'light': 'bg-gray-100',
            'dark': 'bg-gray-800',
            'blue': 'bg-blue-50',
            'green': 'bg-green-50'
        };
        
        $('#pos-preview').removeClass('bg-gray-100 bg-gray-800 bg-blue-50 bg-green-50')
                        .addClass(themeColors[theme] || 'bg-gray-100');
        
        // Show/hide images
        if (!showImages) {
            $('#menu-grid .fas').hide();
        } else {
            $('#menu-grid .fas').show();
        }
        
        // Show/hide prices
        if (!showPrices) {
            $('#menu-grid .text-purple-600').hide();
        } else {
            $('#menu-grid .text-purple-600').show();
        }
    }

    // Bind change events
    $('#grid_columns, #pos_theme, #show_images, #show_prices, #font_size').on('change', updatePreview);

    // Preview button
    $('.preview-btn').on('click', function() {
        updatePreview();
        Swal.fire({
            icon: 'info',
            title: 'Preview Updated',
            text: 'The preview has been updated with your current settings.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Form submission handler
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        // Add loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Display settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });

    // Initialize preview
    updatePreview();
});
</script>
@endpush
