@extends('layouts.master')

@section('title', 'Restaurant Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-gray-600 to-gray-800 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-utensils text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Restaurant Settings</h1>
                        <p class="text-gray-100">Configure restaurant-specific settings and preferences</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-gray-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-gray-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-200 mx-2"></i>
                                <span class="text-white font-medium">Restaurant Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white rounded-lg shadow-lg mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active" data-tab="tables">
                    <i class="fas fa-chair mr-2"></i>
                    Tables & Seating
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="service">
                    <i class="fas fa-concierge-bell mr-2"></i>
                    Service Settings
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="hours">
                    <i class="fas fa-clock mr-2"></i>
                    Operating Hours
                </button>
                <button class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="policies">
                    <i class="fas fa-file-contract mr-2"></i>
                    Policies
                </button>
            </nav>
        </div>
    </div>

    <!-- Tables & Seating Tab -->
    <div id="tables-tab" class="tab-content">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Tables & Seating Management</h3>
                        <p class="text-gray-600 mt-1">Configure restaurant tables and seating arrangements</p>
                    </div>
                    <button class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add Table
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
                    <!-- Sample Tables -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Table 1</h4>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Available</span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex justify-between">
                                <span>Capacity:</span>
                                <span>4 people</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Location:</span>
                                <span>Main Hall</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Type:</span>
                                <span>Regular</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-4">
                            <button class="text-gray-600 hover:text-gray-800 text-sm">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                            <button class="text-red-600 hover:text-red-800 text-sm">
                                <i class="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Table 2</h4>
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Occupied</span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex justify-between">
                                <span>Capacity:</span>
                                <span>2 people</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Location:</span>
                                <span>Window Side</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Type:</span>
                                <span>VIP</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-4">
                            <button class="text-gray-600 hover:text-gray-800 text-sm">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                            <button class="text-red-600 hover:text-red-800 text-sm">
                                <i class="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-medium text-gray-900">Table 3</h4>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Reserved</span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex justify-between">
                                <span>Capacity:</span>
                                <span>6 people</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Location:</span>
                                <span>Private Room</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Type:</span>
                                <span>Family</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-4">
                            <button class="text-gray-600 hover:text-gray-800 text-sm">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                            <button class="text-red-600 hover:text-red-800 text-sm">
                                <i class="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </div>

                    <!-- Add New Table Card -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center hover:border-gray-400 transition-colors duration-200 cursor-pointer">
                        <div class="text-center">
                            <i class="fas fa-plus text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-500 text-sm">Add New Table</p>
                        </div>
                    </div>
                </div>

                <!-- Table Configuration -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Table Configuration</h4>
                    <form class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <label for="default_table_capacity" class="block text-sm font-medium text-gray-700 mb-2">
                                Default Table Capacity
                            </label>
                            <input type="number" id="default_table_capacity" name="default_table_capacity" value="4" min="1" max="20"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                        </div>

                        <div>
                            <label for="table_numbering" class="block text-sm font-medium text-gray-700 mb-2">
                                Table Numbering System
                            </label>
                            <select id="table_numbering" name="table_numbering" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                                <option value="numeric">Numeric (1, 2, 3...)</option>
                                <option value="alphabetic">Alphabetic (A, B, C...)</option>
                                <option value="alphanumeric">Alphanumeric (A1, A2, B1...)</option>
                            </select>
                        </div>

                        <div>
                            <label for="reservation_duration" class="block text-sm font-medium text-gray-700 mb-2">
                                Default Reservation Duration (minutes)
                            </label>
                            <input type="number" id="reservation_duration" name="reservation_duration" value="120" min="30" max="480"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Settings Tab -->
    <div id="service-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Service Settings</h3>
                <p class="text-gray-600 mt-1">Configure service charges, tips, and customer service options</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="service_charge" class="block text-sm font-medium text-gray-700 mb-2">
                            Service Charge (%)
                        </label>
                        <input type="number" id="service_charge" name="service_charge" value="10" min="0" max="25" step="0.5"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                    </div>

                    <div>
                        <label for="tip_percentage" class="block text-sm font-medium text-gray-700 mb-2">
                            Suggested Tip Percentage (%)
                        </label>
                        <input type="number" id="tip_percentage" name="tip_percentage" value="15" min="0" max="30" step="0.5"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                    </div>

                    <div>
                        <label for="minimum_order" class="block text-sm font-medium text-gray-700 mb-2">
                            Minimum Order Amount
                        </label>
                        <input type="number" id="minimum_order" name="minimum_order" value="10" min="0" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                    </div>

                    <div>
                        <label for="delivery_charge" class="block text-sm font-medium text-gray-700 mb-2">
                            Delivery Charge
                        </label>
                        <input type="number" id="delivery_charge" name="delivery_charge" value="5" min="0" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_service_charge" name="auto_service_charge" value="1" checked
                               class="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded">
                        <label for="auto_service_charge" class="ml-2 text-sm text-gray-700">
                            Automatically apply service charge to all orders
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="allow_tip_adjustment" name="allow_tip_adjustment" value="1" checked
                               class="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded">
                        <label for="allow_tip_adjustment" class="ml-2 text-sm text-gray-700">
                            Allow customers to adjust tip amount
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="split_bill_enabled" name="split_bill_enabled" value="1"
                               class="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded">
                        <label for="split_bill_enabled" class="ml-2 text-sm text-gray-700">
                            Enable bill splitting for customers
                        </label>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Service Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Operating Hours Tab -->
    <div id="hours-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Operating Hours</h3>
                <p class="text-gray-600 mt-1">Set restaurant operating hours for each day of the week</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="space-y-4">
                    @php
                        $days = [
                            'monday' => 'Monday',
                            'tuesday' => 'Tuesday', 
                            'wednesday' => 'Wednesday',
                            'thursday' => 'Thursday',
                            'friday' => 'Friday',
                            'saturday' => 'Saturday',
                            'sunday' => 'Sunday'
                        ];
                    @endphp

                    @foreach($days as $day => $dayName)
                    <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="{{ $day }}_enabled" name="{{ $day }}_enabled" value="1" checked
                                   class="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded">
                            <label for="{{ $day }}_enabled" class="ml-2 text-sm font-medium text-gray-700 w-20">
                                {{ $dayName }}
                            </label>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <label for="{{ $day }}_open" class="text-sm text-gray-600">Open:</label>
                            <input type="time" id="{{ $day }}_open" name="{{ $day }}_open" value="09:00"
                                   class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <label for="{{ $day }}_close" class="text-sm text-gray-600">Close:</label>
                            <input type="time" id="{{ $day }}_close" name="{{ $day }}_close" value="22:00"
                                   class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="{{ $day }}_24h" name="{{ $day }}_24h" value="1"
                                   class="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded">
                            <label for="{{ $day }}_24h" class="ml-2 text-sm text-gray-600">
                                24 Hours
                            </label>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Operating Hours
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Policies Tab -->
    <div id="policies-tab" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Restaurant Policies</h3>
                <p class="text-gray-600 mt-1">Configure restaurant policies and terms</p>
            </div>

            <form class="p-6 space-y-6">
                @csrf
                
                <div class="space-y-6">
                    <div>
                        <label for="cancellation_policy" class="block text-sm font-medium text-gray-700 mb-2">
                            Cancellation Policy
                        </label>
                        <textarea id="cancellation_policy" name="cancellation_policy" rows="4" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                                  placeholder="Enter cancellation policy details...">Orders can be cancelled within 5 minutes of placement. After preparation begins, cancellation may not be possible.</textarea>
                    </div>

                    <div>
                        <label for="refund_policy" class="block text-sm font-medium text-gray-700 mb-2">
                            Refund Policy
                        </label>
                        <textarea id="refund_policy" name="refund_policy" rows="4" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                                  placeholder="Enter refund policy details...">Refunds are processed within 3-5 business days. Items must be returned in original condition.</textarea>
                    </div>

                    <div>
                        <label for="terms_conditions" class="block text-sm font-medium text-gray-700 mb-2">
                            Terms & Conditions
                        </label>
                        <textarea id="terms_conditions" name="terms_conditions" rows="6" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                                  placeholder="Enter terms and conditions...">By placing an order, customers agree to our terms of service and privacy policy.</textarea>
                    </div>
                </div>

                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i>
                        Save Policies
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Tab switching
    $('.settings-tab').on('click', function() {
        const tabName = $(this).data('tab');
        
        // Update tab appearance
        $('.settings-tab').removeClass('border-gray-500 text-gray-600').addClass('border-transparent text-gray-500');
        $(this).removeClass('border-transparent text-gray-500').addClass('border-gray-500 text-gray-600');
        
        // Show/hide tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}-tab`).removeClass('hidden');
    });

    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Add loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        // Simulate API call
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Settings saved successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });

    // 24-hour checkbox handlers
    $('input[id$="_24h"]').on('change', function() {
        const day = $(this).attr('id').replace('_24h', '');
        const openInput = $(`#${day}_open`);
        const closeInput = $(`#${day}_close`);
        
        if ($(this).is(':checked')) {
            openInput.val('00:00').prop('disabled', true);
            closeInput.val('23:59').prop('disabled', true);
        } else {
            openInput.prop('disabled', false);
            closeInput.prop('disabled', false);
        }
    });

    // Day enabled/disabled handlers
    $('input[id$="_enabled"]').on('change', function() {
        const day = $(this).attr('id').replace('_enabled', '');
        const openInput = $(`#${day}_open`);
        const closeInput = $(`#${day}_close`);
        const twentyFourInput = $(`#${day}_24h`);
        
        if ($(this).is(':checked')) {
            openInput.prop('disabled', false);
            closeInput.prop('disabled', false);
            twentyFourInput.prop('disabled', false);
        } else {
            openInput.prop('disabled', true);
            closeInput.prop('disabled', true);
            twentyFourInput.prop('disabled', true);
        }
    });
});
</script>
@endpush
