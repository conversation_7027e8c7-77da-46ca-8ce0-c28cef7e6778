<?php

namespace Modules\Reservation\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Table;
use Modules\Reservation\Services\TableService;

class TableController extends Controller
{
    protected TableService $tableService;

    public function __construct(TableService $tableService)
    {
        $this->tableService = $tableService;
    }

    /**
     * Display the tables index page.
     */
    public function index(Request $request)
    {
        // If it's an AJAX request, return JSON data for dropdowns
        if ($request->ajax() || $request->expectsJson()) {
            return $this->dropdown();
        }
        
        return view('Reservation::tables');
    }

    /**
     * Get tables data for DataTables.
     */
    public function dataTable(Request $request)
    {
        if ($request->ajax()) {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'error' => 'User is not assigned to any branch'
                ], 400);
            }

            $query = Table::with(['area'])
                ->where('branch_id', $user->branch_id);

            // Apply filters
            if ($request->filled('area_id')) {
                $query->where('area_id', $request->area_id);
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($table) {
                    $actions = '<div class="btn-group" role="group">';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-info" onclick="showTable(' . $table->id . ')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="editTable(' . $table->id . ')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>';
                    
                    $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteTable(' . $table->id . ')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>';
                    
                    $actions .= '</div>';
                    return $actions;
                })
                ->addColumn('status_badge', function ($table) {
                    $statusColors = [
                        'available' => 'success',
                        'occupied' => 'danger',
                        'reserved' => 'warning',
                        'maintenance' => 'secondary'
                    ];
                    
                    $color = $statusColors[$table->status] ?? 'secondary';
                    return '<span class="badge badge-' . $color . '">' . ucfirst($table->status) . '</span>';
                })
                ->addColumn('area_name', function ($table) {
                    return $table->area ? $table->area->name : 'N/A';
                })
                ->rawColumns(['action', 'status_badge'])
                ->make(true);
        }

        return view('Reservation::tables');
    }


    /**
     * Store a new table.
     */
    public function store(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id || !$user->tenant_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch or tenant'
                ], 400);
            }

            $request->validate([
                'number' => 'required|string|max:255',
                'capacity' => 'required|integer|min:1',
                'area_id' => 'required|exists:areas,id',
                'status' => 'in:available,occupied,reserved,maintenance',
                'description' => 'nullable|string'
            ]);

            $data = $request->all();
            $data['tenant_id'] = $user->tenant_id;
            $data['branch_id'] = $user->branch_id;
            
            $table = $this->tableService->createTable($data);

            return response()->json([
                'success' => true,
                'data' => $table,
                'message' => 'Table created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create table',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Show a specific table.
     */
    public function show($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $table = $this->tableService->getTableByIdForBranch((int)$id, $user->branch_id);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $table,
                'message' => 'Table retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve table',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a specific table.
     */
    public function update(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $table = $this->tableService->updateTableForBranch((int)$id, $request->all(), $user->branch_id);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $table,
                'message' => 'Table updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update table',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Delete a specific table.
     */
    public function destroy($id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $deleted = $this->tableService->deleteTableForBranch((int)$id, $user->branch_id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Table deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete table',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get tables dropdown data.
     */
    public function dropdown()
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $tables = Table::with('area')
                ->where('branch_id', $user->branch_id)
                ->select('id', 'table_name', 'table_number', 'number', 'capacity', 'area_id')
                ->orderBy('table_number')
                ->get()
                ->map(function($table) {
                    return [
                        'id' => $table->id,
                        'table_name' => $table->table_name ?: ('طاولة ' . ($table->table_number ?: $table->number)),
                        'table_number' => $table->table_number ?: $table->number,
                        'capacity' => $table->capacity,
                        'area' => $table->area ? [
                            'id' => $table->area->id,
                            'name' => $table->area->name
                        ] : null
                    ];
                });

            return response()->json($tables);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tables',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export tables.
     */
    public function export(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // For now, just return a simple response
            return response()->json([
                'success' => true,
                'message' => 'Export functionality will be implemented soon'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export tables',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get table reports.
     */
    public function tableReports(Request $request)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // For now, just return basic statistics
            return response()->json([
                'success' => true,
                'message' => 'Table reports functionality will be implemented soon'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate table reports',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update table status.
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $request->validate([
                'status' => 'required|in:available,occupied,reserved,maintenance'
            ]);

            $table = Table::where('id', $id)
                ->where('branch_id', $user->branch_id)
                ->first();

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            $table->status = $request->status;
            $table->save();

            return response()->json([
                'success' => true,
                'data' => $table,
                'message' => 'Table status updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update table status',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get tables by area.
     */
    public function getTablesByArea($areaId)
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $tables = Table::where('area_id', $areaId)
                ->where('branch_id', $user->branch_id)
                ->select('id', 'number', 'capacity', 'status')
                ->orderBy('number')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $tables
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tables for area',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}