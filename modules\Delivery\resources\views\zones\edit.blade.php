@extends('layouts.master')

@section('content')
<!-- <PERSON> Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900">إدارة مناطق التوصيل</h1>
            <p class="text-sm text-gray-600 mt-1">تعديل منطقة: {{ $zone->name }}</p>
        </div>
        <div class="flex space-x-2">
            <a href="{{ route('delivery.zones.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Main Content Card -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">تعديل منطقة التوصيل</h2>
        <p class="text-sm text-gray-600 mt-1">قم بتعديل البيانات التالية لمنطقة التوصيل</p>
    </div>
    <div class="p-6">

        <form action="{{ route('delivery.zones.update', $zone->id) }}" method="POST" id="zoneForm">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم المنطقة <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                               id="name" name="name" value="{{ old('name', $zone->name) }}" required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="branch_id" class="block text-sm font-medium text-gray-700 mb-2">الفرع <span class="text-red-500">*</span></label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('branch_id') border-red-500 @enderror"
                                id="branch_id" name="branch_id" required>
                            <option value="">اختر الفرع</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}" {{ old('branch_id', $zone->branch_id) == $branch->id ? 'selected' : '' }}>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('branch_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror"
                                  id="description" name="description" rows="3">{{ old('description', $zone->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Delivery Settings -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">إعدادات التوصيل</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="delivery_fee" class="block text-sm font-medium text-gray-700 mb-2">رسوم التوصيل (ر.س) <span class="text-red-500">*</span></label>
                        <input type="number" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('delivery_fee') border-red-500 @enderror"
                               id="delivery_fee" name="delivery_fee" value="{{ old('delivery_fee', $zone->delivery_fee) }}" required>
                        @error('delivery_fee')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="minimum_order_amount" class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى للطلب (ر.س) <span class="text-red-500">*</span></label>
                        <input type="number" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('minimum_order_amount') border-red-500 @enderror"
                               id="minimum_order_amount" name="minimum_order_amount" value="{{ old('minimum_order_amount', $zone->minimum_order_amount) }}" required>
                        @error('minimum_order_amount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="estimated_delivery_time_minutes" class="block text-sm font-medium text-gray-700 mb-2">وقت التوصيل المتوقع (دقيقة) <span class="text-red-500">*</span></label>
                        <input type="number" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('estimated_delivery_time_minutes') border-red-500 @enderror"
                               id="estimated_delivery_time_minutes" name="estimated_delivery_time_minutes" value="{{ old('estimated_delivery_time_minutes', $zone->estimated_delivery_time_minutes) }}" required>
                        @error('estimated_delivery_time_minutes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">الأولوية <span class="text-red-500">*</span></label>
                        <input type="number" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('priority') border-red-500 @enderror"
                               id="priority" name="priority" value="{{ old('priority', $zone->priority) }}" required>
                        @error('priority')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">الأولوية الأعلى تعني فحص المنطقة أولاً</p>
                    </div>

                    <div>
                        <label for="is_active" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                id="is_active" name="is_active">
                            <option value="1" {{ old('is_active', $zone->is_active) == 1 ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ old('is_active', $zone->is_active) == 0 ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Map Section -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">تحديد المنطقة على الخريطة</h3>
                <div class="border border-gray-300 rounded-md">
                    <div id="map" style="height: 400px; width: 100%;"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2">انقر على الخريطة لتحديد نقاط المنطقة. يجب تحديد 3 نقاط على الأقل لتكوين منطقة.</p>
                
                <!-- Hidden input for coordinates -->
                <input type="hidden" id="coordinates" name="coordinates" value="{{ old('coordinates', json_encode($zone->coordinates)) }}">
                @error('coordinates')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Coordinates List -->
            <div class="mb-8" id="coordinatesList" style="display: none;">
                <h4 class="text-md font-medium text-gray-900 mb-2">النقاط المحددة:</h4>
                <div class="bg-gray-50 p-4 rounded-md">
                    <ul id="pointsList" class="space-y-2"></ul>
                    <button type="button" id="clearPoints" class="mt-3 text-sm text-red-600 hover:text-red-800">
                        <i class="fas fa-trash mr-1"></i>
                        مسح جميع النقاط
                    </button>
                </div>
            </div>

            <!-- Form Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('delivery.zones.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    إلغاء
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.maps_api_key', 'YOUR_API_KEY') }}&libraries=geometry"></script>
<script>
let map;
let polygon;
let coordinates = [];

$(document).ready(function() {
    // Load existing coordinates
    const existingCoordinates = $('#coordinates').val();
    if (existingCoordinates) {
        try {
            coordinates = JSON.parse(existingCoordinates);
        } catch (e) {
            coordinates = [];
        }
    }

    initMap();

    // Form validation
    $('#zoneForm').on('submit', function(e) {
        if (coordinates.length < 3) {
            e.preventDefault();
            alert('يجب تحديد 3 نقاط على الأقل لتكوين منطقة التوصيل');
            return false;
        }
        
        // Update hidden input with coordinates
        $('#coordinates').val(JSON.stringify(coordinates));
    });

    // Clear points button
    $('#clearPoints').on('click', function() {
        clearAllPoints();
    });
});

function initMap() {
    // Default to Riyadh, Saudi Arabia or center of existing coordinates
    let defaultCenter = { lat: 24.7136, lng: 46.6753 };
    
    if (coordinates.length > 0) {
        // Calculate center of existing coordinates
        const latSum = coordinates.reduce((sum, coord) => sum + coord.latitude, 0);
        const lngSum = coordinates.reduce((sum, coord) => sum + coord.longitude, 0);
        defaultCenter = {
            lat: latSum / coordinates.length,
            lng: lngSum / coordinates.length
        };
    }
    
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 12,
        center: defaultCenter,
        mapTypeId: 'roadmap'
    });

    // Initialize polygon
    polygon = new google.maps.Polygon({
        paths: [],
        strokeColor: '#FF0000',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#FF0000',
        fillOpacity: 0.35,
        editable: true,
        draggable: false
    });
    
    polygon.setMap(map);

    // Load existing coordinates into polygon
    if (coordinates.length > 0) {
        const path = polygon.getPath();
        coordinates.forEach(coord => {
            path.push(new google.maps.LatLng(coord.latitude, coord.longitude));
        });
        updatePointsList();
    }

    // Add click listener to map
    map.addListener('click', function(event) {
        addPoint(event.latLng);
    });

    // Add listener for polygon path changes
    polygon.getPath().addListener('set_at', updateCoordinatesFromPolygon);
    polygon.getPath().addListener('insert_at', updateCoordinatesFromPolygon);
    polygon.getPath().addListener('remove_at', updateCoordinatesFromPolygon);
}

function addPoint(latLng) {
    const point = {
        latitude: latLng.lat(),
        longitude: latLng.lng()
    };
    
    coordinates.push(point);
    
    // Update polygon
    const path = polygon.getPath();
    path.push(latLng);
    
    updatePointsList();
    updateCoordinatesInput();
}

function updateCoordinatesFromPolygon() {
    coordinates = [];
    const path = polygon.getPath();
    
    for (let i = 0; i < path.getLength(); i++) {
        const point = path.getAt(i);
        coordinates.push({
            latitude: point.lat(),
            longitude: point.lng()
        });
    }
    
    updatePointsList();
    updateCoordinatesInput();
}

function updatePointsList() {
    const pointsList = $('#pointsList');
    pointsList.empty();
    
    if (coordinates.length > 0) {
        $('#coordinatesList').show();
        
        coordinates.forEach((point, index) => {
            pointsList.append(`
                <li class="flex justify-between items-center text-sm">
                    <span>النقطة ${index + 1}: ${point.latitude.toFixed(6)}, ${point.longitude.toFixed(6)}</span>
                    <button type="button" class="text-red-600 hover:text-red-800" onclick="removePoint(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </li>
            `);
        });
    } else {
        $('#coordinatesList').hide();
    }
}

function updateCoordinatesInput() {
    $('#coordinates').val(JSON.stringify(coordinates));
}

function removePoint(index) {
    coordinates.splice(index, 1);
    
    // Update polygon
    const path = polygon.getPath();
    path.removeAt(index);
    
    updatePointsList();
    updateCoordinatesInput();
}

function clearAllPoints() {
    coordinates = [];
    polygon.getPath().clear();
    updatePointsList();
    updateCoordinatesInput();
}
</script>
@endpush