@extends('layouts.master')

@section('title', 'تفاصيل الدور')

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <i class="fas fa-user-shield text-blue-600"></i>
                        تفاصيل الدور: {{ $role->name }}
                    </h1>
                    <div class="flex space-x-2">
                        <a href="{{ route('roles.edit', $role) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            تعديل
                        </a>
                        <a href="{{ route('roles.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-arrow-right mr-2"></i>
                            العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-shield text-blue-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">اسم الدور</p>
                        <p class="text-lg font-bold text-gray-900">{{ $role->name }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shield-alt text-purple-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">نوع الحارس</p>
                        <p class="text-lg font-bold text-gray-900">
                            @if($role->guard_name === 'web')
                                ويب
                            @elseif($role->guard_name === 'api')
                                API
                            @elseif($role->guard_name === 'sanctum')
                                Sanctum
                            @else
                                {{ $role->guard_name }}
                            @endif
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar text-green-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">تاريخ الإنشاء</p>
                        <p class="text-lg font-bold text-gray-900">{{ $role->created_at->format('Y-m-d') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">عدد الصلاحيات</p>
                        <p class="text-3xl font-bold text-blue-600">{{ $role->permissions->count() }}</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-key text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="showPermissions()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        عرض جميع الصلاحيات <i class="fas fa-arrow-left mr-1"></i>
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">عدد المستخدمين</p>
                        <p class="text-3xl font-bold text-green-600">{{ $role->users->count() }}</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="showUsers()" class="text-green-600 hover:text-green-800 text-sm font-medium">
                        عرض جميع المستخدمين <i class="fas fa-arrow-left mr-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button class="tab-button active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-tab="permissions">
                        الصلاحيات ({{ $role->permissions->count() }})
                    </button>
                    <button class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="users">
                        المستخدمين ({{ $role->users->count() }})
                    </button>
                </nav>
            </div>

            <!-- Permissions Tab -->
            <div id="permissions" class="tab-content active p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">صلاحيات الدور</h3>
                    <button onclick="managePermissions({{ $role->id }})" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-cog mr-2"></i>
                        إدارة الصلاحيات
                    </button>
                </div>

                @if($role->permissions->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($role->permissions->groupBy(function($permission) { return explode('.', $permission->name)[0] ?? 'other'; }) as $group => $permissions)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-3 capitalize">{{ $group }}</h4>
                                <div class="space-y-2">
                                    @foreach($permissions as $permission)
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-gray-600">{{ $permission->name }}</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                @if($permission->guard_name === 'web') bg-indigo-100 text-indigo-800
                                                @elseif($permission->guard_name === 'api') bg-purple-100 text-purple-800
                                                @elseif($permission->guard_name === 'sanctum') bg-yellow-100 text-yellow-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                                {{ $permission->guard_name }}
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-key text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">لا توجد صلاحيات مخصصة لهذا الدور</p>
                        <button onclick="managePermissions({{ $role->id }})" class="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            إضافة صلاحيات
                        </button>
                    </div>
                @endif
            </div>

            <!-- Users Tab -->
            <div id="users" class="tab-content p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">المستخدمين المخصص لهم هذا الدور</h3>
                </div>

                @if($role->users->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-right text-gray-500">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3">الاسم</th>
                                    <th class="px-6 py-3">البريد الإلكتروني</th>
                                    <th class="px-6 py-3">المنصب</th>
                                    <th class="px-6 py-3">الحالة</th>
                                    <th class="px-6 py-3">تاريخ الإنشاء</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($role->users as $user)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-user text-blue-600"></i>
                                                </div>
                                                <div class="mr-3">
                                                    <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $user->email }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $user->position ?? '-' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($user->is_active)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    نشط
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    غير نشط
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $user->created_at->format('Y-m-d') }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">لا يوجد مستخدمين مخصص لهم هذا الدور</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showPermissions() {
    $('.tab-button').removeClass('active');
    $('.tab-content').removeClass('active');
    $('[data-tab="permissions"]').addClass('active');
    $('#permissions').addClass('active');
}

function showUsers() {
    $('.tab-button').removeClass('active');
    $('.tab-content').removeClass('active');
    $('[data-tab="users"]').addClass('active');
    $('#users').addClass('active');
}

function managePermissions(roleId) {
    window.location.href = `/admin/roles/${roleId}/permissions`;
}
</script>
@endpush